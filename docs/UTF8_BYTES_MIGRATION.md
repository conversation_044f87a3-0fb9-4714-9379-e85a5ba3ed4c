# UTF-8 字节计量系统迁移文档

## 🎯 迁移目标

将 SoulVoice 的用量统计系统从字符计量改为 UTF-8 字节计量，提供更准确的资源消耗统计。

## 📊 主要变更

### 1. 用量计算单位变更
- **修改前**: 按字符数计量 (characters)
- **修改后**: 按 UTF-8 字节数计量 (bytes)
- **新用户默认额度**: 10,000 UTF-8 字节

### 2. 修改的文件和功能

#### 🔧 核心服务层 (`src/services/usageService.ts`)
- **recordUsage()**: 参数从 `charactersUsed` 改为 `bytesUsed`
- **calculateCost()**: 费率注释更新为"每字节"
- **getUserUsageStats()**: 返回值从 `totalCharacters` 改为 `totalBytes`
- **getCurrentMonthUsage()**: 返回值从 `totalCharacters` 改为 `totalBytes`
- **groupUsageByDate()**: 添加注释说明字段语义变更
- **groupUsageByService()**: 添加注释说明字段语义变更

#### 📱 用户界面层
**Usage 页面 (`src/pages/Usage.tsx`)**:
- 用量显示单位: "字符" → "UTF-8 字节"
- 图表数据字段: `characters` → `bytes`
- API 调用记录: 显示字节数而非字符数
- 功能分类统计: 添加"字节"单位

**Dashboard 页面 (`src/pages/Dashboard.tsx`)**:
- 本月用量显示: "字符" → "UTF-8 字节"
- 剩余额度显示: "字符" → "UTF-8 字节"

**VoiceLab 页面 (`src/pages/VoiceLab.tsx`)**:
- 预计消耗显示: 价格 → UTF-8 字节数
- 用量记录: 使用 `TextEncoder` 计算实际字节数

### 3. 技术实现细节

#### UTF-8 字节计算
```typescript
// 使用 TextEncoder API 准确计算 UTF-8 字节数
const bytesUsed = new TextEncoder().encode(text).length;
```

#### 数据库兼容性
- **字段名保持不变**: `characters_used` 字段继续使用，但语义改为存储字节数
- **向后兼容**: 现有数据结构不变，只是数据含义更新
- **注释标注**: 在代码中添加注释说明字段现在存储字节数

### 4. 用户体验改进

#### 更准确的计量
- **中文字符**: 3 字节 (UTF-8)
- **英文字符**: 1 字节 (UTF-8)
- **标点符号**: 1-3 字节 (根据字符类型)

#### 实时显示
- 文本输入时实时显示 UTF-8 字节数
- 移除价格显示，专注于资源消耗

## 🔄 迁移影响

### 对现有用户的影响
1. **显示单位变更**: 界面上所有"字符"显示改为"UTF-8 字节"
2. **计量更准确**: 中文内容的字节数会比字符数更大
3. **额度保持**: 现有用户的额度数值保持不变，但单位语义更新

### 对新用户的影响
1. **默认额度**: 10,000 UTF-8 字节
2. **统一计量**: 所有功能使用相同的字节计量标准

## 📈 技术优势

1. **国际化标准**: UTF-8 是国际标准编码
2. **精确计量**: 反映真实的存储和传输成本
3. **API 一致性**: 与大多数云服务 API 的计费方式一致
4. **多语言支持**: 准确处理各种语言的字符

## 🚀 部署说明

### 无需数据库迁移
- 数据库表结构无需修改
- 现有数据继续有效
- 只需部署新的应用代码

### 测试要点
1. 验证 UTF-8 字节计算准确性
2. 确认用量统计显示正确
3. 测试多语言文本的字节计算
4. 验证图表和统计数据正确性

## 📝 后续优化建议

1. **数据库字段重命名**: 考虑在未来版本中将 `characters_used` 重命名为 `bytes_used`
2. **历史数据处理**: 可选择性地转换历史数据的计量单位
3. **用户通知**: 考虑在界面上添加说明，告知用户计量单位的变更
4. **监控指标**: 更新监控和分析系统以使用新的字节计量

---

**迁移完成时间**: 2025-01-31  
**影响范围**: 用量统计、计费显示、资源监控  
**兼容性**: 向后兼容，无破坏性变更
