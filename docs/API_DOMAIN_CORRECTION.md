# API 域名修正说明

## 🔧 修正内容

已将语音合成 API 的域名从错误的地址修正为正确的 SiliconFlow API 域名。

### 修正前
```
https://api.tikhub.io/v1/audio/speech
```

### 修正后
```
https://api.siliconflow.cn/v1/audio/speech
```

## 📁 修改的文件

### 1. 核心服务文件
**文件**: `src/services/ttsService.ts`
- **修改行**: 第 37 行
- **修改内容**: 更新 `API_URL` 常量

```typescript
// 修正前
private static readonly API_URL = 'https://api.tikhub.io/v1/audio/speech';

// 修正后
private static readonly API_URL = 'https://api.siliconflow.cn/v1/audio/speech';
```

### 2. 文档文件
**文件**: `docs/TTS_API_Usage.md`
- **修改内容**: 更新概述部分，将 "TikHub API" 改为 "SiliconFlow API"

**文件**: `IMPLEMENTATION_SUMMARY.md`
- **修改内容**: 
  - 更新 API 接口规范中的 curl 示例
  - 在功能特性中添加正确的 API 域名说明

## ✅ 验证结果

- **编译通过**: 项目成功构建，无错误
- **功能完整**: API 调用逻辑保持不变
- **文档同步**: 所有相关文档已同步更新

## 🔗 正确的 API 调用示例

```bash
curl --location --request POST 'https://api.siliconflow.cn/v1/audio/speech' \
--header 'Authorization: Bearer sk-ynabzdlcumjklweexfyruujoydkzfnqctcnlsiifbloqgdcw' \
--header 'Content-Type: application/json' \
--data-raw '{
  "model": "fnlp/MOSS-TTSD-v0.5",
  "input": "里特并非传统意义上的特工",
  "voice": "speech:xzzc01:clzkyf4vy00e5qr6hywum4u84:fobsoavejzngfngubrdd"
}'
```

## 📝 注意事项

1. **API 密钥**: 保持不变，仍使用原有的 API 密钥
2. **请求格式**: 请求参数和格式完全相同
3. **响应处理**: 响应处理逻辑无需修改
4. **功能影响**: 此修正不影响任何现有功能

## 🚀 后续操作

域名修正完成后，建议：

1. **测试验证**: 运行 `npm run dev` 启动开发服务器进行功能测试
2. **API 测试**: 在 VoiceLab 页面测试语音合成功能
3. **错误监控**: 观察控制台日志，确认 API 调用正常

---

**修正完成**: API 域名已成功修正为正确的 SiliconFlow 域名，所有相关文档已同步更新。
