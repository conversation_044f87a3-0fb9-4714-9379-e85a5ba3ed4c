# API 401 错误排查指南

## 🚨 问题描述

用户配置了 SiliconFlow API 密钥后，仍然遇到 "API 请求失败: 401 - API 密钥无效" 错误。

## 🔍 可能的原因

### 1. API 密钥格式问题
- API 密钥格式不正确
- 密钥中包含额外的空格或字符
- 密钥被截断或不完整

### 2. 环境变量配置问题
- `.env` 文件配置错误
- 环境变量名称错误
- 开发服务器未重启

### 3. API 密钥权限问题
- API 密钥已过期
- API 密钥权限不足
- SiliconFlow 账户余额不足

### 4. 网络和代理问题
- 网络连接问题
- 代理配置影响
- DNS 解析问题

## 🔧 排查步骤

### 步骤 1: 验证 API 密钥格式

#### 检查密钥格式
```bash
# 正确的格式应该是：
sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

#### 验证密钥长度
- SiliconFlow API 密钥通常以 `sk-` 开头
- 总长度通常为 48-51 个字符

### 步骤 2: 检查环境变量配置

#### 验证 `.env` 文件
```bash
# 确保 .env 文件中的配置正确
VITE_SILICONFLOW_API_KEY=sk-your-actual-api-key-here
```

#### 检查环境变量是否生效
1. 打开浏览器开发者工具
2. 在控制台中运行：
```javascript
console.log('API Key:', import.meta.env.VITE_SILICONFLOW_API_KEY);
```

### 步骤 3: 测试 API 密钥有效性

#### 使用 curl 测试
```bash
# 配置代理（如果需要）
export http_proxy="http://127.0.0.1:7890"
export https_proxy="http://127.0.0.1:7890"

# 测试 API 调用
curl -X POST https://api.siliconflow.cn/v1/audio/speech \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "FunAudioLLM/CosyVoice2-0.5B",
    "input": "测试",
    "voice": "speech:mcg3fdnx:clzkyf4vy00e5qr6hywum4u84:bzznlkuhcjzpbosexitr"
  }' \
  --output test-audio.mp3
```

#### 检查响应
- **成功**: 生成音频文件
- **401 错误**: API 密钥无效
- **403 错误**: 权限不足或余额不足
- **404 错误**: 端点不存在

### 步骤 4: 检查 SiliconFlow 账户状态

#### 登录 SiliconFlow 控制台
1. 访问 [SiliconFlow 控制台](https://siliconflow.cn)
2. 检查账户余额
3. 验证 API 密钥状态
4. 查看 API 调用日志

#### 检查 API 密钥权限
- 确认密钥有语音合成权限
- 检查调用频率限制
- 验证密钥是否被禁用

## 🛠️ 解决方案

### 方案 1: 重新生成 API 密钥

1. **删除旧密钥**
   - 在 SiliconFlow 控制台中删除当前密钥

2. **生成新密钥**
   - 创建新的 API 密钥
   - 确保权限设置正确

3. **更新配置**
   ```bash
   # 更新 .env 文件
   VITE_SILICONFLOW_API_KEY=sk-new-api-key-here
   ```

4. **重启服务**
   ```bash
   npm run dev
   ```

### 方案 2: 检查网络配置

#### 配置代理（如果需要）
```bash
# 在终端中设置代理
export http_proxy="http://127.0.0.1:7890"
export https_proxy="http://127.0.0.1:7890"

# 重启开发服务器
npm run dev
```

#### 测试网络连接
```bash
# 测试 SiliconFlow API 连通性
curl -I https://api.siliconflow.cn/v1/audio/speech
```

### 方案 3: 使用备用调用方式

如果直接调用仍有问题，可以通过 Supabase Functions：

#### 配置 Supabase 环境变量
1. 登录 Supabase 控制台
2. 进入项目设置 → Environment Variables
3. 添加：`SILICONFLOW_API_KEY=your-api-key`
4. 重新部署 Functions

#### 修改前端配置
```bash
# 在 .env 中移除前端密钥，强制使用 Supabase Functions
# VITE_SILICONFLOW_API_KEY=
```

## 🧪 测试验证

### 1. 环境变量测试
使用提供的测试页面验证环境变量：
```bash
open test-api-key.html
```

### 2. API 调用测试
在音色实验室中：
1. 选择音色
2. 输入短文本（如："测试"）
3. 点击生成音频
4. 查看浏览器控制台的错误信息

### 3. 网络测试
```bash
# 测试网络连接
ping api.siliconflow.cn

# 测试 HTTPS 连接
curl -I https://api.siliconflow.cn
```

## 📋 常见错误代码

| 错误代码 | 含义 | 解决方案 |
|---------|------|----------|
| 401 | API 密钥无效 | 检查密钥格式和有效性 |
| 403 | 权限不足 | 检查账户余额和权限 |
| 404 | 端点不存在 | 检查 API URL 是否正确 |
| 429 | 请求频率过高 | 降低请求频率或升级套餐 |
| 500 | 服务器错误 | 稍后重试或联系技术支持 |

## 🔄 完整排查流程

1. **验证密钥格式** → 检查是否以 `sk-` 开头
2. **测试环境变量** → 确认前端能读取到密钥
3. **测试网络连接** → 确认能访问 SiliconFlow API
4. **验证密钥有效性** → 使用 curl 直接测试
5. **检查账户状态** → 确认余额和权限
6. **重新生成密钥** → 如果以上都正常，重新生成密钥

## 📞 获取帮助

如果问题仍然存在：

1. **收集错误信息**
   - 浏览器控制台的完整错误信息
   - 网络请求的详细信息
   - API 密钥的前8位字符

2. **联系技术支持**
   - SiliconFlow 官方技术支持
   - 项目维护者

---

**更新日期**: 2025-01-31  
**版本**: v1.0.0
