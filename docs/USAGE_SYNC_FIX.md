# 用量统计数据同步问题修复

## 问题描述

用户在声音实验室生成语音后，发现：
1. **Dashboard 页面**的用量没有变化
2. **Usage 用量统计页面**显示了正确的使用量（5000多字节）
3. **总额度显示**仍然是 10000 字节，没有反映订阅升级

## 问题根因分析

### 数据流问题
项目中存在两套并行的用量记录系统：

1. **`usage_records` 表**：
   - 由 `UsageService.recordUsage()` 记录
   - 声音实验室使用此系统记录用量
   - Usage 页面显示此数据

2. **`user_balances` 表**：
   - 新的订阅系统引入的用户余额表
   - Dashboard 页面显示此数据
   - 包含订阅等级和总配额信息

### 数据不同步
- 声音实验室只更新了 `usage_records`
- 没有同步更新 `user_balances` 的 `used_bytes`
- 导致两个页面显示不同的数据

## 解决方案

### 1. 数据迁移
```sql
-- 为现有用户创建用户余额记录
INSERT INTO user_balances (user_id, total_bytes, used_bytes, free_bytes, subscription_level)
SELECT 
  p.id as user_id,
  10000 as total_bytes,
  COALESCE((SELECT SUM(characters_used) FROM usage_records WHERE user_id = p.id), 0) as used_bytes,
  10000 as free_bytes,
  'free' as subscription_level
FROM profiles p
WHERE p.id NOT IN (SELECT user_id FROM user_balances);

-- 同步现有数据
UPDATE user_balances 
SET used_bytes = COALESCE((
  SELECT SUM(characters_used) 
  FROM usage_records 
  WHERE user_id = user_balances.user_id
), 0);
```

### 2. 声音实验室双重记录
修改 `src/pages/VoiceLab.tsx`，在语音生成成功后：

```typescript
// 记录到 usage_records 表（保持兼容性）
await UsageService.recordUsage(user.id, 'tts', bytesUsed);

// 同时更新用户余额
await SubscriptionService.consumeUserBytes(user.id, bytesUsed);
```

### 3. Dashboard 数据源优化
修改 `src/pages/Dashboard.tsx`，优先使用 `user_balances` 数据：

```typescript
// 优先使用用户余额数据
const balance = await SubscriptionService.getUserBalance(user!.id);
if (balance) {
  setUsage({ used: balance.used_bytes, total: balance.total_bytes });
}
```

### 4. Usage 页面总额度修正
修改 `src/pages/Usage.tsx`，从 `user_balances` 获取正确的总额度：

```typescript
// 获取用户余额信息以获取正确的总额度
const balance = await SubscriptionService.getUserBalance(user!.id);
const totalBytes = balance ? balance.total_bytes : 10000;
```

## 修复后的数据流

```
声音实验室生成语音
    ↓
1. 记录到 usage_records 表 (UsageService.recordUsage)
    ↓
2. 更新 user_balances 表 (SubscriptionService.consumeUserBytes)
    ↓
3. Dashboard 显示 user_balances 数据
    ↓
4. Usage 页面显示 usage_records 统计 + user_balances 总额度
```

## 验证方法

### 1. 访问调试页面
```
http://localhost:5173/usage-debug
```

这个页面会显示：
- `usage_records` 表的统计数据
- `user_balances` 表的当前状态
- 数据同步状态对比

### 2. 测试流程
1. 在声音实验室生成一段语音
2. 检查 Dashboard 用量是否更新
3. 检查 Usage 页面数据是否正确
4. 访问调试页面验证数据同步

### 3. 预期结果
- Dashboard 和 Usage 页面显示一致的使用量
- 总额度正确反映用户的订阅等级
- 数据实时同步更新

## 技术细节

### 数据表结构

**usage_records 表**：
- `characters_used`: 实际存储字节数（历史原因字段名未改）
- `service_type`: 服务类型 (tts/clone)
- `cost`: 费用计算

**user_balances 表**：
- `total_bytes`: 总字节配额（免费 + 订阅）
- `used_bytes`: 已使用字节数
- `subscription_level`: 订阅等级

### 关键服务方法

**SubscriptionService.consumeUserBytes()**：
- 检查余额是否足够
- 原子性更新 `used_bytes`
- 抛出余额不足异常

**SubscriptionService.getUserBalance()**：
- 获取用户余额信息
- 自动创建缺失的记录

## 后续优化建议

1. **统一数据源**：
   - 考虑将 `usage_records` 作为详细记录
   - `user_balances` 作为汇总数据
   - 定期同步确保一致性

2. **实时更新**：
   - 使用 Supabase Realtime 实现实时数据更新
   - 避免页面刷新才能看到最新数据

3. **错误处理**：
   - 余额不足时的用户友好提示
   - 数据同步失败的恢复机制

4. **性能优化**：
   - 缓存用户余额信息
   - 批量更新减少数据库调用

## 测试检查清单

- [ ] 声音实验室生成语音后 Dashboard 用量更新
- [ ] Usage 页面显示正确的总额度
- [ ] 订阅升级后总额度正确增加
- [ ] 余额不足时显示正确提示
- [ ] 调试页面显示数据同步状态
- [ ] 多次生成语音数据累计正确

通过这些修复，用量统计系统现在能够正确同步和显示用户的使用情况，为订阅系统提供准确的数据基础。
