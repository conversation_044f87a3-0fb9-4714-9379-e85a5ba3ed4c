# 🚀 SoulVoice Vercel 部署指南

## 📋 部署概述

本指南将帮助您将 SoulVoice 项目部署到 Vercel 平台，实现快速、可靠的生产环境部署。

## 🛠️ 前置要求

### 必需工具
- **Node.js** >= 18.0.0
- **npm** >= 8.0.0
- **Git** 版本控制
- **Vercel CLI** (可选，脚本会自动安装)

### 必需服务
- **Supabase** 项目 (已创建)
- **GitHub** 仓库 (用于代码托管)
- **Vercel** 账户

## 🚀 快速部署

### 方法一：使用部署脚本 (推荐)

```bash
# 1. 运行部署脚本
./scripts/deploy-vercel.sh

# 2. 部署到生产环境
./scripts/deploy-vercel.sh --prod
```

### 方法二：手动部署

#### 1. 准备环境变量

```bash
# 复制环境变量模板
cp .env.example .env.local

# 编辑环境变量文件
nano .env.local
```

#### 2. 构建项目

```bash
# 安装依赖
npm install

# 构建项目
npm run build
```

#### 3. 部署到 Vercel

```bash
# 安装 Vercel CLI
npm install -g vercel

# 登录 Vercel
vercel login

# 部署项目
vercel

# 部署到生产环境
vercel --prod
```

## ⚙️ 环境变量配置

### 在 Vercel Dashboard 中配置

1. 访问 [Vercel Dashboard](https://vercel.com/dashboard)
2. 选择您的项目
3. 进入 **Settings** → **Environment Variables**
4. 添加以下必需的环境变量：

| 变量名 | 描述 | 示例值 |
|--------|------|--------|
| `vite_supabase_url` | Supabase 项目 URL | `https://xxx.supabase.co` |
| `vite_supabase_anon_key` | Supabase 匿名密钥 | `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` |
| `vite_api_base_url` | API 基础 URL | `https://xxx.supabase.co/functions/v1` |
| `vite_siliconflow_api_key` | SiliconFlow API 密钥 | `sk-xxxxxxxxxxxxxxxx` |

### 可选环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `vite_wechat_pay_merchant_id` | 微信支付商户号 | - |
| `vite_wechat_pay_app_id` | 微信支付应用 ID | - |
| `vite_debug_mode` | 调试模式 | `false` |

## 🔧 配置文件说明

### vercel.json

```json
{
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "framework": null,
  "rewrites": [
    {
      "source": "/((?!api/).*)",
      "destination": "/index.html"
    }
  ]
}
```

### vite.config.ts 优化

- **代码分割**: 自动分离 vendor、router、UI 等模块
- **资源优化**: 压缩 JS/CSS，移除 console.log
- **缓存策略**: 文件名包含 hash，优化缓存

## 📊 部署验证

### 1. 检查部署状态

```bash
# 查看部署日志
vercel logs

# 检查项目状态
vercel ls
```

### 2. 功能测试清单

- [ ] 页面正常加载
- [ ] 用户注册/登录
- [ ] 语音合成功能
- [ ] API 调用正常
- [ ] 数据库连接
- [ ] 支付功能 (如果启用)

### 3. 性能检查

- [ ] 首屏加载时间 < 3s
- [ ] Lighthouse 评分 > 90
- [ ] 移动端适配正常

## 🌐 域名配置

### 1. 添加自定义域名

1. 在 Vercel Dashboard 中选择项目
2. 进入 **Settings** → **Domains**
3. 添加您的域名
4. 配置 DNS 记录

### 2. DNS 配置

```
# A 记录
@ → 76.76.19.61

# CNAME 记录
www → cname.vercel-dns.com
```

## 🔒 安全配置

### 1. 环境变量安全

- ✅ 使用 Vercel 环境变量存储敏感信息
- ✅ 不要在代码中硬编码密钥
- ✅ 定期轮换 API 密钥

### 2. 访问控制

- ✅ 配置 CORS 策略
- ✅ 启用 HTTPS
- ✅ 设置安全头部

## 🚨 故障排除

### 常见问题

#### 1. 构建失败

```bash
# 检查依赖
npm install

# 清理缓存
npm run build -- --force
```

#### 2. 环境变量未生效

- 检查变量名是否正确 (去掉 `VITE_` 前缀)
- 确认在 Vercel Dashboard 中已设置
- 重新部署项目

#### 3. API 调用失败

- 检查 Supabase 项目状态
- 验证 API 密钥有效性
- 查看网络请求日志

### 调试命令

```bash
# 本地预览构建结果
npm run preview

# 检查环境变量
vercel env ls

# 查看部署日志
vercel logs --follow
```

## 📈 监控和维护

### 1. 性能监控

- 使用 Vercel Analytics
- 配置 Sentry 错误监控
- 设置 Uptime 监控

### 2. 定期维护

- 更新依赖包
- 监控 API 使用量
- 备份重要数据

## 🎯 最佳实践

1. **环境分离**: 使用不同的 Supabase 项目用于开发和生产
2. **版本控制**: 使用 Git 标签管理发布版本
3. **自动化**: 设置 GitHub Actions 自动部署
4. **监控**: 配置错误监控和性能监控
5. **备份**: 定期备份数据库和配置

## 📞 支持

如果在部署过程中遇到问题，请：

1. 查看 [Vercel 文档](https://vercel.com/docs)
2. 检查 [Supabase 文档](https://supabase.com/docs)
3. 提交 GitHub Issue
4. 联系技术支持

---

**部署成功后，您的 SoulVoice 应用将在全球 CDN 上运行，为用户提供快速、可靠的语音服务！** 🎉
