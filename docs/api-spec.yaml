openapi: 3.0.3
info:
  title: SoulVoice API
  description: 高质量的语音合成与克隆 API 服务
  version: 1.0.0
  contact:
    name: SoulVoice Support
    email: <EMAIL>
    url: https://soulvoice.com
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://your-project.supabase.co/functions/v1
    description: Production server
  - url: http://localhost:54321/functions/v1
    description: Development server

security:
  - ApiKeyAuth: []

paths:
  /tts:
    post:
      summary: 生成语音
      description: 将文本转换为高质量的语音音频
      tags:
        - Text-to-Speech
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - text
              properties:
                text:
                  type: string
                  description: 要转换的文本内容
                  maxLength: 5000
                  example: "你好，欢迎使用 SoulVoice！"
                voice:
                  type: string
                  description: 音色 ID
                  default: "zh-CN-XiaoxiaoNeural"
                  example: "zh-CN-XiaoxiaoNeural"
                model:
                  type: string
                  description: 语音模型
                  default: "FunAudioLLM/CosyVoice2-0.5B"
                  example: "FunAudioLLM/CosyVoice2-0.5B"
                speed:
                  type: number
                  description: 语音速度 (0.5-2.0)
                  minimum: 0.5
                  maximum: 2.0
                  default: 1.0
                  example: 1.0
                pitch:
                  type: number
                  description: 音调 (-20 到 20)
                  minimum: -20
                  maximum: 20
                  example: 0
                emotion:
                  type: string
                  description: 情感表达
                  enum: [neutral, happy, sad, angry, excited]
                  example: "neutral"
                response_format:
                  type: string
                  description: 音频格式
                  enum: [mp3, wav, flac]
                  default: "mp3"
                  example: "mp3"
                sample_rate:
                  type: integer
                  description: 采样率
                  enum: [16000, 22050, 44100, 48000]
                  default: 22050
                  example: 22050
      responses:
        '200':
          description: 语音生成成功
          headers:
            X-Usage-Characters:
              description: 使用的字符数
              schema:
                type: integer
            X-Usage-Cost:
              description: 本次请求费用（美元）
              schema:
                type: number
                format: float
            X-RateLimit-Remaining:
              description: 剩余请求次数
              schema:
                type: integer
          content:
            audio/mpeg:
              schema:
                type: string
                format: binary
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: 认证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '429':
          description: 请求频率超限
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /voices:
    get:
      summary: 获取音色列表
      description: 获取可用的音色列表，包括系统音色和用户自定义音色
      tags:
        - Voices
      responses:
        '200':
          description: 音色列表获取成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  voices:
                    type: array
                    items:
                      $ref: '#/components/schemas/Voice'
                  total:
                    type: integer
                    description: 音色总数
        '401':
          description: 认证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /usage:
    get:
      summary: 获取用量统计
      description: 获取当前用户的 API 使用统计信息
      tags:
        - Usage
      responses:
        '200':
          description: 用量统计获取成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UsageStats'
        '401':
          description: 认证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

components:
  securitySchemes:
    ApiKeyAuth:
      type: http
      scheme: bearer
      bearerFormat: API Key
      description: 在 Authorization 头中使用 Bearer token 格式的 API 密钥

  schemas:
    Voice:
      type: object
      properties:
        id:
          type: string
          description: 音色唯一标识符
          example: "zh-CN-XiaoxiaoNeural"
        name:
          type: string
          description: 音色名称
          example: "温柔知性女声"
        language:
          type: string
          description: 语言代码
          example: "zh-CN"
        gender:
          type: string
          description: 性别
          enum: [male, female, unknown]
          example: "female"
        preview_url:
          type: string
          description: 音色预览 URL
          example: "https://example.com/preview.mp3"
        type:
          type: string
          description: 音色类型
          enum: [system, custom]
          example: "system"

    UsageStats:
      type: object
      properties:
        current_month:
          type: object
          properties:
            total_requests:
              type: integer
              description: 本月总请求数
            total_characters:
              type: integer
              description: 本月总字符数
            total_cost:
              type: number
              format: float
              description: 本月总费用
            tts_requests:
              type: integer
              description: TTS 请求数
            clone_requests:
              type: integer
              description: 语音克隆请求数
        today:
          type: object
          properties:
            total_requests:
              type: integer
              description: 今日总请求数
            total_characters:
              type: integer
              description: 今日总字符数
            total_cost:
              type: number
              format: float
              description: 今日总费用
        recent_usage:
          type: array
          items:
            type: object
            properties:
              date:
                type: string
                format: date
                description: 日期
              requests:
                type: integer
                description: 请求数
              characters:
                type: integer
                description: 字符数
              cost:
                type: number
                format: float
                description: 费用

    Error:
      type: object
      properties:
        error:
          type: object
          properties:
            message:
              type: string
              description: 错误消息
              example: "Invalid API key"
            code:
              type: string
              description: 错误代码
              example: "UNAUTHORIZED"
            timestamp:
              type: string
              format: date-time
              description: 错误发生时间
              example: "2024-01-01T12:00:00Z"
