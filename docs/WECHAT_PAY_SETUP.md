# 微信支付 API v3 集成配置指南

## 概述

本文档介绍如何在 SoulVoice 项目中配置和使用微信支付 API v3 功能。

## 前置条件

1. **微信商户账号**：需要有已认证的微信商户账号
2. **API 证书**：下载微信支付 API 证书文件
3. **Supabase 项目**：确保 Supabase 项目已正确配置

## 配置步骤

### 1. 获取微信支付配置信息

登录微信商户平台，获取以下信息：

- **应用 ID (AppID)**：微信公众号或小程序的 AppID
- **商户号 (MchID)**：微信支付商户号
- **API 证书序列号 (Serial No)**：API 证书的序列号
- **API v3 密钥 (API v3 Key)**：在商户平台设置的 API v3 密钥
- **私钥文件**：下载的 API 证书私钥文件内容

### 2. 配置 Supabase 环境变量

在 Supabase 项目的设置中添加以下环境变量：

```bash
# 微信支付配置（标准参数名）
WECHAT_APP_ID=wx2b08dc50xxxxxx
WECHAT_MCH_ID=17161281111
WECHAT_CERT_SERIAL_NUMBER=12BC2A1A155B4A8B660B1B34AEA7369B766DF151
WECHAT_API_V3_KEY=123xxxxxxxx

# 私钥配置（二选一）
# 方式1：直接配置私钥内容（推荐）
WECHAT_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----
your_private_key_content_here
-----END PRIVATE KEY-----"

# 方式2：配置私钥文件路径（仅本地开发）
WECHAT_KEY_PATH=./certs/apiclient_key.pem

# 证书配置（可选，用于验证微信支付平台签名）
# 方式1：直接配置证书内容
WECHAT_PUBLIC_KEY="-----BEGIN CERTIFICATE-----
your_certificate_content_here
-----END CERTIFICATE-----"

# 方式2：配置证书文件路径
WECHAT_CERT_PATH=./certs/apiclient_cert.pem
```

**注意事项：**
- 私钥内容需要包含完整的 PEM 格式头尾
- 确保私钥内容中的换行符正确
- 所有敏感信息都应通过环境变量配置，不要硬编码在代码中

### 3. 验证配置

#### 3.1 检查 Supabase Functions 部署

确认微信支付函数已正确部署：

```bash
supabase functions deploy wechat-pay --project-ref your_project_ref
```

#### 3.2 测试 API 连通性

访问测试页面验证功能：

```
https://your-app-domain.com/payment-test
```

## API 接口说明

### 创建支付订单

**接口地址：** `POST /functions/v1/wechat-pay/create-order`

**请求参数：**
```json
{
  "amount": 5000,  // 金额（分）
  "description": "SoulVoice 账户充值 $50",
  "userId": "user_uuid"
}
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "orderId": "order_uuid",
    "qrCode": "weixin://wxpay/bizpayurl?pr=xxx",
    "outTradeNo": "SV_1234567890_abc123",
    "amount": 5000,
    "description": "SoulVoice 账户充值 $50"
  }
}
```

### 查询订单状态

**接口地址：** `GET /functions/v1/wechat-pay/query-order/{orderId}`

**响应示例：**
```json
{
  "success": true,
  "data": {
    "order": {
      "id": "order_uuid",
      "status": "paid",
      "amount": 5000,
      "description": "SoulVoice 账户充值 $50",
      "created_at": "2024-01-01T00:00:00Z",
      "paid_at": "2024-01-01T00:05:00Z"
    }
  }
}
```

### 获取用户订单列表

**接口地址：** `GET /functions/v1/wechat-pay/orders?page=1&limit=20&status=paid`

## 前端集成

### 使用微信支付服务

```typescript
import { WechatPayService } from '../services/wechatPayService';

// 创建支付订单
const result = await WechatPayService.createOrder({
  amount: 5000, // 50元，单位：分
  description: '账户充值',
  userId: user.id,
});

if (result.success) {
  // 显示二维码
  console.log('支付二维码:', result.data.qrCode);
}
```

### 使用支付组件

```tsx
import { WechatPayModal } from '../components/payment/WechatPayModal';

<WechatPayModal
  isOpen={showPayment}
  onClose={() => setShowPayment(false)}
  amount={5000} // 金额（分）
  description="账户充值 $50"
  onSuccess={(order) => {
    console.log('支付成功:', order);
  }}
  onError={(error) => {
    console.error('支付失败:', error);
  }}
/>
```

## 安全注意事项

1. **密钥保护**：
   - 所有微信支付密钥都应存储在 Supabase 环境变量中
   - 不要在前端代码中暴露任何敏感信息
   - 定期轮换 API v3 密钥

2. **签名验证**：
   - 所有支付通知都会进行签名验证
   - 确保通知 URL 使用 HTTPS
   - 验证通知的时间戳防止重放攻击

3. **订单安全**：
   - 使用 RLS 策略确保用户只能访问自己的订单
   - 订单金额在服务端验证，不信任前端传入的金额
   - 实现幂等性防止重复支付

## 测试指南

### 开发环境测试

1. 配置微信支付沙箱环境（如果可用）
2. 使用测试商户号和证书
3. 访问 `/payment-test` 页面进行功能测试

### 生产环境部署

1. 确保所有环境变量正确配置
2. 验证支付通知 URL 可访问
3. 进行小额真实支付测试
4. 监控支付成功率和错误日志

## 常见问题

### Q: 签名验证失败
A: 检查私钥格式是否正确，确保包含完整的 PEM 头尾

### Q: 订单创建失败
A: 验证商户号和 AppID 是否匹配，检查 API v3 密钥是否正确

### Q: 支付通知未收到
A: 确认通知 URL 配置正确，检查网络连通性和 HTTPS 证书

### Q: 二维码无法扫描
A: 检查生成的支付链接格式，确认微信支付配置无误

## 监控和日志

系统会自动记录以下信息：

- 支付订单创建日志
- 支付通知接收日志
- 错误和异常日志
- 性能监控数据

可以在 Supabase Dashboard 的 Functions 日志中查看详细信息。

## 技术支持

如遇到问题，请检查：

1. Supabase Functions 日志
2. 微信商户平台的交易记录
3. 网络连接和防火墙设置
4. 证书有效期和权限配置

更多技术细节请参考微信支付官方文档：
https://pay.weixin.qq.com/wiki/doc/apiv3/index.shtml
