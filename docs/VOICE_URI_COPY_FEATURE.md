# 音色 URI 一键复制功能

## 📋 功能概述

为了方便开发者在接口调用时使用音色，我们在音色管理界面中新增了一键复制 URI 的功能。用户可以快速复制音色的 URI 标识符，用于 API 调用。

## 🎯 功能特性

### 1. 一键复制
- 点击复制按钮即可将音色 URI 复制到剪贴板
- 复制成功后按钮会显示绿色的勾选图标
- 2秒后自动恢复为复制图标

### 2. 视觉反馈
- **复制前**: 蓝色复制图标 📋
- **复制后**: 绿色勾选图标 ✅
- **悬停效果**: 按钮颜色变化提示

### 3. 工具提示
- 悬停时显示"复制音色 URI"
- 复制成功后显示"已复制 URI!"

## 📍 功能位置

该功能已添加到以下页面的音色卡片中：

### 1. 音色实验室 (`/voice-lab`)
- 位置：音色选择卡片的操作按钮组
- 图标：蓝色复制图标，位于播放按钮旁边

### 2. 音色测试 (`/voice-test`)
- 位置：音色列表卡片的操作按钮组
- 图标：蓝色复制图标，位于播放和删除按钮之间

### 3. 音色管理 (`/voice-management`)
- 位置：音色表格的操作列
- 图标：蓝色复制图标，位于编辑和删除按钮之前

## 🔧 技术实现

### 复制函数
```typescript
const handleCopyUri = (uri: string, voiceId: string) => {
  navigator.clipboard.writeText(uri);
  setCopiedUri(voiceId);
  setTimeout(() => setCopiedUri(null), 2000);
};
```

### 状态管理
```typescript
const [copiedUri, setCopiedUri] = useState<string | null>(null);
```

### UI 组件
```tsx
<button
  className={`p-1 transition-colors ${
    copiedUri === voice.id 
      ? 'text-green-400' 
      : 'text-blue-400 hover:text-blue-300'
  }`}
  onClick={(e) => {
    e.stopPropagation();
    handleCopyUri(voice.uri || voice.id, voice.id);
  }}
  title={copiedUri === voice.id ? '已复制 URI!' : '复制音色 URI'}
>
  {copiedUri === voice.id ? (
    <CheckCircle className="h-4 w-4" />
  ) : (
    <Copy className="h-4 w-4" />
  )}
</button>
```

## 📱 使用方法

### 步骤 1：进入音色管理页面
1. 登录 SoulVoice 系统
2. 导航到以下任一页面：
   - 音色实验室
   - 音色测试
   - 音色管理

### 步骤 2：找到目标音色
1. 浏览音色列表
2. 找到需要复制 URI 的音色

### 步骤 3：复制 URI
1. 点击音色卡片上的蓝色复制图标 📋
2. 观察图标变为绿色勾选 ✅
3. URI 已成功复制到剪贴板

### 步骤 4：使用 URI
将复制的 URI 粘贴到 API 调用中：

```typescript
// 使用复制的 URI 进行 API 调用
const result = await TTSService.generateSpeech({
  model: 'fnlp/MOSS-TTSD-v0.5',
  input: '你好，这是测试文本',
  voice: 'speech:mcg3fdnx:clzkyf4vy00e5qr6hywum4u84:bzznlkuhcjzpbosexitr', // 复制的 URI
});
```

## 🎨 UI 设计

### 按钮样式
- **默认状态**: 蓝色图标 (`text-blue-400`)
- **悬停状态**: 浅蓝色 (`hover:text-blue-300`)
- **复制成功**: 绿色图标 (`text-green-400`)
- **过渡效果**: 平滑颜色变化 (`transition-colors`)

### 图标使用
- **复制图标**: `Copy` from lucide-react
- **成功图标**: `CheckCircle` from lucide-react

## 🔄 兼容性

### 浏览器支持
- Chrome 66+
- Firefox 63+
- Safari 13.1+
- Edge 79+

### 功能降级
如果浏览器不支持 `navigator.clipboard` API，功能会静默失败，不会影响其他功能的使用。

## 🚀 使用场景

### 1. API 开发
开发者可以快速获取音色 URI，用于：
- REST API 调用
- SDK 集成
- 测试脚本编写

### 2. 配置管理
系统管理员可以：
- 快速获取系统音色 URI
- 配置默认音色设置
- 导出音色配置

### 3. 文档编写
技术文档编写者可以：
- 快速获取示例 URI
- 编写 API 使用示例
- 创建教程内容

## 📝 注意事项

1. **权限要求**: 需要浏览器剪贴板访问权限
2. **安全考虑**: URI 包含敏感信息，请妥善保管
3. **有效性**: 复制的 URI 仅在对应音色存在时有效
4. **格式**: URI 格式为 `speech:identifier:token:hash`

---

**更新日期**: 2025-01-31  
**版本**: v1.0.0  
**作者**: SoulVoice 开发团队
