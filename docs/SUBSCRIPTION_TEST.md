# 订阅功能测试指南

## 功能概述

已成功实现完整的订阅系统，包括：

### ✅ 已完成的功能

1. **数据库设计**
   - `subscription_plans` - 订阅计划表
   - `user_subscriptions` - 用户订阅记录表
   - `user_balances` - 用户余额表
   - 完整的 RLS 安全策略

2. **订阅计划**
   - 免费版：0元 - 10,000字节（注册赠送）
   - 基础版：9.9元/月 - 20万字节
   - 标准版：39.9元/月 - 100万字节
   - 专业版：68.8元/月 - 200万字节

3. **前端页面**
   - `/subscription` - 订阅页面（4个竖向卡片布局）
   - Dashboard 中的订阅卡片（替换了快速入门）
   - 侧边栏订阅菜单项

4. **支付集成**
   - 微信支付 API v3 集成
   - 支付成功后自动创建订阅
   - 自动更新用户余额和订阅等级

5. **后端服务**
   - `SubscriptionService` - 订阅服务类
   - `subscription-webhook` - 支付成功处理函数
   - 完整的错误处理和日志记录

## 测试步骤

### 1. 访问订阅页面

```
http://localhost:5173/subscription
```

**预期结果：**
- 显示4个订阅卡片（免费版 + 3个付费版）
- 显示当前用户状态（订阅等级、可用字节数）
- 免费版显示"当前计划"，付费版显示"立即订阅"

### 2. 测试订阅流程

1. **点击任意付费计划的"立即订阅"按钮**
   - 应该弹出微信支付模态框
   - 显示对应的金额和描述

2. **支付流程**
   - 显示二维码（测试环境可能无法真实支付）
   - 轮询订单状态
   - 支付成功后自动创建订阅

3. **订阅成功后**
   - 用户余额增加对应字节数
   - 订阅等级更新
   - 当前计划标记更新

### 3. 验证 Dashboard 集成

访问 Dashboard 页面：
```
http://localhost:5173/dashboard
```

**预期结果：**
- 右下角显示"订阅计划"卡片（替换了快速入门）
- 显示当前订阅等级和可用字节数
- 点击"查看订阅计划"跳转到订阅页面

### 4. 验证侧边栏菜单

**预期结果：**
- 侧边栏显示"订阅计划"菜单项（Crown 图标）
- 点击跳转到订阅页面

## 数据库验证

### 查看订阅计划
```sql
SELECT * FROM subscription_plans WHERE is_active = true;
```

### 查看用户余额
```sql
SELECT * FROM user_balances WHERE user_id = 'your_user_id';
```

### 查看用户订阅
```sql
SELECT 
  us.*,
  sp.name as plan_name,
  sp.price,
  sp.bytes_quota
FROM user_subscriptions us
JOIN subscription_plans sp ON us.plan_id = sp.id
WHERE us.user_id = 'your_user_id'
ORDER BY us.created_at DESC;
```

## API 测试

### 1. 获取订阅计划
```bash
curl -X GET \
  'https://xfsvmyceleiafhqewdkj.supabase.co/rest/v1/subscription_plans?is_active=eq.true' \
  -H 'apikey: your_anon_key' \
  -H 'Authorization: Bearer your_access_token'
```

### 2. 创建订阅（通过 Webhook）
```bash
curl -X POST \
  'https://xfsvmyceleiafhqewdkj.supabase.co/functions/v1/subscription-webhook/process-payment' \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer your_access_token' \
  -d '{
    "orderId": "order_uuid",
    "planId": "plan_uuid", 
    "userId": "user_uuid"
  }'
```

## 常见问题排查

### 1. 订阅页面无法加载
- 检查数据库连接
- 确认订阅计划数据已插入
- 查看浏览器控制台错误

### 2. 支付流程失败
- 确认微信支付配置正确
- 检查 Supabase Functions 日志
- 验证用户认证状态

### 3. 订阅创建失败
- 检查 subscription-webhook 函数日志
- 确认支付订单状态为 'paid'
- 验证订阅计划 ID 正确

### 4. 余额未更新
- 检查 user_balances 表权限
- 确认 Webhook 处理成功
- 查看函数执行日志

## 功能特性

### 🎨 UI/UX 特性
- 响应式设计，支持移动端
- 优雅的卡片布局和动画效果
- 清晰的订阅状态指示
- 实时的支付状态反馈

### 🔒 安全特性
- RLS 数据库安全策略
- 用户身份验证验证
- 支付订单状态验证
- 防重复订阅处理

### 📊 数据管理
- 完整的订阅生命周期管理
- 用户余额实时更新
- 订阅历史记录追踪
- 灵活的计划配置

### 🔄 集成特性
- 微信支付 API v3 集成
- Supabase Edge Functions
- 实时数据同步
- 错误处理和重试机制

## 下一步扩展

1. **订阅管理**
   - 订阅续费功能
   - 订阅取消/暂停
   - 订阅历史查看

2. **计费优化**
   - 按量计费选项
   - 企业定制计划
   - 优惠券系统

3. **用户体验**
   - 订阅到期提醒
   - 用量预警通知
   - 自动续费选项

4. **分析统计**
   - 订阅转化率分析
   - 用户行为追踪
   - 收入统计报表

## 技术架构

```
前端 (React/TypeScript)
    ↓
订阅服务 (SubscriptionService)
    ↓
Supabase Edge Functions
    ↓
数据库 (PostgreSQL + RLS)
    ↓
微信支付 API v3
```

这个订阅系统为 SoulVoice 提供了完整的商业化基础，支持灵活的定价策略和安全的支付处理。
