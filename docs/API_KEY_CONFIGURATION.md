# API 密钥配置指南

## 📋 概述

SoulVoice 需要配置 SiliconFlow API 密钥才能正常使用语音合成功能。本文档将指导您如何正确配置 API 密钥。

## 🔑 获取 SiliconFlow API 密钥

### 1. 注册 SiliconFlow 账户
1. 访问 [SiliconFlow 官网](https://siliconflow.cn)
2. 注册账户并完成实名认证
3. 登录到控制台

### 2. 创建 API 密钥
1. 在控制台中找到 "API 密钥" 或 "API Keys" 选项
2. 点击 "创建新密钥" 或 "Create New Key"
3. 设置密钥名称（如：SoulVoice-Production）
4. 复制生成的 API 密钥（格式类似：`sk-xxxxxxxxxxxxxxxxx`）

⚠️ **重要提醒**: API 密钥只会显示一次，请务必保存好！

## ⚙️ 配置方法

### 方法一：环境变量配置（推荐）

#### 1. 编辑 `.env` 文件
在项目根目录的 `.env` 文件中添加您的 API 密钥：

```bash
# SiliconFlow API 配置
VITE_SILICONFLOW_API_KEY=sk-your-actual-api-key-here
```

#### 2. 完整的 `.env` 文件示例
```bash
# Supabase 配置
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
VITE_SUPABASE_URL=https://xfsvmyceleiafhqewdkj.supabase.co

# SiliconFlow API 配置
VITE_SILICONFLOW_API_KEY=sk-ynabzdlcumjklweexfyruujoydkzfnqctcnlsiifbloqgdcw

# API 基础 URL (可选)
VITE_API_BASE_URL=https://xfsvmyceleiafhqewdkj.supabase.co/functions/v1
```

### 方法二：Supabase 环境变量（生产环境推荐）

对于生产环境，建议在 Supabase 项目中配置环境变量：

#### 1. 登录 Supabase 控制台
1. 访问 [Supabase 控制台](https://supabase.com/dashboard)
2. 选择您的项目

#### 2. 配置环境变量
1. 进入 "Settings" → "Environment Variables"
2. 添加新的环境变量：
   - **Name**: `SILICONFLOW_API_KEY`
   - **Value**: 您的 SiliconFlow API 密钥

#### 3. 重新部署 Functions
```bash
supabase functions deploy tts
```

## 🔧 配置验证

### 1. 检查配置是否生效
重启开发服务器后，可以在浏览器控制台中检查：

```javascript
// 在浏览器控制台中运行
console.log('API Key configured:', !!import.meta.env.VITE_SILICONFLOW_API_KEY);
```

### 2. 测试 API 调用
1. 打开音色实验室页面
2. 选择一个音色
3. 输入测试文本
4. 点击生成音频
5. 检查是否成功生成音频

## 🚨 安全注意事项

### 1. 保护 API 密钥
- ❌ **不要** 将 API 密钥提交到 Git 仓库
- ❌ **不要** 在公开场所分享 API 密钥
- ✅ **确保** `.env` 文件在 `.gitignore` 中
- ✅ **定期** 轮换 API 密钥

### 2. 环境分离
```bash
# 开发环境
.env.development

# 生产环境
.env.production

# 测试环境
.env.test
```

### 3. 权限控制
- 为不同环境使用不同的 API 密钥
- 设置适当的 API 调用限制
- 监控 API 使用情况

## 🔄 故障排除

### 问题 1: "API 密钥未配置"
**解决方案**:
1. 检查 `.env` 文件中是否正确配置了 `VITE_SILICONFLOW_API_KEY`
2. 重启开发服务器
3. 确保密钥格式正确（以 `sk-` 开头）

### 问题 2: "API 调用失败 401"
**解决方案**:
1. 验证 API 密钥是否有效
2. 检查 SiliconFlow 账户余额
3. 确认 API 密钥权限设置

### 问题 3: "API 调用失败 429"
**解决方案**:
1. 检查 API 调用频率限制
2. 实现请求重试机制
3. 考虑升级 API 套餐

## 📁 相关文件

### 配置文件
- `.env` - 本地环境变量
- `.env.example` - 环境变量模板
- `src/services/ttsService.ts` - API 调用服务

### 文档文件
- `docs/API_KEY_CONFIGURATION.md` - 本配置指南
- `docs/TTS_API_FIX.md` - API 修复说明

## 🎯 快速配置步骤

1. **获取 API 密钥**
   ```bash
   # 访问 SiliconFlow 控制台获取密钥
   ```

2. **配置环境变量**
   ```bash
   # 编辑 .env 文件
   echo "VITE_SILICONFLOW_API_KEY=sk-your-key-here" >> .env
   ```

3. **重启服务**
   ```bash
   npm run dev
   ```

4. **测试功能**
   - 打开音色实验室
   - 生成测试音频

## 📞 支持

如果您在配置过程中遇到问题：

1. 检查本文档的故障排除部分
2. 查看浏览器控制台的错误信息
3. 确认 SiliconFlow API 服务状态
4. 联系技术支持

---

**更新日期**: 2025-01-31  
**版本**: v1.0.0  
**状态**: ✅ 已完成
