# 语音合成 API 使用指南

## 概述

本文档介绍如何使用 SoulVoice 的语音合成 API 服务。该服务基于 SiliconFlow API 封装，提供高质量的中文语音合成功能。

## 快速开始

### 基本用法

```typescript
import { TTSService } from '../services/ttsService';

// 生成语音
const result = await TTSService.generateSpeech({
  model: 'fnlp/MOSS-TTSD-v0.5',
  input: '你好，欢迎使用 SoulVoice！',
  voice: 'zh-CN-XiaoxiaoNeural',
});

if (result.success) {
  // 处理音频数据
  if (result.audioData) {
    const audioUrl = TTSService.createAudioUrl(result.audioData);
    // 播放音频或下载
  }
  
  // 查看用量信息
  console.log('字符数:', result.usage?.characters);
  console.log('费用:', result.usage?.cost);
} else {
  console.error('生成失败:', result.error);
}
```

## API 接口

### TTSRequest 接口

```typescript
interface TTSRequest {
  model: string;        // 模型名称，默认: 'fnlp/MOSS-TTSD-v0.5'
  input: string;        // 要合成的文本 (最大 5000 字符)
  voice: string;        // 语音 ID
  speed?: number;       // 语速 (可选)
  pitch?: number;       // 音调 (可选)
  emotion?: string;     // 情感 (可选)
}
```

### TTSResponse 接口

```typescript
interface TTSResponse {
  success: boolean;     // 是否成功
  audioUrl?: string;    // 音频 URL (如果 API 返回 URL)
  audioData?: ArrayBuffer; // 音频数据 (如果 API 返回二进制数据)
  message?: string;     // 成功消息
  error?: string;       // 错误消息
  usage?: {             // 用量信息
    characters: number; // 字符数
    cost: number;       // 费用
  };
}
```

## 支持的语音

| 语音 ID | 名称 | 描述 |
|---------|------|------|
| zh-CN-XiaoxiaoNeural | 温柔知性女声 | 适合客服、教育场景 |
| zh-CN-YunxiNeural | 沉稳磁性男声 | 适合播音、解说场景 |
| zh-CN-XiaoyiNeural | 活泼青春女声 | 适合娱乐、互动场景 |
| zh-CN-YunjianNeural | 专业播音男声 | 适合新闻、正式场景 |

## 错误处理

服务提供了完善的错误处理机制：

```typescript
const result = await TTSService.generateSpeech(request);

if (!result.success) {
  switch (result.error) {
    case '输入文本不能为空':
      // 处理空文本错误
      break;
    case '输入文本长度不能超过 5000 字符':
      // 处理文本过长错误
      break;
    default:
      // 处理其他错误
      console.error('未知错误:', result.error);
  }
}
```

## 音频处理

### 播放音频

```typescript
if (result.success && result.audioData) {
  const audioUrl = TTSService.createAudioUrl(result.audioData);
  const audio = new Audio(audioUrl);
  
  audio.onended = () => {
    URL.revokeObjectURL(audioUrl); // 清理内存
  };
  
  audio.play();
}
```

### 下载音频

```typescript
if (result.success && result.audioData) {
  TTSService.downloadAudio(result.audioData, 'my-speech.mp3');
}
```

## 费用计算

- 语音合成: 每字符 $0.0002
- 费用会自动计算并记录到用量统计中

## 服务状态检查

```typescript
const status = await TTSService.getServiceStatus();

if (status.available) {
  console.log('服务可用，延迟:', status.latency, 'ms');
} else {
  console.log('服务不可用');
}
```

## 最佳实践

1. **文本长度控制**: 建议单次请求文本长度不超过 1000 字符，以获得最佳性能
2. **错误重试**: 对于网络错误，建议实现指数退避重试机制
3. **内存管理**: 及时调用 `URL.revokeObjectURL()` 释放音频 URL
4. **用量监控**: 定期检查用量统计，避免超出预算

## 集成示例

在 React 组件中使用：

```typescript
import React, { useState } from 'react';
import { TTSService } from '../services/ttsService';

const VoiceGenerator: React.FC = () => {
  const [text, setText] = useState('');
  const [loading, setLoading] = useState(false);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);

  const handleGenerate = async () => {
    if (!text.trim()) return;
    
    setLoading(true);
    try {
      const result = await TTSService.generateSpeech({
        model: 'fnlp/MOSS-TTSD-v0.5',
        input: text,
        voice: 'zh-CN-XiaoxiaoNeural',
      });

      if (result.success && result.audioData) {
        const url = TTSService.createAudioUrl(result.audioData);
        setAudioUrl(url);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <textarea 
        value={text} 
        onChange={(e) => setText(e.target.value)}
        placeholder="输入要合成的文本..."
      />
      <button onClick={handleGenerate} disabled={loading}>
        {loading ? '生成中...' : '生成语音'}
      </button>
      {audioUrl && <audio src={audioUrl} controls />}
    </div>
  );
};
```

## 故障排除

### 常见问题

1. **API 密钥错误**: 检查 API 密钥是否正确配置
2. **网络超时**: 检查网络连接，考虑增加超时时间
3. **音频格式不支持**: 确保浏览器支持 MP3 格式
4. **内存泄漏**: 确保及时释放音频 URL

### 调试模式

服务会在控制台输出详细的日志信息，包括：
- 请求参数
- 响应时间
- 错误详情
- 用量统计

查看控制台日志以获取更多调试信息。
