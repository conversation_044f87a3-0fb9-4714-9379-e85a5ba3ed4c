import { supabase } from '../lib/supabase';

export interface WechatPayOrder {
  id: string;
  user_id: string;
  amount: number;
  description: string;
  out_trade_no: string;
  prepay_id?: string;
  transaction_id?: string;
  status: 'pending' | 'paid' | 'failed' | 'cancelled';
  created_at: string;
  paid_at?: string;
  notify_data?: any;
}

export interface CreateOrderRequest {
  amount: number; // 金额（分）
  description: string;
  userId: string;
}

export interface CreateOrderResponse {
  success: boolean;
  data?: {
    orderId: string;
    qrCode: string;
    outTradeNo: string;
    amount: number;
    description: string;
  };
  error?: string;
}

export interface QueryOrderResponse {
  success: boolean;
  data?: {
    order: WechatPayOrder;
  };
  error?: string;
}

export interface GetOrdersResponse {
  success: boolean;
  data?: {
    orders: WechatPayOrder[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
  error?: string;
}

export class WechatPayService {
  private static readonly API_BASE_URL = import.meta.env.VITE_SUPABASE_URL;

  /**
   * 获取认证 Token
   */
  private static async getAuthToken(): Promise<string> {
    const { data: { session } } = await supabase.auth.getSession();
    return session?.access_token || '';
  }

  /**
   * 创建支付订单
   */
  static async createOrder(request: CreateOrderRequest): Promise<CreateOrderResponse> {
    try {
      const token = await this.getAuthToken();
      if (!token) {
        return {
          success: false,
          error: '用户未登录',
        };
      }

      const response = await fetch(`${this.API_BASE_URL}/functions/v1/wechat-pay/create-order`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(request),
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error?.message || '创建订单失败',
        };
      }

      return result;
    } catch (error) {
      console.error('Create order error:', error);
      return {
        success: false,
        error: '网络错误，请稍后重试',
      };
    }
  }

  /**
   * 查询订单状态
   */
  static async queryOrder(orderId: string): Promise<QueryOrderResponse> {
    try {
      const token = await this.getAuthToken();
      if (!token) {
        return {
          success: false,
          error: '用户未登录',
        };
      }

      const response = await fetch(`${this.API_BASE_URL}/functions/v1/wechat-pay/query-order/${orderId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error?.message || '查询订单失败',
        };
      }

      return result;
    } catch (error) {
      console.error('Query order error:', error);
      return {
        success: false,
        error: '网络错误，请稍后重试',
      };
    }
  }

  /**
   * 获取用户订单列表
   */
  static async getUserOrders(
    page: number = 1,
    limit: number = 20,
    status?: string
  ): Promise<GetOrdersResponse> {
    try {
      const token = await this.getAuthToken();
      if (!token) {
        return {
          success: false,
          error: '用户未登录',
        };
      }

      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });

      if (status) {
        params.append('status', status);
      }

      const response = await fetch(
        `${this.API_BASE_URL}/functions/v1/wechat-pay/orders?${params}`,
        {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        }
      );

      const result = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: result.error?.message || '获取订单列表失败',
        };
      }

      return result;
    } catch (error) {
      console.error('Get user orders error:', error);
      return {
        success: false,
        error: '网络错误，请稍后重试',
      };
    }
  }

  /**
   * 轮询订单状态
   */
  static async pollOrderStatus(
    orderId: string,
    onStatusChange: (order: WechatPayOrder) => void,
    maxAttempts: number = 150, // 5分钟，每2秒一次
    interval: number = 2000
  ): Promise<void> {
    let attempts = 0;

    const poll = async () => {
      if (attempts >= maxAttempts) {
        console.log('轮询超时，停止查询');
        return;
      }

      attempts++;

      try {
        const result = await this.queryOrder(orderId);
        
        if (result.success && result.data) {
          const order = result.data.order;
          onStatusChange(order);

          // 如果订单已完成（成功或失败），停止轮询
          if (order.status === 'paid' || order.status === 'failed') {
            return;
          }
        }

        // 继续轮询
        setTimeout(poll, interval);
      } catch (error) {
        console.error('轮询订单状态失败:', error);
        setTimeout(poll, interval);
      }
    };

    // 开始轮询
    poll();
  }

  /**
   * 格式化金额显示
   */
  static formatAmount(amount: number): string {
    return (amount / 100).toFixed(2);
  }

  /**
   * 格式化订单状态
   */
  static formatStatus(status: string): string {
    const statusMap: Record<string, string> = {
      pending: '待支付',
      paid: '已支付',
      failed: '支付失败',
      cancelled: '已取消',
    };
    return statusMap[status] || status;
  }

  /**
   * 获取状态颜色
   */
  static getStatusColor(status: string): string {
    const colorMap: Record<string, string> = {
      pending: 'text-yellow-500',
      paid: 'text-green-500',
      failed: 'text-red-500',
      cancelled: 'text-gray-500',
    };
    return colorMap[status] || 'text-gray-500';
  }

  /**
   * 检查订单是否可以重新支付
   */
  static canRetryPayment(order: WechatPayOrder): boolean {
    return order.status === 'failed' || order.status === 'cancelled';
  }

  /**
   * 检查订单是否已过期（30分钟）
   */
  static isOrderExpired(order: WechatPayOrder): boolean {
    if (order.status !== 'pending') {
      return false;
    }

    const createdAt = new Date(order.created_at);
    const now = new Date();
    const diffMinutes = (now.getTime() - createdAt.getTime()) / (1000 * 60);
    
    return diffMinutes > 30;
  }
}
