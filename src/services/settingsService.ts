import { supabase } from '../lib/supabase';
import type { UserSettings } from '../lib/supabase';

export class SettingsService {
  // 获取用户设置
  static async getUserSettings(userId: string): Promise<UserSettings | null> {
    const { data, error } = await supabase
      .from('user_settings')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error) {
      // 如果设置不存在，创建默认设置
      if (error.code === 'PGRST116') {
        return await this.createDefaultSettings(userId);
      }
      throw error;
    }

    return data;
  }

  // 创建默认设置
  private static async createDefaultSettings(userId: string): Promise<UserSettings> {
    const { data, error } = await supabase
      .from('user_settings')
      .insert({
        user_id: userId,
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  // 更新用户设置
  static async updateUserSettings(
    userId: string,
    updates: Partial<Pick<UserSettings, 'notifications' | 'preferences'>>
  ): Promise<UserSettings> {
    const { data, error } = await supabase
      .from('user_settings')
      .update(updates)
      .eq('user_id', userId)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  // 更新通知设置
  static async updateNotificationSettings(
    userId: string,
    notifications: UserSettings['notifications']
  ): Promise<UserSettings> {
    return await this.updateUserSettings(userId, { notifications });
  }

  // 更新偏好设置
  static async updatePreferences(
    userId: string,
    preferences: UserSettings['preferences']
  ): Promise<UserSettings> {
    return await this.updateUserSettings(userId, { preferences });
  }
}