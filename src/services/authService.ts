import { supabase } from '../lib/supabase';
import type { Profile } from '../lib/supabase';
import { ActivityService } from './activityService';

export class AuthService {
  // 注册用户
  static async signUp(email: string, password: string, name: string) {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          name,
        },
      },
    });

    if (error) throw error;

    // 如果用户需要邮箱确认，则不立即创建资料
    // 资料将在用户确认邮箱后通过触发器或后续登录时创建
    if (data.user && !data.user.email_confirmed_at) {
      // 用户需要确认邮箱，资料创建将延后
      console.log('User needs to confirm email before profile creation');
    } else if (data.user && data.user.email_confirmed_at) {
      // 用户已确认，可以创建资料
      await this.createUserProfile(data.user.id, email, name);
    }

    return data;
  }

  // 创建用户资料的辅助方法
  private static async createUserProfile(userId: string, email: string, name: string) {
    try {
      const { error: profileError } = await supabase
        .from('profiles')
        .insert({
          id: userId,
          email,
          name,
        });

      if (profileError) throw profileError;

      // 创建默认设置
      const { error: settingsError } = await supabase
        .from('user_settings')
        .insert({
          user_id: userId,
        });

      if (settingsError) throw settingsError;
    } catch (error) {
      console.error('Error creating user profile:', error);
      throw error;
    }
  }

  // 登录用户
  static async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) throw error;

    // 记录登录活动
    if (data.user) {
      try {
        await ActivityService.logAccountActivity(data.user.id, 'login');
      } catch (error) {
        console.error('Failed to log login activity:', error);
      }
    }

    return data;
  }

  // 使用 GitHub 登录
  static async signInWithGitHub() {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'github',
      options: {
        redirectTo: `${window.location.origin}/dashboard`,
      },
    });

    if (error) throw error;
    return data;
  }

  // 登出
  static async signOut() {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  }

  // 获取当前用户
  static async getCurrentUser() {
    const { data: { user } } = await supabase.auth.getUser();
    return user;
  }

  // 获取用户资料
  static async getProfile(userId: string): Promise<Profile | null> {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    // 如果用户资料不存在，返回 null 而不是抛出错误
    if (error && error.code === 'PGRST116') {
      console.log('User profile not found, may need to create one');
      return null;
    }

    if (error) {
      console.error('Error fetching user profile:', error);
      throw error;
    }

    return data;
  }

  // 更新用户资料
  static async updateProfile(userId: string, updates: Partial<Profile>) {
    const { data, error } = await supabase
      .from('profiles')
      .update(updates)
      .eq('id', userId)
      .select()
      .single();

    if (error) throw error;
    return data;
  }
}