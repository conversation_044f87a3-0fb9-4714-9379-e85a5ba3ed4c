import { supabase } from '../lib/supabase';
import type { ApiKey } from '../lib/supabase';
import SHA256 from 'crypto-js/sha256';
import { ActivityService } from './activityService';

export class ApiKeyService {
  // 生成随机十六进制字符串（浏览器兼容）
  private static generateRandomHexString(length: number): string {
    const array = new Uint8Array(length);
    window.crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  // 生成 API 密钥
  private static generateApiKey(): { key: string; hash: string; prefix: string } {
    const key = `sk-${this.generateRandomHexString(32)}`;
    const hash = SHA256(key).toString();
    // 不再截断密钥，存储完整密钥（但仍然叫prefix保持兼容性）
    const prefix = key;

    return { key, hash, prefix };
  }

  // 创建 API 密钥
  static async createApiKey(userId: string, name: string): Promise<{ apiKey: ApiKey; key: string }> {
    const { key, hash, prefix } = this.generateApiKey();

    const { data, error } = await supabase
      .from('api_keys')
      .insert({
        user_id: userId,
        name,
        key_hash: hash,
        key_prefix: prefix,
      })
      .select()
      .single();

    if (error) throw error;

    // 记录 API 密钥创建活动
    try {
      await ActivityService.logApiKeyActivity(userId, 'create', name, data.id);
    } catch (error) {
      console.error('Failed to log API key activity:', error);
    }

    return { apiKey: data, key };
  }

  // 获取用户的 API 密钥列表
  static async getApiKeys(userId: string): Promise<ApiKey[]> {
    const { data, error } = await supabase
      .from('api_keys')
      .select('*')
      .eq('user_id', userId)
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  // 删除 API 密钥
  static async deleteApiKey(keyId: string, userId: string): Promise<void> {
    // 先获取密钥信息用于记录活动
    const { data: keyData } = await supabase
      .from('api_keys')
      .select('name')
      .eq('id', keyId)
      .eq('user_id', userId)
      .single();

    const { error } = await supabase
      .from('api_keys')
      .update({ is_active: false })
      .eq('id', keyId)
      .eq('user_id', userId);

    if (error) throw error;

    // 记录 API 密钥删除活动
    if (keyData) {
      try {
        await ActivityService.logApiKeyActivity(userId, 'delete', keyData.name, keyId);
      } catch (error) {
        console.error('Failed to log API key activity:', error);
      }
    }
  }

  // 更新最后使用时间
  static async updateLastUsed(keyHash: string): Promise<void> {
    const { error } = await supabase
      .from('api_keys')
      .update({ last_used_at: new Date().toISOString() })
      .eq('key_hash', keyHash);

    if (error) throw error;
  }

  // 验证 API 密钥
  static async validateApiKey(key: string): Promise<ApiKey | null> {
    const hash = SHA256(key).toString();

    const { data, error } = await supabase
      .from('api_keys')
      .select('*')
      .eq('key_hash', hash)
      .eq('is_active', true)
      .single();

    if (error || !data) return null;

    // 更新最后使用时间
    await this.updateLastUsed(hash);

    return data;
  }
}