import { supabase } from '../lib/supabase';

export interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  bytes_quota: number;
  duration_days: number;
  features: string[];
  is_active: boolean;
  created_at: string;
}

export interface UserSubscription {
  id: string;
  user_id: string;
  plan_id: string;
  order_id?: string;
  status: 'active' | 'expired' | 'cancelled';
  bytes_quota: number;
  bytes_used: number;
  start_date: string;
  end_date: string;
  created_at: string;
  updated_at: string;
  plan?: SubscriptionPlan;
}

export interface UserBalance {
  id: string;
  user_id: string;
  total_bytes: number;
  used_bytes: number;
  free_bytes: number;
  subscription_level: 'free' | 'basic' | 'standard' | 'professional';
  updated_at: string;
}

export interface CreateSubscriptionRequest {
  planId: string;
  userId: string;
  orderId: string;
}

export class SubscriptionService {
  /**
   * 获取所有可用的订阅计划
   */
  static async getSubscriptionPlans(): Promise<SubscriptionPlan[]> {
    try {
      const { data, error } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .order('price', { ascending: true });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('获取订阅计划失败:', error);
      return [];
    }
  }

  /**
   * 获取用户当前订阅
   */
  static async getUserSubscription(userId: string): Promise<UserSubscription | null> {
    try {
      const { data, error } = await supabase
        .from('user_subscriptions')
        .select(`
          *,
          plan:subscription_plans(*)
        `)
        .eq('user_id', userId)
        .eq('status', 'active')
        .gte('end_date', new Date().toISOString())
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return data || null;
    } catch (error) {
      console.error('获取用户订阅失败:', error);
      return null;
    }
  }

  /**
   * 获取用户余额信息
   */
  static async getUserBalance(userId: string): Promise<UserBalance | null> {
    try {
      const { data, error } = await supabase
        .from('user_balances')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      
      // 如果用户没有余额记录，创建一个默认的
      if (!data) {
        return await this.createUserBalance(userId);
      }
      
      return data;
    } catch (error) {
      console.error('获取用户余额失败:', error);
      return null;
    }
  }

  /**
   * 创建用户余额记录
   */
  static async createUserBalance(userId: string): Promise<UserBalance> {
    try {
      const { data, error } = await supabase
        .from('user_balances')
        .insert({
          user_id: userId,
          total_bytes: 10000, // 注册赠送 10000 字节
          used_bytes: 0,
          free_bytes: 10000,
          subscription_level: 'free',
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('创建用户余额失败:', error);
      throw error;
    }
  }

  /**
   * 创建订阅（通过 Webhook 处理）
   */
  static async createSubscription(request: CreateSubscriptionRequest): Promise<UserSubscription> {
    try {
      // 调用订阅 Webhook 处理支付成功后的订阅创建
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/subscription-webhook/process-payment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`,
        },
        body: JSON.stringify({
          orderId: request.orderId,
          planId: request.planId,
          userId: request.userId,
        }),
      });

      const result = await response.json();

      if (!response.ok || !result.success) {
        throw new Error(result.error || '创建订阅失败');
      }

      // 重新获取创建的订阅记录
      const { data: subscription, error: subscriptionError } = await supabase
        .from('user_subscriptions')
        .select(`
          *,
          plan:subscription_plans(*)
        `)
        .eq('order_id', request.orderId)
        .single();

      if (subscriptionError) throw subscriptionError;

      return subscription;
    } catch (error) {
      console.error('创建订阅失败:', error);
      throw error;
    }
  }

  /**
   * 更新用户余额（订阅后）
   */
  static async updateUserBalanceForSubscription(userId: string, plan: SubscriptionPlan): Promise<void> {
    try {
      const currentBalance = await this.getUserBalance(userId);
      
      if (!currentBalance) {
        throw new Error('用户余额记录不存在');
      }

      // 确定订阅等级
      let subscriptionLevel: 'free' | 'basic' | 'standard' | 'professional' = 'free';
      if (plan.name === '基础版') subscriptionLevel = 'basic';
      else if (plan.name === '标准版') subscriptionLevel = 'standard';
      else if (plan.name === '专业版') subscriptionLevel = 'professional';

      // 更新余额
      const { error } = await supabase
        .from('user_balances')
        .update({
          total_bytes: currentBalance.total_bytes + plan.bytes_quota,
          subscription_level: subscriptionLevel,
          updated_at: new Date().toISOString(),
        })
        .eq('user_id', userId);

      if (error) throw error;
    } catch (error) {
      console.error('更新用户余额失败:', error);
      throw error;
    }
  }

  /**
   * 格式化字节数显示
   */
  static formatBytes(bytes: number): string {
    if (bytes === 0) return '0 字节';
    
    const k = 1000;
    const sizes = ['字节', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }

  /**
   * 格式化订阅等级显示
   */
  static formatSubscriptionLevel(level: string): string {
    const levelMap: Record<string, string> = {
      free: '免费版',
      basic: '基础版',
      standard: '标准版',
      professional: '专业版',
    };
    return levelMap[level] || level;
  }

  /**
   * 获取订阅等级颜色
   */
  static getSubscriptionLevelColor(level: string): string {
    const colorMap: Record<string, string> = {
      free: 'text-gray-500',
      basic: 'text-blue-500',
      standard: 'text-purple-500',
      professional: 'text-yellow-500',
    };
    return colorMap[level] || 'text-gray-500';
  }

  /**
   * 检查用户是否有足够的字节数
   */
  static async checkUserQuota(userId: string, requiredBytes: number): Promise<boolean> {
    try {
      const balance = await this.getUserBalance(userId);
      if (!balance) return false;
      
      const availableBytes = balance.total_bytes - balance.used_bytes;
      return availableBytes >= requiredBytes;
    } catch (error) {
      console.error('检查用户配额失败:', error);
      return false;
    }
  }

  /**
   * 消费用户字节数
   */
  static async consumeUserBytes(userId: string, bytesUsed: number): Promise<void> {
    try {
      const balance = await this.getUserBalance(userId);
      if (!balance) {
        throw new Error('用户余额记录不存在');
      }

      const newUsedBytes = balance.used_bytes + bytesUsed;
      
      if (newUsedBytes > balance.total_bytes) {
        throw new Error('字节数不足');
      }

      const { error } = await supabase
        .from('user_balances')
        .update({
          used_bytes: newUsedBytes,
          updated_at: new Date().toISOString(),
        })
        .eq('user_id', userId);

      if (error) throw error;
    } catch (error) {
      console.error('消费用户字节数失败:', error);
      throw error;
    }
  }
}
