export interface VoiceCloneRequest {
  displayName: string; // 用户输入的中文名称
  audioFile: File; // 音频文件对象
  text: string;
  model?: string; // 可选的模型参数
}

export interface VoiceCloneResponse {
  uri: string;
  customName: string; // API返回的UUID标识符
  displayName: string; // 用户输入的显示名称
  status: string;
  message?: string;
}

export interface VoiceCloneModel {
  id: string;
  name: string;
  description: string;
  features: string[];
  isDefault?: boolean;
}

export class VoiceCloneService {
  private static readonly API_URL = 'https://api.siliconflow.cn/v1/uploads/audio/voice';
  private static readonly API_KEY = 'sk-ynabzdlcumjklweexfyruujoydkzfnqctcnlsiifbloqgdcw';
  private static readonly DEFAULT_MODEL = 'FunAudioLLM/CosyVoice2-0.5B';

  // 支持的模型列表
  static readonly SUPPORTED_MODELS: VoiceCloneModel[] = [
    {
      id: 'FunAudioLLM/CosyVoice2-0.5B',
      name: 'CosyVoice 2.0',
      description: '基于大语言模型的流式语音合成模型，支持超低延迟和多语言',
      features: [
        '150ms 超低延迟',
        '支持中文方言（粤语、四川话等）',
        '支持英文、日语、韩语',
        '情感和方言细粒度控制',
        '发音错误率降低30%-50%'
      ],
      isDefault: true
    },
    {
      id: 'fnlp/MOSS-TTSD-v0.5',
      name: 'MOSS-TTSD',
      description: '开源双语口语对话合成模型，专为对话场景优化',
      features: [
        '双语支持（中文+英文）',
        '零样本双人声音克隆',
        '长时程语音生成（最长960秒）',
        '表现力丰富的对话语音',
        '适合AI播客制作'
      ]
    }
  ];

  /**
   * 获取支持的模型列表
   */
  static getSupportedModels(): VoiceCloneModel[] {
    return this.SUPPORTED_MODELS;
  }

  /**
   * 获取默认模型
   */
  static getDefaultModel(): VoiceCloneModel {
    return this.SUPPORTED_MODELS.find(model => model.isDefault) || this.SUPPORTED_MODELS[0];
  }

  /**
   * 根据ID获取模型信息
   */
  static getModelById(modelId: string): VoiceCloneModel | null {
    return this.SUPPORTED_MODELS.find(model => model.id === modelId) || null;
  }

  /**
   * 生成8位UUID作为customName
   */
  private static generateCustomName(): string {
    return Math.random().toString(36).substring(2, 10);
  }

  static async cloneVoice(request: VoiceCloneRequest): Promise<VoiceCloneResponse> {
    try {
      // 使用请求中的模型，如果没有则使用默认模型
      const selectedModel = request.model || this.DEFAULT_MODEL;

      // 生成8位UUID作为customName
      const customName = this.generateCustomName();

      // 将音频文件转换为base64
      const base64Audio = await this.convertFileToBase64(request.audioFile);

      // 构建JSON请求体
      const requestBody = {
        model: selectedModel,
        customName: customName,
        audio: base64Audio,
        text: request.text,
      };

      console.log('Voice clone request:', {
        model: selectedModel,
        customName: customName,
        textLength: request.text.length,
        audioSize: request.audioFile.size,
        audioType: request.audioFile.type
      });

      const response = await fetch(this.API_URL, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error Response:', errorText);
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log('Voice clone response:', data);

      return {
        uri: data.uri,
        customName: customName,
        displayName: request.displayName,
        status: 'success',
      };
    } catch (error) {
      console.error('Voice clone error:', error);
      return {
        uri: '',
        customName: '',
        displayName: request.displayName,
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * 将文件转换为base64格式，包含MIME类型前缀
   */
  static convertFileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // result 已经是 data:audio/xxx;base64,xxx 格式
        resolve(result);
      };
      reader.onerror = () => {
        reject(new Error('文件读取失败'));
      };
      reader.readAsDataURL(file);
    });
  }

  /**
   * 验证音频文件格式
   */
  static validateAudioFile(file: File): { valid: boolean; error?: string } {
    const allowedTypes = ['audio/wav', 'audio/mp3', 'audio/mpeg', 'audio/m4a', 'audio/aac'];
    const maxSize = 10 * 1024 * 1024; // 10MB

    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: '不支持的音频格式，请使用 WAV、MP3、M4A 或 AAC 格式'
      };
    }

    if (file.size > maxSize) {
      return {
        valid: false,
        error: '文件大小超过限制，请使用小于 10MB 的音频文件'
      };
    }

    return { valid: true };
  }

  static extractTextFromAudio(file: File): Promise<string> {
    // In a real implementation, you would use speech-to-text service
    // For now, we'll return a placeholder
    return Promise.resolve('请在此输入音频对应的文字内容');
  }
}