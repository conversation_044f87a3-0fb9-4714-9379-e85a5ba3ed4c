import { supabase } from '../lib/supabase';

export interface ActivityLog {
  id: string;
  user_id: string;
  activity_type: 'api_call' | 'api_key' | 'voice_model' | 'account';
  action: string;
  resource_type?: string;
  resource_id?: string;
  message: string;
  metadata?: any;
  created_at: string;
}

export interface ActivityMetadata {
  characters?: number;
  cost?: number;
  errorCode?: string;
  duration?: number;
  apiKeyName?: string;
  modelName?: string;
  endpoint?: string;
  responseTime?: number;
}

export class ActivityService {
  /**
   * 记录用户活动
   */
  static async logActivity(
    userId: string,
    activity: Omit<ActivityLog, 'id' | 'user_id' | 'created_at'>
  ): Promise<ActivityLog> {
    try {
      const { data, error } = await supabase
        .from('activity_logs')
        .insert({
          user_id: userId,
          activity_type: activity.activity_type,
          action: activity.action,
          resource_type: activity.resource_type,
          resource_id: activity.resource_id,
          message: activity.message,
          metadata: activity.metadata,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error logging activity:', error);
      throw error;
    }
  }

  /**
   * 获取用户最近活动
   */
  static async getRecentActivities(
    userId: string,
    limit: number = 10
  ): Promise<ActivityLog[]> {
    try {
      const { data, error } = await supabase
        .from('activity_logs')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching recent activities:', error);
      throw error;
    }
  }

  /**
   * 记录 API 调用活动
   */
  static async logApiCall(
    userId: string,
    serviceType: 'tts' | 'clone',
    status: 'success' | 'error',
    metadata: {
      characters?: number;
      cost?: number;
      errorMessage?: string;
      responseTime?: number;
      requestId?: string;
    }
  ): Promise<void> {
    const serviceName = serviceType === 'tts' ? '语音合成' : '语音克隆';
    const message = status === 'success' 
      ? `API 调用成功 - ${serviceName}`
      : `API 调用失败 - ${serviceName}`;

    await this.logActivity(userId, {
      activity_type: 'api_call',
      action: status,
      resource_type: serviceType,
      message,
      metadata: {
        ...metadata,
        endpoint: `/v1/${serviceType}`,
      },
    });
  }

  /**
   * 记录 API 密钥活动
   */
  static async logApiKeyActivity(
    userId: string,
    action: 'create' | 'delete' | 'regenerate',
    keyName: string,
    keyId?: string
  ): Promise<void> {
    const actionMap = {
      create: '创建',
      delete: '删除',
      regenerate: '重新生成',
    };

    const message = `${actionMap[action]} API 密钥 - ${keyName}`;

    await this.logActivity(userId, {
      activity_type: 'api_key',
      action,
      resource_type: 'api_key',
      resource_id: keyId,
      message,
      metadata: {
        apiKeyName: keyName,
      },
    });
  }

  /**
   * 记录语音模型活动
   */
  static async logVoiceModelActivity(
    userId: string,
    action: 'create' | 'ready' | 'failed',
    modelName: string,
    modelId?: string
  ): Promise<void> {
    const actionMap = {
      create: '开始训练',
      ready: '训练完成',
      failed: '训练失败',
    };

    const message = `语音模型${actionMap[action]} - ${modelName}`;

    await this.logActivity(userId, {
      activity_type: 'voice_model',
      action,
      resource_type: 'voice_model',
      resource_id: modelId,
      message,
      metadata: {
        modelName,
      },
    });
  }

  /**
   * 记录账户活动
   */
  static async logAccountActivity(
    userId: string,
    action: 'login' | 'logout' | 'profile_update' | 'settings_update',
    details?: string
  ): Promise<void> {
    const actionMap = {
      login: '用户登录',
      logout: '用户登出',
      profile_update: '更新个人资料',
      settings_update: '更新账户设置',
    };

    const message = details ? `${actionMap[action]} - ${details}` : actionMap[action];

    await this.logActivity(userId, {
      activity_type: 'account',
      action,
      resource_type: 'account',
      message,
    });
  }

  /**
   * 清理旧的活动记录（保留30天）
   */
  static async cleanupOldActivities(): Promise<void> {
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const { error } = await supabase
        .from('activity_logs')
        .delete()
        .lt('created_at', thirtyDaysAgo.toISOString());

      if (error) throw error;
    } catch (error) {
      console.error('Error cleaning up old activities:', error);
      throw error;
    }
  }

  /**
   * 获取活动统计信息
   */
  static async getActivityStats(
    userId: string,
    days: number = 7
  ): Promise<{
    totalActivities: number;
    apiCalls: number;
    successfulCalls: number;
    failedCalls: number;
  }> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const { data, error } = await supabase
        .from('activity_logs')
        .select('activity_type, action')
        .eq('user_id', userId)
        .gte('created_at', startDate.toISOString());

      if (error) throw error;

      const activities = data || [];
      const apiCalls = activities.filter(a => a.activity_type === 'api_call');
      
      return {
        totalActivities: activities.length,
        apiCalls: apiCalls.length,
        successfulCalls: apiCalls.filter(a => a.action === 'success').length,
        failedCalls: apiCalls.filter(a => a.action === 'error').length,
      };
    } catch (error) {
      console.error('Error fetching activity stats:', error);
      throw error;
    }
  }

  /**
   * 格式化活动时间为相对时间
   */
  static formatRelativeTime(dateString: string): string {
    const now = new Date();
    const activityTime = new Date(dateString);
    const diffInSeconds = Math.floor((now.getTime() - activityTime.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return `${diffInSeconds}秒前`;
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes}分钟前`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours}小时前`;
    } else {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days}天前`;
    }
  }
}
