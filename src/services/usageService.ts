import { supabase } from '../lib/supabase';
import type { UsageRecord } from '../lib/supabase';

export class UsageService {
  // 记录用量
  static async recordUsage(
    userId: string,
    serviceType: 'tts' | 'clone',
    bytesUsed: number,
    apiKeyId?: string
  ): Promise<UsageRecord> {
    const cost = this.calculateCost(serviceType, bytesUsed);

    const { data, error } = await supabase
      .from('usage_records')
      .insert({
        user_id: userId,
        api_key_id: apiKeyId,
        service_type: serviceType,
        characters_used: bytesUsed, // 数据库字段保持不变，但语义上现在是字节数
        cost,
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  // 计算费用
  private static calculateCost(serviceType: 'tts' | 'clone', bytesUsed: number): number {
    const rates = {
      tts: 0.0002, // 每字节 $0.0002
      clone: 0.001, // 每字节 $0.001
    };

    return bytesUsed * rates[serviceType];
  }

  // 获取用户用量统计
  static async getUserUsageStats(userId: string, days: number = 30) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const { data, error } = await supabase
      .from('usage_records')
      .select('*')
      .eq('user_id', userId)
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: false });

    if (error) throw error;

    const records = data || [];
    
    // 计算总用量
    const totalBytes = records.reduce((sum, record) => sum + record.characters_used, 0); // characters_used 字段现在存储字节数
    const totalCost = records.reduce((sum, record) => sum + record.cost, 0);

    // 按日期分组
    const dailyUsage = this.groupUsageByDate(records);

    // 按服务类型分组
    const usageByService = this.groupUsageByService(records);

    return {
      totalBytes,
      totalCost,
      dailyUsage,
      usageByService,
      records: records.slice(0, 10), // 最近 10 条记录
    };
  }

  // 按日期分组用量
  private static groupUsageByDate(records: UsageRecord[]) {
    const grouped: { [date: string]: { characters: number; cost: number } } = {};

    records.forEach(record => {
      const date = new Date(record.created_at).toISOString().split('T')[0];
      if (!grouped[date]) {
        grouped[date] = { characters: 0, cost: 0 };
      }
      grouped[date].characters += record.characters_used; // characters_used 字段现在存储字节数
      grouped[date].cost += record.cost;
    });

    return Object.entries(grouped)
      .map(([date, usage]) => ({ date, ...usage }))
      .sort((a, b) => a.date.localeCompare(b.date));
  }

  // 按服务类型分组用量
  private static groupUsageByService(records: UsageRecord[]) {
    const grouped: { [service: string]: { characters: number; cost: number; count: number } } = {};

    records.forEach(record => {
      const service = record.service_type;
      if (!grouped[service]) {
        grouped[service] = { characters: 0, cost: 0, count: 0 };
      }
      grouped[service].characters += record.characters_used; // characters_used 字段现在存储字节数
      grouped[service].cost += record.cost;
      grouped[service].count += 1;
    });

    return grouped;
  }

  // 获取用户当月用量
  static async getCurrentMonthUsage(userId: string) {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    const { data, error } = await supabase
      .from('usage_records')
      .select('characters_used, cost')
      .eq('user_id', userId)
      .gte('created_at', startOfMonth.toISOString());

    if (error) throw error;

    const records = data || [];
    const totalBytes = records.reduce((sum, record) => sum + record.characters_used, 0); // characters_used 字段现在存储字节数
    const totalCost = records.reduce((sum, record) => sum + record.cost, 0);

    return { totalBytes, totalCost };
  }
}