import { supabase } from '../lib/supabase';
import type { VoiceModel } from '../lib/supabase';

export class VoiceModelService {
  // 缓存机制
  private static voiceModelCache: Map<string, VoiceModel[]> = new Map();
  private static cacheTimestamp: Map<string, number> = new Map();
  private static readonly CACHE_DURATION = 2 * 60 * 1000; // 2分钟缓存

  // 创建语音模型记录
  static async createVoiceModel(
    userId: string,
    name: string,
    modelId: string,
    apiIdentifier?: string
  ): Promise<VoiceModel> {
    const { data, error } = await supabase
      .from('voice_models')
      .insert({
        user_id: userId,
        name,
        model_id: modelId,
        status: 'processing',
      })
      .select()
      .single();

    if (error) throw error;

    // 同时在 voices 表中创建记录
    if (apiIdentifier) {
      try {
        await supabase
          .from('voices')
          .insert({
            name: name,
            uri: modelId,
            voice_type: 'cloned',
            user_id: userId,
            api_identifier: apiIdentifier,
            preview_text: '这是您克隆的声音',
            model: 'FunAudioLLM/CosyVoice2-0.5B'
          });
      } catch (voiceError) {
        console.warn('Failed to create voice record:', voiceError);
      }
    }

    return data;
  }

  // 获取用户的语音模型列表
  static async getVoiceModels(userId: string): Promise<VoiceModel[]> {
    // 检查缓存
    if (this.isCacheValid(userId)) {
      const cachedModels = this.voiceModelCache.get(userId);
      if (cachedModels) {
        console.log(`[VoiceModelService] Using cached voice models for ${userId}`, { count: cachedModels.length });
        return cachedModels;
      }
    }

    try {
      console.log(`[VoiceModelService] Fetching voice models from database for ${userId}`);

      const { data, error } = await supabase
        .from('voice_models')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      const models = data || [];

      // 更新缓存
      this.voiceModelCache.set(userId, models);
      this.cacheTimestamp.set(userId, Date.now());

      console.log(`[VoiceModelService] Voice models fetched successfully for ${userId}`, { count: models.length });
      return models;
    } catch (error) {
      console.error('Error fetching voice models:', error);
      throw error;
    }
  }

  // 更新语音模型状态
  static async updateVoiceModelStatus(
    modelId: string,
    status: 'processing' | 'ready' | 'failed'
  ): Promise<void> {
    const { error } = await supabase
      .from('voice_models')
      .update({ status })
      .eq('id', modelId);

    if (error) throw error;
  }

  // 删除语音模型
  static async deleteVoiceModel(modelId: string, userId: string): Promise<void> {
    const { error } = await supabase
      .from('voice_models')
      .delete()
      .eq('id', modelId)
      .eq('user_id', userId);

    if (error) throw error;
  }

  // 根据外部模型 ID 查找语音模型
  static async getVoiceModelByExternalId(externalModelId: string): Promise<VoiceModel | null> {
    const { data, error } = await supabase
      .from('voice_models')
      .select('*')
      .eq('model_id', externalModelId)
      .single();

    if (error || !data) return null;
    return data;
  }

  /**
   * 检查缓存是否有效
   */
  private static isCacheValid(userId: string): boolean {
    const timestamp = this.cacheTimestamp.get(userId);
    return timestamp ? (Date.now() - timestamp < this.CACHE_DURATION) : false;
  }

  /**
   * 清除缓存
   */
  static clearCache(userId?: string): void {
    if (userId) {
      this.voiceModelCache.delete(userId);
      this.cacheTimestamp.delete(userId);
    } else {
      this.voiceModelCache.clear();
      this.cacheTimestamp.clear();
    }
    console.log(`[VoiceModelService] Cache cleared for ${userId || 'all'}`);
  }

  /**
   * 创建语音模型后清除缓存
   */
  static async createVoiceModelWithCacheClear(
    userId: string,
    name: string,
    modelId: string,
    apiIdentifier?: string
  ): Promise<VoiceModel> {
    const model = await this.createVoiceModel(userId, name, modelId, apiIdentifier);
    this.clearCache(userId);
    return model;
  }

  /**
   * 删除语音模型后清除缓存
   */
  static async deleteVoiceModelWithCacheClear(modelId: string, userId: string): Promise<void> {
    await this.deleteVoiceModel(modelId, userId);
    this.clearCache(userId);
  }

  /**
   * 更新语音模型状态后清除缓存
   */
  static async updateVoiceModelStatusWithCacheClear(
    modelId: string,
    status: 'processing' | 'ready' | 'failed'
  ): Promise<void> {
    await this.updateVoiceModelStatus(modelId, status);
    // 清除所有用户的缓存，因为我们不知道这个模型属于哪个用户
    this.clearCache();
  }
}