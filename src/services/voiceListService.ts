// 音色列表服务 - SiliconFlow API 封装
export interface VoiceItem {
  model: string;
  customName: string;
  text: string;
  uri: string;
}

export interface VoiceListResponse {
  success: boolean;
  voices?: VoiceItem[];
  error?: string;
}

export interface FormattedVoice {
  id: string;
  name: string;
  preview: string;
  uri: string;
  model: string;
  isCustom: boolean;
}

export class VoiceListService {
  private static readonly API_URL = 'https://api.siliconflow.cn/v1/audio/voice/list';
  private static readonly API_KEY = 'sk-ynabzdlcumjklweexfyruujoydkzfnqctcnlsiifbloqgdcw';
  
  // 缓存音色列表，避免频繁请求
  private static voiceCache: FormattedVoice[] | null = null;
  private static cacheTimestamp: number = 0;
  private static readonly CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存

  /**
   * 获取音色列表
   * @param forceRefresh 是否强制刷新缓存
   * @returns Promise<VoiceListResponse>
   */
  static async getVoiceList(forceRefresh: boolean = false): Promise<VoiceListResponse> {
    try {
      // 检查缓存
      if (!forceRefresh && this.voiceCache && this.isCacheValid()) {
        this.logInfo('Using cached voice list', { count: this.voiceCache.length });
        return {
          success: true,
          voices: this.voiceCache,
        };
      }

      this.logInfo('Fetching voice list from API');

      // 发送请求
      const response = await fetch(this.API_URL, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.API_KEY}`,
          'Content-Type': 'application/json',
          'User-Agent': 'SoulVoice/1.0.0',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        this.logError('API_ERROR', `HTTP ${response.status}`, {
          status: response.status,
          errorText,
        });

        return {
          success: false,
          error: `获取音色列表失败: ${response.status} - ${this.getErrorMessage(response.status)}`,
        };
      }

      const data = await response.json();

      if (!data.result || !Array.isArray(data.result)) {
        this.logError('INVALID_RESPONSE', '响应格式无效', { data });
        return {
          success: false,
          error: '响应格式无效',
        };
      }

      // 格式化音色数据
      const formattedVoices = this.formatVoices(data.result);
      
      // 更新缓存
      this.voiceCache = formattedVoices;
      this.cacheTimestamp = Date.now();

      this.logInfo('Voice list fetched successfully', { 
        count: formattedVoices.length,
        cached: true 
      });

      return {
        success: true,
        voices: formattedVoices,
      };

    } catch (error) {
      this.logError('NETWORK_ERROR', '网络请求失败', { error });
      
      // 如果有缓存，返回缓存数据
      if (this.voiceCache) {
        this.logInfo('Falling back to cached voice list');
        return {
          success: true,
          voices: this.voiceCache,
        };
      }

      return {
        success: false,
        error: '网络请求失败，请检查网络连接',
      };
    }
  }

  /**
   * 格式化音色数据
   */
  private static formatVoices(rawVoices: VoiceItem[]): FormattedVoice[] {
    return rawVoices.map((voice, index) => ({
      id: voice.uri || `voice-${index}`,
      name: voice.customName || `音色 ${index + 1}`,
      preview: voice.text || '这是一个音色预览',
      uri: voice.uri,
      model: voice.model,
      isCustom: true, // SiliconFlow 返回的都是自定义音色
    }));
  }

  /**
   * 检查缓存是否有效
   */
  private static isCacheValid(): boolean {
    return Date.now() - this.cacheTimestamp < this.CACHE_DURATION;
  }

  /**
   * 清除缓存
   */
  static clearCache(): void {
    this.voiceCache = null;
    this.cacheTimestamp = 0;
    this.logInfo('Voice cache cleared');
  }

  /**
   * 根据 URI 查找音色
   */
  static async findVoiceByUri(uri: string): Promise<FormattedVoice | null> {
    const result = await this.getVoiceList();
    if (!result.success || !result.voices) {
      return null;
    }

    return result.voices.find(voice => voice.uri === uri) || null;
  }

  /**
   * 获取默认音色列表（仅 API 音色，不再包含硬编码音色）
   */
  static async getAllVoices(): Promise<FormattedVoice[]> {
    // 获取 API 音色
    const apiResult = await this.getVoiceList();
    return apiResult.success ? (apiResult.voices || []) : [];
  }

  /**
   * 获取错误消息
   */
  private static getErrorMessage(status: number): string {
    const errorMessages: Record<number, string> = {
      400: '请求参数错误',
      401: 'API 密钥无效',
      403: '访问被拒绝',
      404: '接口不存在',
      429: '请求频率过高，请稍后重试',
      500: '服务器内部错误',
      502: '网关错误',
      503: '服务暂时不可用',
      504: '网关超时',
    };

    return errorMessages[status] || '未知错误';
  }

  /**
   * 记录信息日志
   */
  private static logInfo(message: string, data?: any): void {
    console.log(`[Voice List Service] ${message}`, data);
  }

  /**
   * 记录错误日志
   */
  private static logError(code: string, message: string, data?: any): void {
    console.error(`[Voice List Service] ${code}: ${message}`, data);
  }

  /**
   * 获取服务状态
   */
  static async getServiceStatus(): Promise<{ available: boolean; latency?: number }> {
    const startTime = Date.now();

    try {
      const response = await fetch(this.API_URL, {
        method: 'HEAD',
        headers: {
          'Authorization': `Bearer ${this.API_KEY}`,
        },
      });

      const latency = Date.now() - startTime;

      return {
        available: response.ok,
        latency,
      };
    } catch (error) {
      return {
        available: false,
      };
    }
  }
}
