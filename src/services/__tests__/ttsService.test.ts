import { TTSService } from '../ttsService';

// Mock fetch for testing
global.fetch = jest.fn();

describe('TTSService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('validateRequest', () => {
    it('should validate input text', async () => {
      const result = await TTSService.generateSpeech({
        model: 'test-model',
        input: '',
        voice: 'zh-CN-XiaoxiaoNeural',
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('输入文本不能为空');
    });

    it('should validate input length', async () => {
      const longText = 'a'.repeat(5001);
      const result = await TTSService.generateSpeech({
        model: 'test-model',
        input: longText,
        voice: 'zh-CN-XiaoxiaoNeural',
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('输入文本长度不能超过 5000 字符');
    });
  });

  describe('mapVoiceId', () => {
    it('should map predefined voice IDs', () => {
      const voices = TTSService.getSupportedVoices();
      expect(voices).toHaveLength(4);
      expect(voices[0].id).toBe('zh-CN-XiaoxiaoNeural');
    });
  });

  describe('calculateCost', () => {
    it('should calculate cost correctly', async () => {
      const mockResponse = new Response(new ArrayBuffer(1024), {
        status: 200,
        headers: { 'content-type': 'audio/mpeg' },
      });

      (global.fetch as jest.Mock).mockResolvedValueOnce(mockResponse);

      const result = await TTSService.generateSpeech({
        model: 'test-model',
        input: 'Hello world',
        voice: 'zh-CN-XiaoxiaoNeural',
      });

      if (result.success && result.usage) {
        expect(result.usage.characters).toBe(11);
        expect(result.usage.cost).toBe(0.0022); // 11 * 0.0002
      }
    });
  });

  describe('error handling', () => {
    it('should handle network errors', async () => {
      (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

      const result = await TTSService.generateSpeech({
        model: 'test-model',
        input: 'Hello world',
        voice: 'zh-CN-XiaoxiaoNeural',
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Network error');
    });

    it('should handle API errors', async () => {
      const mockResponse = new Response('API Error', {
        status: 400,
        statusText: 'Bad Request',
      });

      (global.fetch as jest.Mock).mockResolvedValueOnce(mockResponse);

      const result = await TTSService.generateSpeech({
        model: 'test-model',
        input: 'Hello world',
        voice: 'zh-CN-XiaoxiaoNeural',
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('API 请求失败');
    });
  });

  describe('utility functions', () => {
    it('should create audio URL from ArrayBuffer', () => {
      const audioData = new ArrayBuffer(1024);
      const url = TTSService.createAudioUrl(audioData);
      
      expect(url).toMatch(/^blob:/);
      
      // Clean up
      URL.revokeObjectURL(url);
    });

    it('should get service status', async () => {
      const mockResponse = new Response('', { status: 200 });
      (global.fetch as jest.Mock).mockResolvedValueOnce(mockResponse);

      const status = await TTSService.getServiceStatus();
      
      expect(status.available).toBe(true);
      expect(typeof status.latency).toBe('number');
    });
  });
});
