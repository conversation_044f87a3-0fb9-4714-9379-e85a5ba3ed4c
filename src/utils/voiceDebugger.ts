// 音色调试工具
import { supabase } from '../lib/supabase';
import { TTSService } from '../services/ttsService';
import { VoiceService } from '../services/voiceService';
import { VoiceModelService } from '../services/voiceModelService';
import { VoiceInitializer } from './initializeVoices';

export class VoiceDebugger {
  /**
   * 完整的音色系统诊断
   */
  static async diagnose(userId?: string): Promise<{
    summary: string;
    details: {
      databaseConnection: boolean;
      voicesTable: {
        exists: boolean;
        count: number;
        systemVoices: number;
        userVoices: number;
      };
      voiceModelsTable: {
        exists: boolean;
        count: number;
        userModels: number;
      };
      services: {
        ttsService: boolean;
        voiceService: boolean;
        voiceModelService: boolean;
      };
      caches: {
        ttsCache: number;
        voiceCache: number;
        voiceModelCache: number;
      };
    };
  }> {
    console.log('[VoiceDebugger] Starting comprehensive diagnosis...');
    
    const details = {
      databaseConnection: false,
      voicesTable: {
        exists: false,
        count: 0,
        systemVoices: 0,
        userVoices: 0,
      },
      voiceModelsTable: {
        exists: false,
        count: 0,
        userModels: 0,
      },
      services: {
        ttsService: false,
        voiceService: false,
        voiceModelService: false,
      },
      caches: {
        ttsCache: 0,
        voiceCache: 0,
        voiceModelCache: 0,
      },
    };

    // 1. 检查数据库连接
    try {
      const { error } = await supabase.from('voices').select('count(*)', { count: 'exact', head: true });
      details.databaseConnection = !error;
      if (error) {
        console.error('[VoiceDebugger] Database connection failed:', error);
      }
    } catch (error) {
      console.error('[VoiceDebugger] Database connection error:', error);
    }

    // 2. 检查 voices 表
    if (details.databaseConnection) {
      try {
        const { data: allVoices, error } = await supabase
          .from('voices')
          .select('*')
          .eq('is_active', true);

        if (!error && allVoices) {
          details.voicesTable.exists = true;
          details.voicesTable.count = allVoices.length;
          details.voicesTable.systemVoices = allVoices.filter(v => v.voice_type === 'system').length;
          details.voicesTable.userVoices = allVoices.filter(v => v.voice_type !== 'system').length;
        }
      } catch (error) {
        console.error('[VoiceDebugger] Error checking voices table:', error);
      }
    }

    // 3. 检查 voice_models 表
    if (details.databaseConnection && userId) {
      try {
        const { data: allModels, error } = await supabase
          .from('voice_models')
          .select('*');

        if (!error && allModels) {
          details.voiceModelsTable.exists = true;
          details.voiceModelsTable.count = allModels.length;
          details.voiceModelsTable.userModels = allModels.filter(m => m.user_id === userId).length;
        }
      } catch (error) {
        console.error('[VoiceDebugger] Error checking voice_models table:', error);
      }
    }

    // 4. 测试服务
    try {
      const voices = await TTSService.getSupportedVoices(userId);
      details.services.ttsService = Array.isArray(voices);
    } catch (error) {
      console.error('[VoiceDebugger] TTSService test failed:', error);
    }

    try {
      const voices = await VoiceService.getAllVoices(userId);
      details.services.voiceService = Array.isArray(voices);
    } catch (error) {
      console.error('[VoiceDebugger] VoiceService test failed:', error);
    }

    if (userId) {
      try {
        const models = await VoiceModelService.getVoiceModels(userId);
        details.services.voiceModelService = Array.isArray(models);
      } catch (error) {
        console.error('[VoiceDebugger] VoiceModelService test failed:', error);
      }
    }

    // 5. 检查缓存状态
    try {
      // 这些是私有属性，我们通过反射访问
      const ttsCache = (TTSService as any).voiceCache;
      const voiceCache = (VoiceService as any).voiceCache;
      const voiceModelCache = (VoiceModelService as any).voiceModelCache;

      details.caches.ttsCache = ttsCache ? ttsCache.size : 0;
      details.caches.voiceCache = voiceCache ? voiceCache.size : 0;
      details.caches.voiceModelCache = voiceModelCache ? voiceModelCache.size : 0;
    } catch (error) {
      console.warn('[VoiceDebugger] Could not access cache information:', error);
    }

    // 生成摘要
    let summary = '音色系统诊断结果:\n';
    
    if (!details.databaseConnection) {
      summary += '❌ 数据库连接失败\n';
    } else {
      summary += '✅ 数据库连接正常\n';
      
      if (details.voicesTable.count === 0) {
        summary += '⚠️ 音色表为空，需要初始化默认音色\n';
      } else {
        summary += `✅ 找到 ${details.voicesTable.count} 个音色 (系统: ${details.voicesTable.systemVoices}, 用户: ${details.voicesTable.userVoices})\n`;
      }
      
      if (userId && details.voiceModelsTable.exists) {
        summary += `📊 用户语音模型: ${details.voiceModelsTable.userModels} 个\n`;
      }
    }

    const serviceResults = Object.values(details.services);
    const workingServices = serviceResults.filter(Boolean).length;
    summary += `🔧 服务状态: ${workingServices}/${serviceResults.length} 正常\n`;

    const totalCacheEntries = Object.values(details.caches).reduce((sum, count) => sum + count, 0);
    summary += `💾 缓存条目: ${totalCacheEntries} 个\n`;

    console.log('[VoiceDebugger] Diagnosis completed:', { summary, details });

    return { summary, details };
  }

  /**
   * 快速修复常见问题
   */
  static async quickFix(): Promise<{
    success: boolean;
    message: string;
    actions: string[];
  }> {
    console.log('[VoiceDebugger] Starting quick fix...');
    const actions: string[] = [];

    try {
      // 1. 清除所有缓存
      TTSService.clearVoiceCache();
      VoiceService.clearCache();
      VoiceModelService.clearCache();
      actions.push('清除所有缓存');

      // 2. 检查并初始化默认音色
      const initResult = await VoiceInitializer.initialize();
      if (initResult.success) {
        actions.push(`初始化音色: ${initResult.message}`);
      } else {
        actions.push(`初始化失败: ${initResult.message}`);
      }

      // 3. 测试服务
      const testResult = await TTSService.getSupportedVoices();
      if (testResult.length > 0) {
        actions.push(`服务测试成功: 找到 ${testResult.length} 个音色`);
        return {
          success: true,
          message: '快速修复完成，音色系统已恢复正常',
          actions,
        };
      } else {
        actions.push('服务测试失败: 未找到音色');
        return {
          success: false,
          message: '快速修复未能解决问题，请检查数据库配置',
          actions,
        };
      }

    } catch (error) {
      console.error('[VoiceDebugger] Quick fix failed:', error);
      actions.push(`修复失败: ${error instanceof Error ? error.message : '未知错误'}`);
      
      return {
        success: false,
        message: '快速修复失败',
        actions,
      };
    }
  }

  /**
   * 生成详细报告
   */
  static async generateReport(userId?: string): Promise<string> {
    const diagnosis = await this.diagnose(userId);
    
    let report = '=== 音色系统诊断报告 ===\n\n';
    report += `时间: ${new Date().toLocaleString()}\n`;
    report += `用户ID: ${userId || '未指定'}\n\n`;
    
    report += '摘要:\n';
    report += diagnosis.summary + '\n';
    
    report += '详细信息:\n';
    report += `- 数据库连接: ${diagnosis.details.databaseConnection ? '正常' : '失败'}\n`;
    report += `- 音色表: ${diagnosis.details.voicesTable.count} 条记录\n`;
    report += `- 语音模型表: ${diagnosis.details.voiceModelsTable.count} 条记录\n`;
    report += `- TTSService: ${diagnosis.details.services.ttsService ? '正常' : '异常'}\n`;
    report += `- VoiceService: ${diagnosis.details.services.voiceService ? '正常' : '异常'}\n`;
    report += `- VoiceModelService: ${diagnosis.details.services.voiceModelService ? '正常' : '异常'}\n`;
    report += `- 缓存状态: TTS(${diagnosis.details.caches.ttsCache}) Voice(${diagnosis.details.caches.voiceCache}) Model(${diagnosis.details.caches.voiceModelCache})\n`;
    
    return report;
  }
}

// 在开发环境中暴露到全局对象，方便调试
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).VoiceDebugger = VoiceDebugger;
}
