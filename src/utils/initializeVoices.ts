// 初始化默认音色数据
import { supabase } from '../lib/supabase';

export interface DefaultVoice {
  name: string;
  description?: string;
  uri: string;
  model: string;
  voice_type: 'system';
  gender: 'male' | 'female' | 'neutral';
  language: string;
  preview_text: string;
  is_active: boolean;
  sort_order: number;
}

const DEFAULT_VOICES: DefaultVoice[] = [
  {
    name: '温柔知性女声',
    description: '温柔知性的女性声音，适合朗读和客服场景',
    uri: 'speech:xzzc01:clzkyf4vy00e5qr6hywum4u84:fobsoavejzngfngubrdd',
    model: 'FunAudioLLM/CosyVoice2-0.5B',
    voice_type: 'system',
    gender: 'female',
    language: 'zh-CN',
    preview_text: '你好，我是小晓，很高兴为您服务',
    is_active: true,
    sort_order: 1,
  },
  {
    name: '沉稳磁性男声',
    description: '沉稳磁性的男性声音，适合播音和解说场景',
    uri: 'speech:yunxi01:clzkyf4vy00e5qr6hywum4u84:fobsoavejzngfngubrdd',
    model: 'FunAudioLLM/CosyVoice2-0.5B',
    voice_type: 'system',
    gender: 'male',
    language: 'zh-CN',
    preview_text: '你好，我是云希，欢迎使用语音合成服务',
    is_active: true,
    sort_order: 2,
  },
  {
    name: '活泼青春女声',
    description: '活泼青春的女性声音，适合娱乐和互动场景',
    uri: 'speech:xiaoyi01:clzkyf4vy00e5qr6hywum4u84:fobsoavejzngfngubrdd',
    model: 'FunAudioLLM/CosyVoice2-0.5B',
    voice_type: 'system',
    gender: 'female',
    language: 'zh-CN',
    preview_text: '你好，我是小艺，让我们一起探索语音的魅力吧',
    is_active: true,
    sort_order: 3,
  },
  {
    name: '专业播音男声',
    description: '专业播音的男性声音，适合新闻和正式场景',
    uri: 'speech:yunjian01:clzkyf4vy00e5qr6hywum4u84:fobsoavejzngfngubrdd',
    model: 'FunAudioLLM/CosyVoice2-0.5B',
    voice_type: 'system',
    gender: 'male',
    language: 'zh-CN',
    preview_text: '你好，我是云健，为您提供专业的语音播报服务',
    is_active: true,
    sort_order: 4,
  },
];

export class VoiceInitializer {
  /**
   * 检查并初始化默认音色
   */
  static async initializeDefaultVoices(): Promise<{
    success: boolean;
    message: string;
    voicesCreated: number;
  }> {
    try {
      console.log('[VoiceInitializer] Checking for existing voices...');
      
      // 检查是否已有系统音色
      const { data: existingVoices, error: checkError } = await supabase
        .from('voices')
        .select('uri')
        .eq('voice_type', 'system')
        .eq('is_active', true);

      if (checkError) {
        console.error('[VoiceInitializer] Error checking existing voices:', checkError);
        return {
          success: false,
          message: `检查现有音色失败: ${checkError.message}`,
          voicesCreated: 0,
        };
      }

      const existingUris = new Set(existingVoices?.map(v => v.uri) || []);
      const voicesToCreate = DEFAULT_VOICES.filter(voice => !existingUris.has(voice.uri));

      if (voicesToCreate.length === 0) {
        console.log('[VoiceInitializer] All default voices already exist');
        return {
          success: true,
          message: '所有默认音色已存在',
          voicesCreated: 0,
        };
      }

      console.log(`[VoiceInitializer] Creating ${voicesToCreate.length} new voices...`);

      // 批量插入新音色
      const { data: createdVoices, error: insertError } = await supabase
        .from('voices')
        .insert(voicesToCreate)
        .select();

      if (insertError) {
        console.error('[VoiceInitializer] Error creating voices:', insertError);
        return {
          success: false,
          message: `创建音色失败: ${insertError.message}`,
          voicesCreated: 0,
        };
      }

      console.log(`[VoiceInitializer] Successfully created ${createdVoices?.length || 0} voices`);

      return {
        success: true,
        message: `成功创建 ${createdVoices?.length || 0} 个默认音色`,
        voicesCreated: createdVoices?.length || 0,
      };

    } catch (error) {
      console.error('[VoiceInitializer] Unexpected error:', error);
      return {
        success: false,
        message: `初始化失败: ${error instanceof Error ? error.message : '未知错误'}`,
        voicesCreated: 0,
      };
    }
  }

  /**
   * 检查数据库连接
   */
  static async checkDatabaseConnection(): Promise<{
    success: boolean;
    message: string;
  }> {
    try {
      console.log('[VoiceInitializer] Checking database connection...');
      
      const { data, error } = await supabase
        .from('voices')
        .select('count(*)', { count: 'exact', head: true });

      if (error) {
        console.error('[VoiceInitializer] Database connection failed:', error);
        return {
          success: false,
          message: `数据库连接失败: ${error.message}`,
        };
      }

      console.log('[VoiceInitializer] Database connection successful');
      return {
        success: true,
        message: '数据库连接正常',
      };

    } catch (error) {
      console.error('[VoiceInitializer] Database connection error:', error);
      return {
        success: false,
        message: `数据库连接错误: ${error instanceof Error ? error.message : '未知错误'}`,
      };
    }
  }

  /**
   * 完整的初始化流程
   */
  static async initialize(): Promise<{
    success: boolean;
    message: string;
    details: {
      databaseConnection: boolean;
      voicesCreated: number;
    };
  }> {
    console.log('[VoiceInitializer] Starting initialization...');

    // 1. 检查数据库连接
    const connectionResult = await this.checkDatabaseConnection();
    if (!connectionResult.success) {
      return {
        success: false,
        message: connectionResult.message,
        details: {
          databaseConnection: false,
          voicesCreated: 0,
        },
      };
    }

    // 2. 初始化默认音色
    const voicesResult = await this.initializeDefaultVoices();

    return {
      success: voicesResult.success,
      message: voicesResult.success 
        ? `初始化完成: ${voicesResult.message}`
        : `初始化失败: ${voicesResult.message}`,
      details: {
        databaseConnection: true,
        voicesCreated: voicesResult.voicesCreated,
      },
    };
  }
}

// 在开发环境中暴露到全局对象，方便调试
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).VoiceInitializer = VoiceInitializer;
}
