// 性能测试工具 - 用于测试音色加载优化效果

import { TTSService } from '../services/ttsService';
import { VoiceService } from '../services/voiceService';
import { VoiceModelService } from '../services/voiceModelService';

export class PerformanceTest {
  /**
   * 测试音色加载性能
   */
  static async testVoiceLoadingPerformance(userId?: string, iterations: number = 5): Promise<{
    averageTime: number;
    totalRequests: number;
    cacheHitRate: number;
    results: Array<{ iteration: number; time: number; fromCache: boolean }>;
  }> {
    console.log(`[PerformanceTest] Starting voice loading test with ${iterations} iterations`);
    
    const results: Array<{ iteration: number; time: number; fromCache: boolean }> = [];
    let totalRequests = 0;
    let cacheHits = 0;

    // 清除缓存以确保第一次请求是从数据库获取
    TTSService.clearVoiceCache(userId);
    VoiceService.clearCache(userId);
    if (userId) {
      VoiceModelService.clearCache(userId);
    }

    for (let i = 1; i <= iterations; i++) {
      const startTime = performance.now();
      
      try {
        // 模拟 VoiceLab 组件的加载逻辑
        const [systemVoices, userVoiceModels] = await Promise.all([
          TTSService.getSupportedVoices(userId),
          userId ? VoiceModelService.getVoiceModels(userId) : Promise.resolve([])
        ]);

        const endTime = performance.now();
        const duration = endTime - startTime;
        
        // 判断是否来自缓存（第一次请求通常较慢）
        const fromCache = i > 1 && duration < 100; // 如果响应时间小于100ms，认为是缓存命中
        
        if (fromCache) {
          cacheHits++;
        }

        results.push({
          iteration: i,
          time: duration,
          fromCache
        });

        totalRequests++;

        console.log(`[PerformanceTest] Iteration ${i}: ${duration.toFixed(2)}ms ${fromCache ? '(cached)' : '(database)'}`);
        
        // 短暂延迟以模拟真实使用场景
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        console.error(`[PerformanceTest] Error in iteration ${i}:`, error);
      }
    }

    const averageTime = results.reduce((sum, result) => sum + result.time, 0) / results.length;
    const cacheHitRate = (cacheHits / totalRequests) * 100;

    const summary = {
      averageTime,
      totalRequests,
      cacheHitRate,
      results
    };

    console.log(`[PerformanceTest] Test completed:`, {
      averageTime: `${averageTime.toFixed(2)}ms`,
      totalRequests,
      cacheHitRate: `${cacheHitRate.toFixed(1)}%`,
      firstRequestTime: `${results[0]?.time.toFixed(2)}ms`,
      cachedRequestsAverage: results.filter(r => r.fromCache).length > 0 
        ? `${(results.filter(r => r.fromCache).reduce((sum, r) => sum + r.time, 0) / results.filter(r => r.fromCache).length).toFixed(2)}ms`
        : 'N/A'
    });

    return summary;
  }

  /**
   * 测试重复请求优化效果
   */
  static async testDuplicateRequestPrevention(userId?: string): Promise<{
    simultaneousRequests: number;
    actualDatabaseCalls: number;
    preventionRate: number;
  }> {
    console.log(`[PerformanceTest] Testing duplicate request prevention`);
    
    // 清除缓存
    TTSService.clearVoiceCache(userId);
    VoiceService.clearCache(userId);
    if (userId) {
      VoiceModelService.clearCache(userId);
    }

    const simultaneousRequests = 10;
    let databaseCallCount = 0;

    // 监控数据库调用
    const originalGetAllVoices = VoiceService.getAllVoices;
    VoiceService.getAllVoices = async (uid?: string) => {
      databaseCallCount++;
      console.log(`[PerformanceTest] Database call #${databaseCallCount}`);
      return originalGetAllVoices.call(VoiceService, uid);
    };

    try {
      // 同时发起多个相同的请求
      const promises = Array.from({ length: simultaneousRequests }, (_, i) => 
        TTSService.getSupportedVoices(userId).then(voices => {
          console.log(`[PerformanceTest] Request ${i + 1} completed with ${voices.length} voices`);
          return voices;
        })
      );

      await Promise.all(promises);

      const preventionRate = ((simultaneousRequests - databaseCallCount) / simultaneousRequests) * 100;

      const result = {
        simultaneousRequests,
        actualDatabaseCalls: databaseCallCount,
        preventionRate
      };

      console.log(`[PerformanceTest] Duplicate request prevention test completed:`, {
        simultaneousRequests,
        actualDatabaseCalls: databaseCallCount,
        preventionRate: `${preventionRate.toFixed(1)}%`
      });

      return result;

    } finally {
      // 恢复原始方法
      VoiceService.getAllVoices = originalGetAllVoices;
    }
  }

  /**
   * 运行完整的性能测试套件
   */
  static async runFullTestSuite(userId?: string): Promise<{
    loadingPerformance: any;
    duplicateRequestPrevention: any;
  }> {
    console.log(`[PerformanceTest] Running full performance test suite`);
    
    const loadingPerformance = await this.testVoiceLoadingPerformance(userId);
    
    // 等待一段时间让缓存稳定
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const duplicateRequestPrevention = await this.testDuplicateRequestPrevention(userId);

    const results = {
      loadingPerformance,
      duplicateRequestPrevention
    };

    console.log(`[PerformanceTest] Full test suite completed:`, results);
    
    return results;
  }

  /**
   * 清除所有缓存
   */
  static clearAllCaches(userId?: string): void {
    TTSService.clearVoiceCache(userId);
    VoiceService.clearCache(userId);
    if (userId) {
      VoiceModelService.clearCache(userId);
    }
    console.log(`[PerformanceTest] All caches cleared for ${userId || 'all users'}`);
  }
}

// 在开发环境中暴露到全局对象，方便调试
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).PerformanceTest = PerformanceTest;
}
