import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// 数据库类型定义
export interface Profile {
  id: string;
  email: string;
  name: string;
  company?: string;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
  is_admin?: boolean;
}

export interface ApiKey {
  id: string;
  user_id: string;
  name: string;
  key_hash: string;
  key_prefix: string;
  last_used_at?: string;
  created_at: string;
  is_active: boolean;
}

export interface VoiceModel {
  id: string;
  user_id: string;
  name: string;
  model_id: string;
  status: 'processing' | 'ready' | 'failed';
  created_at: string;
}

export interface UsageRecord {
  id: string;
  user_id: string;
  api_key_id?: string;
  service_type: 'tts' | 'clone';
  characters_used: number;
  cost: number;
  status?: 'success' | 'error' | 'pending';
  error_message?: string;
  request_id?: string;
  response_time_ms?: number;
  created_at: string;
}

export interface UserSettings {
  id: string;
  user_id: string;
  notifications: {
    emailNotifications: boolean;
    usageAlerts: boolean;
    securityAlerts: boolean;
    productUpdates: boolean;
  };
  preferences: {
    theme: string;
    language: string;
    defaultVoice: string;
  };
  updated_at: string;
}

export interface ActivityLog {
  id: string;
  user_id: string;
  activity_type: 'api_call' | 'api_key' | 'voice_model' | 'account';
  action: string;
  resource_type?: string;
  resource_id?: string;
  message: string;
  metadata?: any;
  created_at: string;
}

export interface Voice {
  id: string;
  name: string;
  description?: string;
  uri: string;
  model: string;
  voice_type: 'system' | 'user_custom' | 'cloned';
  user_id?: string;
  gender?: 'male' | 'female' | 'neutral';
  language: string;
  preview_text?: string;
  is_active: boolean;
  sort_order: number;
  metadata?: any;
  created_at: string;
  updated_at: string;
}