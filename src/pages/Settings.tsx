import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Settings as SettingsIcon, 
  User, 
  Bell, 
  Shield, 
  CreditCard,
  Globe,
  Moon,
  Sun,
  Save,
  Trash2,
  AlertTriangle
} from 'lucide-react';
import { Sidebar } from '../components/layout/Sidebar';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Input } from '../components/ui/Input';
import { useAuth } from '../contexts/AuthContext';
import { SettingsService } from '../services/settingsService';
import { TTSService } from '../services/ttsService';
import { WechatPayModal } from '../components/payment/WechatPayModal';
import { WechatPayService, type WechatPayOrder } from '../services/wechatPayService';
import type { UserSettings } from '../lib/supabase';

export const Settings: React.FC = () => {
  const { user, profile, updateProfile } = useAuth();
  const [activeTab, setActiveTab] = useState('profile');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [voices, setVoices] = useState([]);
  const [showWechatPay, setShowWechatPay] = useState(false);
  const [paymentAmount, setPaymentAmount] = useState(50); // 默认充值金额
  const [settings, setSettings] = useState({
    profile: {
      name: '',
      email: '',
      company: '',
      timezone: 'Asia/Shanghai'
    },
    notifications: {
      emailNotifications: true,
      usageAlerts: true,
      securityAlerts: true,
      productUpdates: false
    },
    security: {
      twoFactorEnabled: false,
      sessionTimeout: 30
    },
    billing: {
      plan: 'free',
      autoRecharge: false,
      rechargeAmount: 50
    },
    preferences: {
      theme: 'dark',
      language: 'zh-CN',
      defaultVoice: 'zh-CN-XiaoxiaoNeural'
    }
  });

  useEffect(() => {
    if (user && profile) {
      loadSettings();
    }
    loadVoices();
  }, [user, profile]);

  const loadSettings = async () => {
    try {
      setLoading(true);
      
      // 加载用户设置
      const userSettings = await SettingsService.getUserSettings(user!.id);
      
      setSettings(prev => ({
        ...prev,
        profile: {
          name: profile?.name || '',
          email: profile?.email || '',
          company: profile?.company || '',
          timezone: 'Asia/Shanghai'
        },
        notifications: userSettings?.notifications || prev.notifications,
        preferences: userSettings?.preferences || prev.preferences
      }));
    } catch (error) {
      console.error('Error loading settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadVoices = async () => {
    try {
      const voiceList = await TTSService.getSupportedVoices();
      setVoices(voiceList);
    } catch (error) {
      console.error('Error loading voices:', error);
    }
  };

  const tabs = [
    { id: 'profile', label: '个人资料', icon: User },
    { id: 'notifications', label: '通知设置', icon: Bell },
    { id: 'security', label: '安全', icon: Shield },
    { id: 'billing', label: '账单', icon: CreditCard },
    { id: 'preferences', label: '偏好设置', icon: Globe },
  ];

  const handleSave = async () => {
    try {
      setSaving(true);
      
      // 更新用户资料
      if (activeTab === 'profile') {
        await updateProfile({
          name: settings.profile.name,
          company: settings.profile.company
        });
      }
      
      // 更新通知设置
      if (activeTab === 'notifications') {
        await SettingsService.updateNotificationSettings(user!.id, settings.notifications);
      }
      
      // 更新偏好设置
      if (activeTab === 'preferences') {
        await SettingsService.updatePreferences(user!.id, settings.preferences);
      }
      
      // 可以显示成功消息
      console.log('Settings saved successfully');
    } catch (error) {
      console.error('Error saving settings:', error);
    } finally {
      setSaving(false);
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'profile':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-xl font-semibold mb-4">个人资料</h3>
              <p className="text-gray-400 mb-6">管理您的账户信息</p>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <Input
                label="姓名"
                value={settings.profile.name}
                onChange={(e) => setSettings(prev => ({
                  ...prev,
                  profile: { ...prev.profile, name: e.target.value }
                }))}
              />
              <Input
                label="邮箱地址"
                type="email"
                value={settings.profile.email}
                onChange={(e) => setSettings(prev => ({
                  ...prev,
                  profile: { ...prev.profile, email: e.target.value }
                }))}
              />
              <Input
                label="公司名称"
                value={settings.profile.company}
                onChange={(e) => setSettings(prev => ({
                  ...prev,
                  profile: { ...prev.profile, company: e.target.value }
                }))}
                placeholder="可选"
              />
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  时区
                </label>
                <select
                  value={settings.profile.timezone}
                  onChange={(e) => setSettings(prev => ({
                    ...prev,
                    profile: { ...prev.profile, timezone: e.target.value }
                  }))}
                  className="w-full p-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  <option value="Asia/Shanghai">中国标准时间 (UTC+8)</option>
                  <option value="America/New_York">美国东部时间 (UTC-5)</option>
                  <option value="Europe/London">格林威治时间 (UTC+0)</option>
                  <option value="Asia/Tokyo">日本标准时间 (UTC+9)</option>
                </select>
              </div>
            </div>
          </div>
        );

      case 'notifications':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-xl font-semibold mb-4">通知设置</h3>
              <p className="text-gray-400 mb-6">选择您希望接收的通知类型</p>
            </div>

            <div className="space-y-4">
              {[
                { key: 'emailNotifications', label: '邮件通知', desc: '接收重要的账户和服务更新' },
                { key: 'usageAlerts', label: '用量提醒', desc: '当用量达到阈值时通知您' },
                { key: 'securityAlerts', label: '安全警报', desc: '账户安全相关的重要通知' },
                { key: 'productUpdates', label: '产品更新', desc: '新功能和产品改进通知' },
              ].map((item) => (
                <div key={item.key} className="flex items-center justify-between p-4 bg-gray-800 rounded-lg">
                  <div>
                    <h4 className="font-medium">{item.label}</h4>
                    <p className="text-sm text-gray-400">{item.desc}</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={settings.notifications[item.key as keyof typeof settings.notifications]}
                      onChange={(e) => setSettings(prev => ({
                        ...prev,
                        notifications: {
                          ...prev.notifications,
                          [item.key]: e.target.checked
                        }
                      }))}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-500"></div>
                  </label>
                </div>
              ))}
            </div>
          </div>
        );

      case 'security':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-xl font-semibold mb-4">安全设置</h3>
              <p className="text-gray-400 mb-6">保护您的账户安全</p>
            </div>

            <Card glass>
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">双因素认证</h4>
                  <p className="text-sm text-gray-400">为您的账户添加额外的安全层</p>
                </div>
                <Button
                  variant={settings.security.twoFactorEnabled ? "secondary" : "primary"}
                  size="sm"
                  onClick={() => setSettings(prev => ({
                    ...prev,
                    security: {
                      ...prev.security,
                      twoFactorEnabled: !prev.security.twoFactorEnabled
                    }
                  }))}
                >
                  {settings.security.twoFactorEnabled ? '已启用' : '启用'}
                </Button>
              </div>
            </Card>

            <Card glass>
              <div>
                <h4 className="font-medium mb-4">会话超时</h4>
                <p className="text-sm text-gray-400 mb-4">设置自动登出时间（分钟）</p>
                <select
                  value={settings.security.sessionTimeout}
                  onChange={(e) => setSettings(prev => ({
                    ...prev,
                    security: {
                      ...prev.security,
                      sessionTimeout: parseInt(e.target.value)
                    }
                  }))}
                  className="w-full max-w-xs p-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  <option value={15}>15 分钟</option>
                  <option value={30}>30 分钟</option>
                  <option value={60}>1 小时</option>
                  <option value={120}>2 小时</option>
                  <option value={0}>永不超时</option>
                </select>
              </div>
            </Card>

            <Card glass>
              <div>
                <h4 className="font-medium mb-4">密码管理</h4>
                <div className="space-y-3">
                  <Button variant="glass">
                    修改密码
                  </Button>
                  <Button variant="glass">
                    查看登录历史
                  </Button>
                </div>
              </div>
            </Card>
          </div>
        );

      case 'billing':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-xl font-semibold mb-4">账单设置</h3>
              <p className="text-gray-400 mb-6">管理您的订阅和付费设置</p>
            </div>

            <Card glass>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">当前套餐</h4>
                  <div className="flex items-center justify-between p-4 bg-gray-800 rounded-lg">
                    <div>
                      <span className="font-medium">免费套餐</span>
                      <p className="text-sm text-gray-400">10,000 字符/月</p>
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="primary" size="sm">
                        升级套餐
                      </Button>
                      <Button
                        variant="glass"
                        size="sm"
                        onClick={() => {
                          setPaymentAmount(50);
                          setShowWechatPay(true);
                        }}
                      >
                        微信充值
                      </Button>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">自动充值</h4>
                  <div className="flex items-center justify-between p-4 bg-gray-800 rounded-lg">
                    <div>
                      <span className="font-medium">当余额不足时自动充值</span>
                      <p className="text-sm text-gray-400">避免服务中断</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={settings.billing.autoRecharge}
                        onChange={(e) => setSettings(prev => ({
                          ...prev,
                          billing: { ...prev.billing, autoRecharge: e.target.checked }
                        }))}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-500"></div>
                    </label>
                  </div>
                </div>

                {settings.billing.autoRecharge && (
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      充值金额 ($)
                    </label>
                    <select
                      value={settings.billing.rechargeAmount}
                      onChange={(e) => setSettings(prev => ({
                        ...prev,
                        billing: { ...prev.billing, rechargeAmount: parseInt(e.target.value) }
                      }))}
                      className="w-full max-w-xs p-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                    >
                      <option value={25}>$25</option>
                      <option value={50}>$50</option>
                      <option value={100}>$100</option>
                      <option value={200}>$200</option>
                    </select>
                    <div className="mt-3">
                      <Button
                        variant="primary"
                        size="sm"
                        onClick={() => {
                          setPaymentAmount(settings.billing.rechargeAmount);
                          setShowWechatPay(true);
                        }}
                      >
                        立即充值 ${settings.billing.rechargeAmount}
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </Card>

            <Card glass>
              <div>
                <h4 className="font-medium mb-4">付款方式</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                    <span>**** **** **** 1234</span>
                    <Button variant="ghost" size="sm">
                      编辑
                    </Button>
                  </div>
                  <Button variant="glass" size="sm">
                    添加付款方式
                  </Button>
                </div>
              </div>
            </Card>
          </div>
        );

      case 'preferences':
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-xl font-semibold mb-4">偏好设置</h3>
              <p className="text-gray-400 mb-6">自定义您的使用体验</p>
            </div>

            <div className="space-y-6">
              <Card glass>
                <div>
                  <h4 className="font-medium mb-4">界面主题</h4>
                  <div className="flex space-x-4">
                    {[
                      { value: 'dark', label: '深色', icon: Moon },
                      { value: 'light', label: '浅色', icon: Sun },
                    ].map((theme) => (
                      <button
                        key={theme.value}
                        onClick={() => setSettings(prev => ({
                          ...prev,
                          preferences: { ...prev.preferences, theme: theme.value }
                        }))}
                        className={`flex items-center space-x-2 p-3 rounded-lg border transition-colors ${
                          settings.preferences.theme === theme.value
                            ? 'border-purple-500 bg-purple-500/20'
                            : 'border-gray-700 hover:border-gray-600'
                        }`}
                      >
                        <theme.icon className="h-5 w-5" />
                        <span>{theme.label}</span>
                      </button>
                    ))}
                  </div>
                </div>
              </Card>

              <Card glass>
                <div>
                  <h4 className="font-medium mb-4">语言设置</h4>
                  <select
                    value={settings.preferences.language}
                    onChange={(e) => setSettings(prev => ({
                      ...prev,
                      preferences: { ...prev.preferences, language: e.target.value }
                    }))}
                    className="w-full max-w-xs p-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                  >
                    <option value="zh-CN">简体中文</option>
                    <option value="en-US">English</option>
                    <option value="ja-JP">日本語</option>
                  </select>
                </div>
              </Card>

              <Card glass>
                <div>
                  <h4 className="font-medium mb-4">默认音色</h4>
                  <select
                    value={settings.preferences.defaultVoice}
                    onChange={(e) => setSettings(prev => ({
                      ...prev,
                      preferences: { ...prev.preferences, defaultVoice: e.target.value }
                    }))}
                    className="w-full max-w-xs p-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                  >
                    {voices.map((voice) => (
                      <option key={voice.id} value={voice.id}>
                        {voice.name} {voice.isCustom ? '(自定义)' : ''}
                      </option>
                    ))}
                  </select>
                </div>
              </Card>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <Sidebar />
      
      <div className="ml-64 p-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="space-y-8"
        >
          {/* Header */}
          <div>
            <h1 className="text-3xl font-bold mb-2">设置</h1>
            <p className="text-gray-400">管理您的账户和偏好设置</p>
          </div>

          <div className="flex space-x-8">
            {/* Settings Navigation */}
            <div className="w-64 space-y-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${
                    activeTab === tab.id
                      ? 'bg-purple-500/20 text-purple-400 border border-purple-500/30'
                      : 'text-gray-400 hover:text-white hover:bg-gray-800'
                  }`}
                >
                  <tab.icon className="h-5 w-5" />
                  <span className="font-medium">{tab.label}</span>
                </button>
              ))}
            </div>

            {/* Settings Content */}
            <div className="flex-1">
              <Card glass className="p-8">
                {renderTabContent()}
                
                <div className="flex justify-between items-center pt-8 mt-8 border-t border-gray-700">
                  <Button
                    variant="ghost"
                    className="text-red-400 hover:text-red-300"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    删除账户
                  </Button>
                  
                  <div className="flex space-x-3">
                    <Button variant="ghost">
                      取消
                    </Button>
                    <Button 
                      variant="primary" 
                      glow 
                      onClick={handleSave}
                      disabled={saving}
                    >
                      <Save className="h-4 w-4 mr-2" />
                      {saving ? '保存中...' : '保存设置'}
                    </Button>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </motion.div>
      </div>

      {/* 微信支付模态框 */}
      <WechatPayModal
        isOpen={showWechatPay}
        onClose={() => setShowWechatPay(false)}
        amount={paymentAmount * 100} // 转换为分
        description={`SoulVoice 账户充值 $${paymentAmount}`}
        onSuccess={(order: WechatPayOrder) => {
          console.log('支付成功:', order);
          // 这里可以添加成功后的处理逻辑
          // 比如刷新用户余额、显示成功提示等
          alert(`支付成功！充值金额：¥${WechatPayService.formatAmount(order.amount)}`);
        }}
        onError={(error: string) => {
          console.error('支付失败:', error);
          alert(`支付失败：${error}`);
        }}
      />
    </div>
  );
};