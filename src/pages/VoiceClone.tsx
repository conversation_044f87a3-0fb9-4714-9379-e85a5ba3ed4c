import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Upload, 
  Mic, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  Play, 
  Trash2,
  FileAudio,
  Shield,
  Sparkles,
  Loader2
} from 'lucide-react';
import { Sidebar } from '../components/layout/Sidebar';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Input } from '../components/ui/Input';
import { VoiceCloneService, VoiceCloneResponse, VoiceCloneModel } from '../services/voiceCloneService';
import { useAuth } from '../contexts/AuthContext';
import { VoiceModelService } from '../services/voiceModelService';

type Step = 'intro' | 'upload' | 'naming' | 'processing' | 'complete';

interface AudioFile {
  id: string;
  file: File;
  transcription?: string;
}
export const VoiceClone: React.FC = () => {
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState<Step>('intro');
  const [agreed, setAgreed] = useState(false);
  const [files, setFiles] = useState<AudioFile[]>([]);
  const [voiceName, setVoiceName] = useState('');
  const [selectedModel, setSelectedModel] = useState<string>(VoiceCloneService.getDefaultModel().id);
  const [processingProgress, setProcessingProgress] = useState(0);
  const [cloneResult, setCloneResult] = useState<VoiceCloneResponse | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [supportedModels] = useState<VoiceCloneModel[]>(VoiceCloneService.getSupportedModels());

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const uploadedFiles = Array.from(e.target.files || []);
    const audioFiles = uploadedFiles.map(file => ({
      id: Math.random().toString(36).substring(7),
      file: file,
    }));
    setFiles(prev => [...prev, ...audioFiles]);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const droppedFiles = Array.from(e.dataTransfer.files);
    const audioFiles = droppedFiles.map(file => ({
      id: Math.random().toString(36).substring(7),
      file: file,
    }));
    setFiles(prev => [...prev, ...audioFiles]);
  };

  const removeFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
  };

  const startProcessing = async () => {
    if (files.length === 0 || !voiceName.trim()) return;

    setIsProcessing(true);
    setError(null);
    setCurrentStep('processing');
    setProcessingProgress(0);

    try {
      // Use the first file for cloning
      const primaryFile = files[0].file;

      // Validate audio file
      setProcessingProgress(10);
      const validation = VoiceCloneService.validateAudioFile(primaryFile);
      if (!validation.valid) {
        throw new Error(validation.error);
      }

      // Get transcription (in real app, you might want user to provide this)
      setProcessingProgress(20);
      const transcription = files[0].transcription || '请在此输入音频对应的文字内容';

      // Call the voice cloning API
      setProcessingProgress(40);
      const result = await VoiceCloneService.cloneVoice({
        displayName: voiceName,
        audioFile: primaryFile,
        text: transcription,
        model: selectedModel,
      });

      setProcessingProgress(80);

      if (result.status === 'success') {
        // 保存语音模型到数据库
        if (user) {
          await VoiceModelService.createVoiceModel(
            user.id,
            result.displayName,
            result.uri,
            result.customName
          );
        }

        setCloneResult(result);
        setProcessingProgress(100);
        setTimeout(() => {
          setCurrentStep('complete');
        }, 1000);
      } else {
        throw new Error(result.message || 'Voice cloning failed');
      }
    } catch (err) {
      console.error('Voice cloning error:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      setCurrentStep('upload'); // Go back to upload step
    } finally {
      setIsProcessing(false);
    }
  };

  const updateTranscription = (fileId: string, transcription: string) => {
    setFiles(prev => prev.map(file => 
      file.id === fileId ? { ...file, transcription } : file
    ));
  };

  const renderStep = () => {
    switch (currentStep) {
      case 'intro':
        return (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="max-w-2xl mx-auto space-y-8"
          >
            <div className="text-center space-y-4">
              <div className="inline-flex p-4 bg-purple-500/20 rounded-full">
                <Mic className="h-8 w-8 text-purple-500" />
              </div>
              <h2 className="text-2xl font-bold">创建您的专属声音</h2>
              <p className="text-gray-400">
                仅需 15 秒无背景噪音的音频，即可克隆您的声音
              </p>
            </div>

            <Card glass className="p-6">
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <Shield className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold text-green-400">隐私保护</h4>
                    <p className="text-sm text-gray-400">
                      您的音频数据经过加密存储，仅用于生成声音模型，不会被用于其他目的
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <Clock className="h-5 w-5 text-blue-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold text-blue-400">快速处理</h4>
                    <p className="text-sm text-gray-400">
                      通常在 1-2 分钟内完成克隆，您会收到邮件通知
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <Sparkles className="h-5 w-5 text-purple-500 mt-0.5" />
                  <div>
                    <h4 className="font-semibold text-purple-400">高质量输出</h4>
                    <p className="text-sm text-gray-400">
                      支持多种情感表达，音质接近原声
                    </p>
                  </div>
                </div>
              </div>
            </Card>

            {/* 模型介绍 */}
            <Card glass className="p-6">
              <h3 className="text-lg font-semibold mb-4 text-center">支持的克隆模型</h3>
              <div className="grid gap-4">
                {supportedModels.map((model) => (
                  <div
                    key={model.id}
                    className={`p-4 rounded-lg border ${
                      model.isDefault
                        ? 'bg-purple-500/10 border-purple-500/30'
                        : 'bg-gray-800/50 border-gray-700'
                    }`}
                  >
                    <div className="flex items-center space-x-2 mb-2">
                      <h4 className="font-medium text-white">{model.name}</h4>
                      {model.isDefault && (
                        <span className="px-2 py-1 bg-purple-500/20 text-purple-400 text-xs rounded-full">
                          默认推荐
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-400 mb-3">{model.description}</p>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                      {model.features.map((feature, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                          <span className="text-xs text-gray-300">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            <Card glass className="p-6">
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="agreement"
                    checked={agreed}
                    onChange={(e) => setAgreed(e.target.checked)}
                    className="w-4 h-4 accent-purple-500"
                  />
                  <label htmlFor="agreement" className="text-sm text-gray-300">
                    我确认我拥有此声音的合法使用权，并承诺不将其用于任何非法或不道德的活动
                  </label>
                </div>

                <div className="text-xs text-gray-500">
                  通过使用此服务，您同意我们的{' '}
                  <a href="#" className="text-purple-400 hover:underline">服务条款</a>
                  {' '}和{' '}
                  <a href="#" className="text-purple-400 hover:underline">隐私政策</a>
                </div>
              </div>
            </Card>

            <div className="text-center">
              <Button
                variant="primary"
                size="lg"
                glow
                disabled={!agreed}
                onClick={() => setCurrentStep('upload')}
                className="px-8"
              >
                我同意，开始克隆
              </Button>
            </div>
          </motion.div>
        );

      case 'upload':
        return (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="max-w-2xl mx-auto space-y-8"
          >
            <div className="text-center space-y-4">
              <h2 className="text-2xl font-bold">上传音频文件</h2>
              <p className="text-gray-400">
                请上传 1-3 段，总时长超过 15 秒的 WAV 或 MP3 文件
              </p>
            </div>

            <Card glass className="p-6">
              <div
                className="border-2 border-dashed border-gray-600 rounded-lg p-8 text-center hover:border-purple-500 transition-colors cursor-pointer"
                onDrop={handleDrop}
                onDragOver={(e) => e.preventDefault()}
                onClick={() => document.getElementById('file-input')?.click()}
              >
                <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-lg font-medium mb-2">点击上传或拖拽文件到此处</p>
                <p className="text-sm text-gray-400">
                  支持 WAV, MP3 格式，单个文件最大 10MB
                </p>
                <input
                  id="file-input"
                  type="file"
                  accept="audio/*"
                  multiple
                  onChange={handleFileUpload}
                  className="hidden"
                />
              </div>
            </Card>

            {files.length > 0 && (
              <Card glass className="p-6">
                <h3 className="text-lg font-semibold mb-4">已上传文件</h3>
                <div className="space-y-3">
                  {files.map((file, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <FileAudio className="h-5 w-5 text-purple-500" />
                        <div>
                          <p className="font-medium">{file.file.name}</p>
                          <p className="text-sm text-gray-400">
                            {(file.file.size / 1024 / 1024).toFixed(2)} MB
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button variant="ghost" size="sm">
                          <Play className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => removeFile(index)}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
                
                {/* Transcription input for the first file */}
                {files.length > 0 && (
                  <div className="mt-4 p-4 bg-gray-800 rounded-lg">
                    <h4 className="font-medium mb-2">音频文字内容</h4>
                    <p className="text-sm text-gray-400 mb-3">
                      请输入 "{files[0].file.name}" 的文字内容，这将帮助提高克隆质量
                    </p>
                    <textarea
                      className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white resize-none focus:outline-none focus:ring-2 focus:ring-purple-500"
                      rows={3}
                      placeholder="请输入音频对应的文字内容..."
                      value={files[0].transcription || ''}
                      onChange={(e) => updateTranscription(files[0].id, e.target.value)}
                    />
                  </div>
                )}
              </Card>
            )}

            {error && (
              <div className="bg-red-900/20 border border-red-600/30 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <AlertCircle className="h-5 w-5 text-red-500 mt-0.5" />
                  <div className="text-sm">
                    <p className="font-medium text-red-400">处理失败</p>
                    <p className="text-gray-300 mt-1">{error}</p>
                  </div>
                </div>
              </div>
            )}

            <div className="bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <AlertCircle className="h-5 w-5 text-yellow-500 mt-0.5" />
                <div className="text-sm">
                  <p className="font-medium text-yellow-400">录音建议</p>
                  <ul className="mt-2 space-y-1 text-gray-300">
                    <li>• 确保录音清晰，无背景噪音</li>
                    <li>• 使用稳定的语调，避免过度情绪化</li>
                    <li>• 包含不同的语句和语调变化</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="flex justify-between">
              <Button variant="ghost" onClick={() => setCurrentStep('intro')}>
                返回上一步
              </Button>
              <Button
                variant="primary"
                size="lg"
                glow
                disabled={files.length === 0 || !files[0]?.transcription?.trim()}
                onClick={() => setCurrentStep('naming')}
              >
                下一步：命名
              </Button>
            </div>
          </motion.div>
        );

      case 'naming':
        return (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="max-w-2xl mx-auto space-y-8"
          >
            <div className="text-center space-y-4">
              <h2 className="text-2xl font-bold">为您的声音命名</h2>
              <p className="text-gray-400">
                为这个新的声音模型起一个名字，方便后续使用
              </p>
            </div>

            <Card glass className="p-6">
              <div className="space-y-6">
                <Input
                  label="声音名称"
                  placeholder="例如：我的专属声音"
                  value={voiceName}
                  onChange={(e) => setVoiceName(e.target.value)}
                />

                {/* 模型选择 */}
                <div className="space-y-3">
                  <label className="block text-sm font-medium text-gray-300">
                    选择克隆模型
                  </label>
                  <div className="grid gap-3">
                    {supportedModels.map((model) => (
                      <div
                        key={model.id}
                        className={`p-4 rounded-lg border cursor-pointer transition-all duration-200 ${
                          selectedModel === model.id
                            ? 'bg-purple-500/20 border-purple-500/50'
                            : 'bg-gray-800 border-gray-700 hover:bg-gray-700'
                        }`}
                        onClick={() => setSelectedModel(model.id)}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <h4 className="font-medium text-white">{model.name}</h4>
                              {model.isDefault && (
                                <span className="px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded-full">
                                  推荐
                                </span>
                              )}
                            </div>
                            <p className="text-sm text-gray-400 mt-1">{model.description}</p>
                            <div className="flex flex-wrap gap-1 mt-2">
                              {model.features.slice(0, 3).map((feature, index) => (
                                <span
                                  key={index}
                                  className="px-2 py-1 bg-gray-700 text-gray-300 text-xs rounded"
                                >
                                  {feature}
                                </span>
                              ))}
                              {model.features.length > 3 && (
                                <span className="px-2 py-1 bg-gray-700 text-gray-300 text-xs rounded">
                                  +{model.features.length - 3} 更多
                                </span>
                              )}
                            </div>
                          </div>
                          <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                            selectedModel === model.id
                              ? 'border-purple-500 bg-purple-500'
                              : 'border-gray-600'
                          }`}>
                            {selectedModel === model.id && (
                              <div className="w-2 h-2 bg-white rounded-full"></div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="text-sm text-gray-400">
                  建议使用容易识别的名称，您之后可以在声音实验室中找到它
                </div>
              </div>
            </Card>

            <Card glass className="p-6">
              <h3 className="text-lg font-semibold mb-4">克隆摘要</h3>
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">选择的模型</span>
                  <span className="text-right">
                    {VoiceCloneService.getModelById(selectedModel)?.name || selectedModel}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">上传文件数量</span>
                  <span>{files.length} 个</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">总文件大小</span>
                  <span>{(files.reduce((sum, file) => sum + file.file.size, 0) / 1024 / 1024).toFixed(2)} MB</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">文字内容</span>
                  <span className="text-right max-w-xs truncate">{files[0]?.transcription || '未提供'}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">预计处理时间</span>
                  <span>1-2 分钟</span>
                </div>
              </div>
            </Card>

            <div className="flex justify-between">
              <Button variant="ghost" onClick={() => setCurrentStep('upload')}>
                返回上一步
              </Button>
              <Button
                variant="primary"
                size="lg"
                glow
                disabled={!voiceName.trim()}
                onClick={startProcessing}
              >
                开始克隆
              </Button>
            </div>
          </motion.div>
        );

      case 'processing':
        return (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="max-w-2xl mx-auto space-y-8 text-center"
          >
            <div className="space-y-4">
              <div className="inline-flex p-4 bg-purple-500/20 rounded-full">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                >
                  {isProcessing ? (
                    <Loader2 className="h-8 w-8 text-purple-500" />
                  ) : (
                    <Sparkles className="h-8 w-8 text-purple-500" />
                  )}
                </motion.div>
              </div>
              <h2 className="text-2xl font-bold">克隆正在进行中</h2>
              <p className="text-gray-400">
                {isProcessing ? '正在处理您的音频文件...' : '我们正在分析您的声音特征，大约需要 1-2 分钟'}
              </p>
            </div>

            <Card glass className="p-6">
              <div className="space-y-4">
                <div className="flex justify-between text-sm">
                  <span>处理进度</span>
                  <span>{Math.round(processingProgress)}%</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-500"
                    style={{ width: `${processingProgress}%` }}
                  />
                </div>
                <div className="text-xs text-gray-400">
                  {isProcessing ? '请耐心等待，不要关闭此页面' : '完成后我们会通过邮件通知您，您也可以在此页面等待'}
                </div>
              </div>
            </Card>

            <div className="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <Clock className="h-5 w-5 text-blue-500 mt-0.5" />
                <div className="text-sm text-left">
                  <p className="font-medium text-blue-400">处理阶段</p>
                  <ul className="mt-2 space-y-1 text-gray-300">
                    <li>• 音频预处理和降噪</li>
                    <li>• 声音特征提取</li>
                    <li>• 神经网络模型训练</li>
                    <li>• 质量验证和优化</li>
                  </ul>
                </div>
              </div>
            </div>
          </motion.div>
        );

      case 'complete':
        return (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="max-w-2xl mx-auto space-y-8 text-center"
          >
            <div className="space-y-4">
              <div className="inline-flex p-4 bg-green-500/20 rounded-full">
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
              <h2 className="text-2xl font-bold">克隆成功！</h2>
              <p className="text-gray-400">
                您的专属声音 "{voiceName}" 已经准备就绪
              </p>
            </div>

            <Card glass className="p-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Mic className="h-5 w-5 text-purple-500" />
                    <div className="text-left">
                      <p className="font-medium">{cloneResult?.displayName || voiceName}</p>
                      <p className="text-sm text-gray-400">
                        {cloneResult?.customName ? `API ID: ${cloneResult.customName}` : '刚刚创建'}
                      </p>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm">
                    <Play className="h-4 w-4" />
                  </Button>
                </div>
                
                <div className="text-sm text-gray-400">
                  您现在可以在声音实验室中使用这个声音进行语音合成
                </div>
                
                {cloneResult?.uri && (
                  <div className="mt-4 space-y-3">
                    <div className="p-3 bg-gray-800 rounded-lg">
                      <p className="text-xs text-gray-500 mb-1">音色 URI（用于 API 调用）:</p>
                      <code className="text-sm text-green-400 break-all">{cloneResult.uri}</code>
                    </div>
                    {cloneResult.customName && (
                      <div className="p-3 bg-gray-800 rounded-lg">
                        <p className="text-xs text-gray-500 mb-1">API 标识符:</p>
                        <code className="text-sm text-blue-400">{cloneResult.customName}</code>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </Card>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="primary" size="lg" glow>
                在声音实验室中使用
              </Button>
              <Button variant="glass" size="lg">
                管理我的声音
              </Button>
            </div>
          </motion.div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <Sidebar />
      
      <div className="ml-64 p-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="space-y-8"
        >
          {/* Header */}
          <div className="text-center">
            <h1 className="text-3xl font-bold mb-2">语音克隆</h1>
            <p className="text-gray-400">创建您的专属 AI 声音</p>
          </div>

          {/* Progress Steps */}
          <div className="flex justify-center mb-8">
            <div className="flex items-center space-x-4">
              {[
                { key: 'intro', label: '授权确认' },
                { key: 'upload', label: '上传音频' },
                { key: 'naming', label: '命名确认' },
                { key: 'processing', label: '处理中' },
                { key: 'complete', label: '完成' }
              ].map((step, index) => (
                <div key={step.key} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    currentStep === step.key
                      ? 'bg-purple-500 text-white'
                      : index < ['intro', 'upload', 'naming', 'processing', 'complete'].indexOf(currentStep)
                      ? 'bg-green-500 text-white'
                      : 'bg-gray-700 text-gray-400'
                  }`}>
                    {index < ['intro', 'upload', 'naming', 'processing', 'complete'].indexOf(currentStep) ? (
                      <CheckCircle className="h-4 w-4" />
                    ) : (
                      index + 1
                    )}
                  </div>
                  <span className="ml-2 text-sm text-gray-400">{step.label}</span>
                  {index < 4 && (
                    <div className="w-8 h-px bg-gray-700 mx-4" />
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Step Content */}
          <div className="min-h-[600px]">
            {renderStep()}
          </div>
        </motion.div>
      </div>
    </div>
  );
};