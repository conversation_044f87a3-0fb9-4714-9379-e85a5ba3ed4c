import React, { useState, useEffect } from 'react';
import { VoiceService, type Voice, type CreateVoiceRequest } from '../services/voiceService';
import { useAuth } from '../contexts/AuthContext';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Volume2, Plus, Edit, Trash2, Save, X, Copy, CheckCircle } from 'lucide-react';

export const VoiceManagement: React.FC = () => {
  const { user } = useAuth();
  const [voices, setVoices] = useState<Voice[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editingVoice, setEditingVoice] = useState<Voice | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [copiedUri, setCopiedUri] = useState<string | null>(null);
  const [formData, setFormData] = useState<CreateVoiceRequest>({
    name: '',
    description: '',
    uri: '',
    voice_type: 'user_custom',
    gender: 'female',
    language: 'zh-CN',
    preview_text: '',
  });

  useEffect(() => {
    loadVoices();
  }, [user]);

  const loadVoices = async () => {
    try {
      setLoading(true);
      setError(null);
      const voiceList = await VoiceService.getAllVoices(user?.id);
      setVoices(voiceList);
    } catch (err) {
      console.error('Error loading voices:', err);
      setError('加载音色列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = async () => {
    if (!user) return;

    try {
      setError(null);
      await VoiceService.createVoice(user.id, formData);
      setShowCreateForm(false);
      setFormData({
        name: '',
        description: '',
        uri: '',
        voice_type: 'user_custom',
        gender: 'female',
        language: 'zh-CN',
        preview_text: '',
      });
      await loadVoices();
    } catch (err) {
      console.error('Error creating voice:', err);
      setError('创建音色失败');
    }
  };

  const handleUpdate = async () => {
    if (!user || !editingVoice) return;

    try {
      setError(null);
      await VoiceService.updateVoice(editingVoice.id, user.id, formData);
      setEditingVoice(null);
      await loadVoices();
    } catch (err) {
      console.error('Error updating voice:', err);
      setError('更新音色失败');
    }
  };

  const handleDelete = async (voiceId: string) => {
    if (!user) return;

    const voice = voices.find(v => v.id === voiceId);
    if (!voice) return;

    const confirmMessage = `确定要删除音色 "${voice.name}" 吗？\n\n这将同时删除数据库记录和 SiliconFlow 平台上的音色数据。\n此操作无法撤销！`;

    if (!confirm(confirmMessage)) return;

    try {
      setError(null);
      await VoiceService.deleteVoice(voiceId, user.id);
      await loadVoices();
    } catch (err) {
      console.error('Error deleting voice:', err);
      setError(err instanceof Error ? err.message : '删除音色失败');
    }
  };

  const startEdit = (voice: Voice) => {
    setEditingVoice(voice);
    setFormData({
      name: voice.name,
      description: voice.description || '',
      uri: voice.uri,
      voice_type: voice.voice_type,
      gender: voice.gender,
      language: voice.language,
      preview_text: voice.preview_text || '',
    });
  };

  const cancelEdit = () => {
    setEditingVoice(null);
    setShowCreateForm(false);
    setFormData({
      name: '',
      description: '',
      uri: '',
      voice_type: 'user_custom',
      gender: 'female',
      language: 'zh-CN',
      preview_text: '',
    });
  };

  const handleCopyUri = (uri: string, voiceId: string) => {
    navigator.clipboard.writeText(uri);
    setCopiedUri(voiceId);
    setTimeout(() => setCopiedUri(null), 2000);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 p-6">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">音色管理</h1>
          <p className="text-gray-300">管理系统音色和用户自定义音色</p>
        </div>

        {error && (
          <div className="mb-6 p-4 bg-red-500/20 border border-red-500/50 rounded-lg text-red-400">
            {error}
          </div>
        )}

        {/* 创建/编辑表单 */}
        {(showCreateForm || editingVoice) && (
          <Card glass className="mb-6">
            <h2 className="text-xl font-semibold text-white mb-4">
              {editingVoice ? '编辑音色' : '创建音色'}
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  音色名称 *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="输入音色名称"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  URI *
                </label>
                <input
                  type="text"
                  value={formData.uri}
                  onChange={(e) => setFormData({ ...formData, uri: e.target.value })}
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="speech:xxx:xxx:xxx"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  音色类型
                </label>
                <select
                  value={formData.voice_type}
                  onChange={(e) => setFormData({ ...formData, voice_type: e.target.value as any })}
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                  disabled={editingVoice?.voice_type === 'system'}
                >
                  <option value="user_custom">用户自定义</option>
                  <option value="cloned">克隆音色</option>
                  {editingVoice?.voice_type === 'system' && (
                    <option value="system">系统音色</option>
                  )}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  性别
                </label>
                <select
                  value={formData.gender || ''}
                  onChange={(e) => setFormData({ ...formData, gender: e.target.value as any })}
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  <option value="">未指定</option>
                  <option value="female">女性</option>
                  <option value="male">男性</option>
                  <option value="neutral">中性</option>
                </select>
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  描述
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                  rows={2}
                  placeholder="音色描述"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  预览文本
                </label>
                <input
                  type="text"
                  value={formData.preview_text}
                  onChange={(e) => setFormData({ ...formData, preview_text: e.target.value })}
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="你好，我是..."
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <Button variant="ghost" onClick={cancelEdit}>
                <X className="h-4 w-4 mr-2" />
                取消
              </Button>
              <Button 
                onClick={editingVoice ? handleUpdate : handleCreate}
                disabled={!formData.name || !formData.uri}
              >
                <Save className="h-4 w-4 mr-2" />
                {editingVoice ? '更新' : '创建'}
              </Button>
            </div>
          </Card>
        )}

        {/* 音色列表 */}
        <Card glass>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-white">音色列表</h2>
            <Button onClick={() => setShowCreateForm(true)} disabled={showCreateForm || editingVoice}>
              <Plus className="h-4 w-4 mr-2" />
              添加音色
            </Button>
          </div>

          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
            </div>
          ) : voices.length === 0 ? (
            <div className="text-center py-8">
              <Volume2 className="h-12 w-12 text-gray-600 mx-auto mb-3" />
              <p className="text-gray-400">暂无音色</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-700">
                    <th className="text-left py-3 px-4 text-gray-300">名称</th>
                    <th className="text-left py-3 px-4 text-gray-300">类型</th>
                    <th className="text-left py-3 px-4 text-gray-300">性别</th>
                    <th className="text-left py-3 px-4 text-gray-300">语言</th>
                    <th className="text-left py-3 px-4 text-gray-300">URI</th>
                    <th className="text-left py-3 px-4 text-gray-300">操作</th>
                  </tr>
                </thead>
                <tbody>
                  {voices.map((voice) => (
                    <tr key={voice.id} className="border-b border-gray-800 hover:bg-gray-800/50">
                      <td className="py-3 px-4">
                        <div>
                          <p className="text-white font-medium">{voice.name}</p>
                          {voice.description && (
                            <p className="text-sm text-gray-400">{voice.description}</p>
                          )}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          voice.voice_type === 'system' 
                            ? 'bg-blue-500/20 text-blue-400'
                            : voice.voice_type === 'cloned'
                            ? 'bg-green-500/20 text-green-400'
                            : 'bg-yellow-500/20 text-yellow-400'
                        }`}>
                          {voice.voice_type === 'system' ? '系统' : 
                           voice.voice_type === 'cloned' ? '克隆' : '自定义'}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-gray-300">
                        {voice.gender === 'female' ? '女性' : 
                         voice.gender === 'male' ? '男性' : 
                         voice.gender === 'neutral' ? '中性' : '-'}
                      </td>
                      <td className="py-3 px-4 text-gray-300">{voice.language}</td>
                      <td className="py-3 px-4">
                        <code className="text-xs text-gray-400 bg-gray-800 px-2 py-1 rounded">
                          {voice.uri.length > 30 ? `${voice.uri.substring(0, 30)}...` : voice.uri}
                        </code>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleCopyUri(voice.uri, voice.id)}
                            className={copiedUri === voice.id ? 'text-green-400' : 'text-blue-400 hover:text-blue-300'}
                            title={copiedUri === voice.id ? '已复制 URI!' : '复制音色 URI'}
                          >
                            {copiedUri === voice.id ? (
                              <CheckCircle className="h-4 w-4" />
                            ) : (
                              <Copy className="h-4 w-4" />
                            )}
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => startEdit(voice)}
                            disabled={editingVoice !== null || showCreateForm}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          {voice.voice_type !== 'system' && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDelete(voice.id)}
                              className="text-red-400 hover:text-red-300"
                              disabled={editingVoice !== null || showCreateForm}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </Card>
      </div>
    </div>
  );
};
