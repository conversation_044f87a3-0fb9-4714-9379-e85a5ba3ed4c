import React, { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Play, Code, Zap, Heart, Star, Mic } from 'lucide-react';
import { Navbar } from '../components/layout/Navbar';
import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import { ParticleWave } from '../components/animations/ParticleWave';
import { WaveBackground } from '../components/animations/WaveBackground';
import { TTSService } from '../services/ttsService';
import { useAuth } from '../contexts/AuthContext';

export const LandingPage: React.FC = () => {
  const { isAuthenticated } = useAuth();
  const [voices, setVoices] = useState([
    { id: 'zh-CN-XiaoxiaoNeural', name: '温柔知性女声' },
    { id: 'zh-CN-YunxiNeural', name: '沉稳磁性男声' },
    { id: 'zh-CN-XiaoyiNeural', name: '活泼青春女声' },
    { id: 'zh-CN-YunjianNeural', name: '专业播音男声' },
  ]);
  const [selectedVoice, setSelectedVoice] = useState('zh-CN-YunxiNeural');

  useEffect(() => {
    // 异步加载音色列表，但不阻塞页面渲染
    const loadVoices = async () => {
      try {
        const voiceList = await TTSService.getSupportedVoices();
        if (voiceList.length > 0) {
          setVoices(voiceList.filter(v => !v.isCustom)); // 只显示内置音色
        }
      } catch (error) {
        console.error('Error loading voices:', error);
        // 保持默认音色列表
      }
    };

    loadVoices();
  }, []);

  return (
    <div className="min-h-screen bg-gray-900 text-white overflow-hidden">
      <Navbar />
      
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center">
        <ParticleWave />
        <WaveBackground />
        
        <div className="relative z-10 text-center max-w-4xl mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-8"
          >
            <h1 className="text-6xl md:text-8xl font-bold">
              <span className="bg-gradient-to-r from-purple-400 via-pink-500 to-blue-500 bg-clip-text text-transparent">
                赋予每个应用
              </span>
              <br />
              <span className="bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent">
                说话的灵魂
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-gray-300 max-w-2xl mx-auto">
              SoulVoice 提供业界最先进的语音合成与克隆技术，让您的应用拥有真正的声音表现力
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link to={isAuthenticated ? "/dashboard" : "/login"}>
                <Button variant="primary" size="lg" glow className="px-8 py-4">
                  <Play className="h-5 w-5 mr-2" />
                  免费生成 10,000 字符
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold mb-6">为什么选择 SoulVoice？</h2>
            <p className="text-xl text-gray-400">三大核心优势，让您的应用脱颖而出</p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                icon: Zap,
                title: '极致易用',
                description: '一行代码即可集成，3分钟完成部署。支持 REST API、SDK 和 Webhook，开发者友好。',
                color: 'from-yellow-400 to-orange-500'
              },
              {
                icon: Heart,
                title: '超高表现力',
                description: '业界领先的情感表达技术，支持 20+ 种情感风格，让每个字都充满生命力。',
                color: 'from-pink-400 to-red-500'
              },
              {
                icon: Star,
                title: '颠覆性性价比',
                description: '相比传统方案节省 70% 成本，提供免费额度和灵活的按需计费模式。',
                color: 'from-purple-400 to-blue-500'
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ scale: 1.05 }}
                className="group"
              >
                <Card glass glow className="h-full text-center hover:bg-white/10 transition-all duration-300">
                  <div className={`inline-flex p-4 rounded-full bg-gradient-to-r ${feature.color} mb-6`}>
                    <feature.icon className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold mb-4">{feature.title}</h3>
                  <p className="text-gray-400">{feature.description}</p>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Interactive Demo */}
      <section className="py-20 px-4 bg-gray-800/50">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-4xl font-bold mb-6">立即体验 SoulVoice</h2>
            <p className="text-xl text-gray-400">输入文本，选择音色，感受真正的声音魅力</p>
          </motion.div>

          <Card glass className="p-8">
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  输入文本
                </label>
                <textarea
                  className="w-full p-4 bg-gray-800 border border-gray-700 rounded-lg text-white resize-none focus:outline-none focus:ring-2 focus:ring-purple-500"
                  rows={4}
                  placeholder="输入您想要合成的文本，比如：你好，SoulVoice！"
                  defaultValue="你好，欢迎使用 SoulVoice！我是您的专属 AI 语音助手。"
                />
              </div>

              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    音色选择
                  </label>
                  <select
                    value={selectedVoice}
                    onChange={(e) => setSelectedVoice(e.target.value)}
                    className="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                  >
                    {voices.map((voice) => (
                      <option key={voice.id} value={voice.id}>
                        {voice.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    情感风格
                  </label>
                  <select className="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500">
                    <option>自然</option>
                    <option>喜悦</option>
                    <option>温暖</option>
                    <option>专业</option>
                  </select>
                </div>
              </div>

              <div className="text-center">
                <Button variant="primary" size="lg" glow className="px-8">
                  <Play className="h-5 w-5 mr-2" />
                  生成语音
                </Button>
              </div>
            </div>
          </Card>
        </div>
      </section>

      {/* Code Example */}
      <section className="py-20 px-4">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-4xl font-bold mb-6">开发者友好的 API</h2>
            <p className="text-xl text-gray-400">简单到只需一行代码</p>
          </motion.div>

          <Card glass className="p-8">
            <div className="space-y-4">
              <div className="flex space-x-4 border-b border-gray-700 pb-4">
                <button className="px-4 py-2 bg-purple-500 text-white rounded-lg text-sm">
                  Python
                </button>
                <button className="px-4 py-2 text-gray-400 hover:text-white text-sm">
                  JavaScript
                </button>
                <button className="px-4 py-2 text-gray-400 hover:text-white text-sm">
                  cURL
                </button>
              </div>

              <div className="bg-gray-900 rounded-lg p-6 font-mono text-sm">
                <pre className="text-green-400">
{`import requests

# 生成语音
response = requests.post('https://api.soulvoice.com/v1/tts', {
    'text': '你好，SoulVoice！',
    'voice': 'zh-CN-XiaoxiaoNeural',
    'emotion': 'happy',
    'speed': 1.0
}, headers={'Authorization': 'Bearer YOUR_API_KEY'})

# 获取音频文件
audio_data = response.content`}
                </pre>
              </div>

              <div className="text-center">
                <Button variant="glass" className="px-6">
                  <Code className="h-4 w-4 mr-2" />
                  复制代码
                </Button>
              </div>
            </div>
          </Card>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-gradient-to-r from-purple-900/20 to-blue-900/20">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="space-y-8"
          >
            <h2 className="text-4xl font-bold">
              准备好让您的应用开口说话了吗？
            </h2>
            <p className="text-xl text-gray-400">
              加入数千名开发者的行列，开始使用 SoulVoice
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to={isAuthenticated ? "/dashboard" : "/login"}>
                <Button variant="primary" size="lg" glow className="px-8 py-4">
                  立即免费开始
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 border-t border-gray-800 py-12 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <Mic className="h-6 w-6 text-purple-500" />
                <span className="text-lg font-bold">SoulVoice</span>
              </div>
              <p className="text-gray-400 text-sm">
                赋予每个应用说话的灵魂
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold mb-4">产品</h4>
              <ul className="space-y-2 text-sm text-gray-400">
                <li><a href="#" className="hover:text-white">语音合成</a></li>
                <li><a href="#" className="hover:text-white">语音克隆</a></li>
                <li><a href="#" className="hover:text-white">API 文档</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-4">支持</h4>
              <ul className="space-y-2 text-sm text-gray-400">
                <li><a href="#" className="hover:text-white">帮助中心</a></li>
                <li><a href="#" className="hover:text-white">联系我们</a></li>
                <li><a href="#" className="hover:text-white">状态页面</a></li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-4">公司</h4>
              <ul className="space-y-2 text-sm text-gray-400">
                <li><a href="#" className="hover:text-white">关于我们</a></li>
                <li><a href="#" className="hover:text-white">隐私政策</a></li>
                <li><a href="#" className="hover:text-white">服务条款</a></li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400 text-sm">
            <p>&copy; 2024 SoulVoice. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};