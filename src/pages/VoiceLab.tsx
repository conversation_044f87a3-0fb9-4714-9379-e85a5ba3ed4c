import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { Play, Pause, Download, Copy, Code, Volume2, Sliders, AudioWaveform as Waveform, CheckCircle, AlertCircle, RefreshCw, Trash2, AlertTriangle, ChevronDown, ChevronUp } from 'lucide-react';
import { Sidebar } from '../components/layout/Sidebar';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { AudioPlayer } from '../components/ui/AudioPlayer';
import { useAuth } from '../contexts/AuthContext';
import { VoiceModelService } from '../services/voiceModelService';
import { UsageService } from '../services/usageService';
import { TTSService } from '../services/ttsService';
import { VoiceService } from '../services/voiceService';
import { SubscriptionService } from '../services/subscriptionService';
import { VoiceInitializer } from '../utils/initializeVoices';
import { VoiceDebugger } from '../utils/voiceDebugger';

export const VoiceLab: React.FC = () => {
  const { user } = useAuth();
  const [selectedVoice, setSelectedVoice] = useState('speech:mcg3fdnx:clzkyf4vy00e5qr6hywum4u84:bzznlkuhcjzpbosexitr');
  const [text, setText] = useState('里特并非传统意义上的特工，他的武器不是枪，而是大脑，他的战场不是街头，而是数据流，他精通各种加密算法，能轻易穿透防火墙，对他而言，代码就是语言，漏洞就是破绽，他需要一个突破口');
  const [emotion, setEmotion] = useState('natural');
  const [speed, setSpeed] = useState(1.0);
  const [pitch, setPitch] = useState(1.0);

  const [showCode, setShowCode] = useState(false);
  const [activeTab, setActiveTab] = useState('python');
  const [copied, setCopied] = useState(false);
  const [showDeveloperTools, setShowDeveloperTools] = useState(false);

  // 新增的高级参数状态
  const [speechSpeed, setSpeechSpeed] = useState(1.0);
  const [speechGain, setSpeechGain] = useState(0.0);
  const [responseFormat, setResponseFormat] = useState('mp3');
  const [sampleRate, setSampleRate] = useState(44100);
  const [showAdvancedParams, setShowAdvancedParams] = useState(false);
  const [userVoices, setUserVoices] = useState([]);
  const [voices, setVoices] = useState([]);
  const [loadingVoices, setLoadingVoices] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [deletingVoice, setDeletingVoice] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<{ voiceId: string; voiceName: string } | null>(null);
  const [copiedUri, setCopiedUri] = useState<string | null>(null);

  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 参数预设
  const paramPresets = {
    default: {
      speed: 1.0,
      gain: 0.0,
      response_format: 'mp3',
      sample_rate: 44100
    },
    slow_clear: {
      speed: 0.8,
      gain: 2.0,
      response_format: 'wav',
      sample_rate: 44100
    },
    fast_compact: {
      speed: 1.3,
      gain: -1.0,
      response_format: 'mp3',
      sample_rate: 32000
    },
    high_quality: {
      speed: 1.0,
      gain: 1.0,
      response_format: 'wav',
      sample_rate: 44100
    },
    podcast: {
      speed: 1.1,
      gain: 0.5,
      response_format: 'mp3',
      sample_rate: 44100
    }
  };

  // 验证高级参数
  const validateAdvancedParams = () => {
    if (speechSpeed < 0.25 || speechSpeed > 4.0) {
      return { valid: false, error: '语音速度必须在 0.25 到 4.0 之间' };
    }
    if (speechGain < -10 || speechGain > 10) {
      return { valid: false, error: '音频增益必须在 -10dB 到 +10dB 之间' };
    }

    const validCombinations = {
      'mp3': [32000, 44100],
      'wav': [8000, 16000, 24000, 32000, 44100],
      'opus': [48000],
      'pcm': [8000, 16000, 24000, 32000, 44100]
    };

    if (!validCombinations[responseFormat] || !validCombinations[responseFormat].includes(sampleRate)) {
      return { valid: false, error: `${responseFormat.toUpperCase()} 格式不支持 ${sampleRate}Hz 采样率` };
    }

    return { valid: true };
  };

  // 应用参数预设
  const applyPreset = (preset: any) => {
    setSpeechSpeed(preset.speed);
    setSpeechGain(preset.gain);
    setResponseFormat(preset.response_format);
    setSampleRate(preset.sample_rate);
  };

  // 重置为默认参数
  const resetToDefaults = () => {
    applyPreset(paramPresets.default);
  };

  useEffect(() => {
    console.log('[VoiceLab] useEffect triggered', { user: user?.id, loadingVoices });
    // 使用 setTimeout 确保状态更新完成后再调用
    const timer = setTimeout(() => {
      loadAllVoices(true); // 强制刷新，确保一定会执行
    }, 100);

    return () => clearTimeout(timer);
  }, [user]);

  // 清理音频 URL 和定时器
  useEffect(() => {
    return () => {
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
    };
  }, [audioUrl]);

  const loadAllVoices = async (forceRefresh: boolean = false) => {
    console.log('[VoiceLab] loadAllVoices called', { forceRefresh, loadingVoices, user: user?.id });

    // 防抖：如果正在加载，取消之前的请求
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
      loadingTimeoutRef.current = null;
    }

    // 如果不是强制刷新且正在加载中，直接返回
    if (!forceRefresh && loadingVoices) {
      console.log('[VoiceLab] Already loading, skipping', { loadingVoices, forceRefresh });
      return;
    }

    try {
      console.log('[VoiceLab] Starting to load voices');
      setLoadingVoices(true);
      setError(null);

      // 如果是强制刷新，清除缓存
      if (forceRefresh) {
        console.log('[VoiceLab] Force refresh, clearing caches');
        TTSService.clearVoiceCache(user?.id);
        VoiceService.clearCache(user?.id);
        if (user) {
          VoiceModelService.clearCache(user.id);
        }
      }

      console.log('[VoiceLab] Loading voices and voice models...');

      // 并行加载系统音色和用户音色
      const [systemVoices, userVoiceModels] = await Promise.all([
        TTSService.getSupportedVoices(user?.id).catch(err => {
          console.error('[VoiceLab] Error loading system voices:', err);
          return [];
        }),
        user ? VoiceModelService.getVoiceModels(user.id).catch(err => {
          console.error('[VoiceLab] Error loading user voice models:', err);
          return [];
        }) : Promise.resolve([])
      ]);

      console.log('[VoiceLab] Loaded voices:', {
        systemVoices: systemVoices.length,
        userVoiceModels: userVoiceModels.length
      });

      // 过滤出已准备好的用户音色
      const readyUserVoices = userVoiceModels.filter(voice => voice.status === 'ready');
      setUserVoices(readyUserVoices);

      // 合并系统音色和用户克隆音色
      const combinedVoices = [
        ...systemVoices,
        ...readyUserVoices.map(voice => ({
          id: voice.model_id,
          name: `${voice.name} (克隆)`,
          preview: '这是您克隆的声音',
          isCustom: true,
          isCloned: true
        }))
      ];

      console.log('[VoiceLab] Combined voices:', combinedVoices.length);
      setVoices(combinedVoices);

      // 如果没有音色，尝试初始化默认音色
      if (combinedVoices.length === 0) {
        console.log('[VoiceLab] No voices found, attempting to initialize default voices...');
        try {
          const initResult = await VoiceInitializer.initialize();
          if (initResult.success && initResult.details.voicesCreated > 0) {
            console.log('[VoiceLab] Default voices initialized, reloading...');
            // 清除缓存并重新加载
            TTSService.clearVoiceCache(user?.id);
            VoiceService.clearCache(user?.id);
            // 递归调用重新加载（但不强制刷新，避免无限循环）
            return loadAllVoices(false);
          } else {
            setError(`暂无可用音色: ${initResult.message}`);
          }
        } catch (initError) {
          console.error('[VoiceLab] Failed to initialize default voices:', initError);
          setError('暂无可用音色，请检查数据库配置或联系管理员');
        }
      }

    } catch (error) {
      console.error('[VoiceLab] Error loading voices:', error);
      setError(`加载音色列表失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      // 直接设置加载状态，不使用延迟
      console.log('[VoiceLab] Loading completed');
      setLoadingVoices(false);
    }
  };

  const refreshVoices = async () => {
    await loadAllVoices(true); // 强制刷新
  };

  // 调试函数（仅在开发环境中使用）
  const debugVoices = async () => {
    if (process.env.NODE_ENV === 'development') {
      console.log('[VoiceLab] Starting debug diagnosis...');
      const diagnosis = await VoiceDebugger.diagnose(user?.id);
      console.log('[VoiceLab] Diagnosis result:', diagnosis);

      if (diagnosis.details.voicesTable.count === 0) {
        console.log('[VoiceLab] No voices found, attempting quick fix...');
        const fixResult = await VoiceDebugger.quickFix();
        console.log('[VoiceLab] Quick fix result:', fixResult);

        if (fixResult.success) {
          await loadAllVoices(true);
        }
      }
    }
  };

  const handleDeleteVoice = (voiceId: string, voiceName: string) => {
    setShowDeleteConfirm({ voiceId, voiceName });
  };

  const confirmDeleteVoice = async () => {
    if (!showDeleteConfirm || !user) return;

    try {
      setDeletingVoice(showDeleteConfirm.voiceId);
      setError(null);

      await VoiceService.deleteVoice(showDeleteConfirm.voiceId, user.id);
      // 清除缓存
      VoiceService.clearCache(user.id);

      // 如果删除的是当前选中的音色，切换到第一个可用音色
      if (selectedVoice === showDeleteConfirm.voiceId) {
        const remainingVoices = voices.filter(v => v.id !== showDeleteConfirm.voiceId);
        if (remainingVoices.length > 0) {
          setSelectedVoice(remainingVoices[0].id);
        }
      }

      // 刷新音色列表
      await loadAllVoices();

      setShowDeleteConfirm(null);
    } catch (err) {
      console.error('Error deleting voice:', err);
      setError(err instanceof Error ? err.message : '删除音色失败');
    } finally {
      setDeletingVoice(null);
    }
  };

  const cancelDeleteVoice = () => {
    setShowDeleteConfirm(null);
  };



  const emotions = [
    { id: 'natural', name: '自然' },
    { id: 'happy', name: '喜悦' },
    { id: 'sad', name: '悲伤' },
    { id: 'angry', name: '愤怒' },
    { id: 'calm', name: '平静' },
    { id: 'excited', name: '兴奋' },
  ];

  // 获取当前选中的音色对象
  const selectedVoiceObject = voices.find(voice => voice.id === selectedVoice);
  const currentModel = selectedVoiceObject?.model || 'fnlp/MOSS-TTSD-v0.5';



  const codeExamples = {
    python: `import requests

response = requests.post('https://xfsvmyceleiafhqewdkj.supabase.co/functions/v1/tts',
  headers={'Authorization': 'Bearer YOUR_API_KEY'},
  json={
    'text': '${text}',
    'model': '${currentModel}',
    'voice': '${selectedVoice}',
    'speed': ${speed}${emotion !== 'natural' ? `,
    'emotion': '${emotion}'` : ''}${pitch !== 1.0 ? `,
    'pitch': ${pitch}` : ''}
  }
)

audio_data = response.content
with open('output.mp3', 'wb') as f:
    f.write(audio_data)`,

    javascript: `const response = await fetch('https://xfsvmyceleiafhqewdkj.supabase.co/functions/v1/tts', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    text: '${text}',
    model: '${currentModel}',
    voice: '${selectedVoice}',
    speed: ${speed}${emotion !== 'natural' ? `,
    emotion: '${emotion}'` : ''}${pitch !== 1.0 ? `,
    pitch: ${pitch}` : ''}
  })
});

const audioBlob = await response.blob();
const audioUrl = URL.createObjectURL(audioBlob);`,

    curl: `curl -X POST https://xfsvmyceleiafhqewdkj.supabase.co/functions/v1/tts \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "text": "${text}",
    "model": "${currentModel}",
    "voice": "${selectedVoice}",
    "speed": ${speed}${emotion !== 'natural' ? `,
    "emotion": "${emotion}"` : ''}${pitch !== 1.0 ? `,
    "pitch": ${pitch}` : ''}
  }' \\
  --output output.mp3`
  };

  const handleCopy = () => {
    navigator.clipboard.writeText(codeExamples[activeTab as keyof typeof codeExamples]);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const handleCopyUri = (uri: string, voiceId: string) => {
    navigator.clipboard.writeText(uri);
    setCopiedUri(voiceId);
    setTimeout(() => setCopiedUri(null), 2000);
  };



  const handleDownload = () => {
    if (audioUrl) {
      const a = document.createElement('a');
      a.href = audioUrl;
      a.download = `speech_${Date.now()}.mp3`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  };

  const handleGenerate = async () => {
    if (!text.trim()) return;

    // 验证高级参数
    const paramValidation = validateAdvancedParams();
    if (!paramValidation.valid) {
      setError(`参数错误: ${paramValidation.error}`);
      return;
    }

    try {
      setGenerating(true);
      setError(null);

      // 清除之前的音频
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
        setAudioUrl(null);
      }


      // 调用 TTS API，使用当前音色的模型信息和高级参数
      const result = await TTSService.generateSpeech({
        model: currentModel,
        input: text,
        voice: selectedVoice,
        speed: speechSpeed,  // 使用新的语音速度参数
        gain: speechGain,    // 使用新的音频增益参数
        response_format: responseFormat,  // 使用新的输出格式参数
        sample_rate: sampleRate,         // 使用新的采样率参数
        // 保留原有参数作为兼容
        pitch,
        emotion,
      }, user?.id);

      if (result.success) {
        // 记录用量
        if (user && result.usage) {
          // 计算文本的 UTF-8 字节数
          const bytesUsed = new TextEncoder().encode(text).length;

          // 记录到 usage_records 表
          await UsageService.recordUsage(user.id, 'tts', bytesUsed);

          // 同时更新用户余额
          try {
            await SubscriptionService.consumeUserBytes(user.id, bytesUsed);
          } catch (error) {
            console.error('更新用户余额失败:', error);
            // 如果余额不足，显示错误但不阻止音频播放
            if (error.message === '字节数不足') {
              setError('字节数不足，请考虑升级订阅计划');
            }
          }
        }

        // 处理音频数据
        if (result.audioData) {
          const url = TTSService.createAudioUrl(result.audioData);
          setAudioUrl(url);
        } else if (result.audioUrl) {
          setAudioUrl(result.audioUrl);
        }
      } else {
        setError(result.error || '语音生成失败');
      }
    } catch (error) {
      console.error('Error generating speech:', error);
      setError(error instanceof Error ? error.message : '网络请求失败');
    } finally {
      setGenerating(false);
    }
  };
  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <Sidebar />
      
      <div className="ml-64 p-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="space-y-8"
        >
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold mb-2">声音实验室</h1>
            <p className="text-gray-400">在这里探索语音合成的无限可能</p>
            <div className="flex items-center justify-center mt-4 space-x-4 text-sm">
              <div className="flex items-center space-x-2">
                <div className={`w-3 h-3 rounded-full ${text.trim() ? 'bg-green-500' : 'bg-gray-500'}`}></div>
                <span className={text.trim() ? 'text-green-400' : 'text-gray-400'}>文本输入</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className={`w-3 h-3 rounded-full ${selectedVoice ? 'bg-green-500' : 'bg-gray-500'}`}></div>
                <span className={selectedVoice ? 'text-green-400' : 'text-gray-400'}>音色配置</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className={`w-3 h-3 rounded-full ${audioUrl ? 'bg-green-500' : 'bg-gray-500'}`}></div>
                <span className={audioUrl ? 'text-green-400' : 'text-gray-400'}>语音生成</span>
              </div>
            </div>
          </div>

          {/* Card-based Flow Layout */}
          <div className="max-w-4xl mx-auto space-y-8">
            {/* Step 1: Text Input */}
            <Card glass glow>
              <div className="flex items-center space-x-3 mb-6">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                  text.trim() ? 'bg-green-500 text-white' : 'bg-purple-500 text-white'
                }`}>
                  1
                </div>
                <div>
                  <h3 className="text-xl font-semibold">输入文本</h3>
                  <p className="text-gray-400 text-sm">输入您想要合成的文本内容</p>
                </div>
                {text.trim() && (
                  <CheckCircle className="h-6 w-6 text-green-500 ml-auto" />
                )}
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-400">字符数量</span>
                  <span className="text-sm font-medium">{text.length} 字符</span>
                </div>

                <textarea
                  value={text}
                  onChange={(e) => setText(e.target.value)}
                  className="w-full h-32 p-4 bg-gray-800 border border-gray-700 rounded-lg text-white resize-none focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                  placeholder="输入您想要合成的文本..."
                />

                <div className="flex items-center justify-between text-xs">
                  <span className="text-gray-500">
                    支持 SSML 标签，如 [style=happy]开心的文本[/style]
                  </span>
                  <span className="text-gray-400">
                    {new TextEncoder().encode(text).length} UTF-8 字节
                  </span>
                </div>
              </div>
            </Card>

            {/* Step 2: Voice Selection and Parameters */}
            <Card glass glow>
              <div className="flex items-center space-x-3 mb-6">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                  selectedVoice ? 'bg-green-500 text-white' : 'bg-purple-500 text-white'
                }`}>
                  2
                </div>
                <div>
                  <h3 className="text-xl font-semibold">选择音色</h3>
                  <p className="text-gray-400 text-sm">选择合适的音色进行语音合成</p>
                </div>
                {selectedVoice && (
                  <CheckCircle className="h-6 w-6 text-green-500 ml-auto" />
                )}
              </div>

              <div className="space-y-4">
                {/* Voice Selection */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Volume2 className="h-5 w-5 text-purple-500" />
                    <h4 className="text-lg font-semibold">音色选择</h4>
                  </div>
                  <div className="flex items-center space-x-2">
                    {process.env.NODE_ENV === 'development' && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={debugVoices}
                        className="p-2 text-xs"
                        title="调试音色系统"
                      >
                        🔧
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={refreshVoices}
                      disabled={loadingVoices}
                      className="p-2"
                    >
                      <RefreshCw className={`h-4 w-4 ${loadingVoices ? 'animate-spin' : ''}`} />
                    </Button>
                  </div>
                </div>

                  {loadingVoices ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
                    </div>
                  ) : voices.length === 0 ? (
                    <div className="text-center py-8">
                      <Volume2 className="h-12 w-12 text-gray-600 mx-auto mb-3" />
                      <p className="text-gray-400">暂无可用音色</p>
                      <p className="text-gray-500 text-sm mt-1">请稍后重试或联系管理员</p>
                    </div>
                  ) : (
                    <div className="max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800">
                      <div className="space-y-3 pr-2">
                        {voices.map((voice) => (
                        <div
                          key={voice.id}
                          className={`p-3 rounded-lg cursor-pointer transition-all duration-200 ${
                            selectedVoice === voice.id
                              ? 'bg-purple-500/20 border border-purple-500/50'
                              : 'bg-gray-800/50 border border-gray-700 hover:border-gray-600'
                          }`}
                          onClick={() => setSelectedVoice(voice.id)}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2 flex-1 min-w-0">
                              <span className="font-medium truncate">{voice.name}</span>
                              <div className="flex items-center space-x-1 flex-shrink-0">
                                {voice.isCloned && (
                                  <span className="px-2 py-1 bg-blue-500/20 text-blue-400 text-xs rounded-full">
                                    克隆
                                  </span>
                                )}
                                {voice.isCustom && !voice.isCloned && (
                                  <span className="px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded-full">
                                    自定义
                                  </span>
                                )}
                                {voice.gender && (
                                  <span className={`px-2 py-1 text-xs rounded-full ${
                                    voice.gender === 'female'
                                      ? 'bg-pink-500/20 text-pink-400'
                                      : voice.gender === 'male'
                                      ? 'bg-blue-500/20 text-blue-400'
                                      : 'bg-gray-500/20 text-gray-400'
                                  }`}>
                                    {voice.gender === 'female' ? '女声' : voice.gender === 'male' ? '男声' : '中性'}
                                  </span>
                                )}
                                {voice.model && (
                                  <span className="px-2 py-1 bg-purple-500/20 text-purple-400 text-xs rounded-full">
                                    {voice.model.includes('CosyVoice') ? 'CosyVoice' : 'MOSS'}
                                  </span>
                                )}
                              </div>
                            </div>
                            <div className="flex items-center space-x-1 flex-shrink-0 ml-2">
                              <button className="text-purple-400 hover:text-purple-300 p-1">
                                <Play className="h-4 w-4" />
                              </button>
                              <button
                                className={`p-1 transition-colors ${
                                  copiedUri === voice.id
                                    ? 'text-green-400'
                                    : 'text-blue-400 hover:text-blue-300'
                                }`}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleCopyUri(voice.uri || voice.id, voice.id);
                                }}
                                title={copiedUri === voice.id ? '已复制 URI!' : '复制音色 URI'}
                              >
                                {copiedUri === voice.id ? (
                                  <CheckCircle className="h-4 w-4" />
                                ) : (
                                  <Copy className="h-4 w-4" />
                                )}
                              </button>
                              {voice.isCustom && (
                                <button
                                  className="text-red-400 hover:text-red-300 p-1"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDeleteVoice(voice.id, voice.name);
                                  }}
                                  disabled={deletingVoice === voice.id}
                                  title="删除音色"
                                >
                                  {deletingVoice === voice.id ? (
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-400"></div>
                                  ) : (
                                    <Trash2 className="h-4 w-4" />
                                  )}
                                </button>
                              )}
                            </div>
                          </div>
                          <p className="text-sm text-gray-400 mt-1 truncate">"{voice.preview}"</p>
                        </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>



                {/* 高级参数调节 */}
                <div className="mt-6 border border-gray-700 rounded-lg bg-gray-800/30">
                  <div
                    className="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-800/50 transition-colors"
                    onClick={() => setShowAdvancedParams(!showAdvancedParams)}
                  >
                    <div className="flex items-center space-x-2">
                      <Sliders className="h-5 w-5 text-purple-500" />
                      <h4 className="text-lg font-semibold">高级参数</h4>
                    </div>
                    <div className="flex items-center space-x-2">
                      <select
                        className="bg-gray-700 border border-gray-600 rounded px-2 py-1 text-sm"
                        onChange={(e) => {
                          if (e.target.value && paramPresets[e.target.value as keyof typeof paramPresets]) {
                            applyPreset(paramPresets[e.target.value as keyof typeof paramPresets]);
                          }
                        }}
                        onClick={(e) => e.stopPropagation()}
                      >
                        <option value="">选择预设</option>
                        <option value="default">默认设置</option>
                        <option value="slow_clear">慢速清晰</option>
                        <option value="fast_compact">快速紧凑</option>
                        <option value="high_quality">高质量</option>
                        <option value="podcast">播客优化</option>
                      </select>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          resetToDefaults();
                        }}
                        className="text-xs"
                      >
                        重置
                      </Button>
                      {showAdvancedParams ? (
                        <ChevronUp className="h-4 w-4 text-gray-400" />
                      ) : (
                        <ChevronDown className="h-4 w-4 text-gray-400" />
                      )}
                    </div>
                  </div>

                  {showAdvancedParams && (
                    <div className="p-4 border-t border-gray-700 space-y-4">
                      {/* 语音速度和音频增益 */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium mb-2">
                            语音速度 (0.25-4.0): {speechSpeed.toFixed(2)}x
                          </label>
                          <input
                            type="range"
                            min="0.25"
                            max="4.0"
                            step="0.05"
                            value={speechSpeed}
                            onChange={(e) => setSpeechSpeed(parseFloat(e.target.value))}
                            className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
                          />
                          <div className="flex justify-between text-xs text-gray-400 mt-1">
                            <span>0.25x</span>
                            <span>4.0x</span>
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium mb-2">
                            音频增益 (-10 到 10 dB): {speechGain >= 0 ? '+' : ''}{speechGain}dB
                          </label>
                          <input
                            type="range"
                            min="-10"
                            max="10"
                            step="0.5"
                            value={speechGain}
                            onChange={(e) => setSpeechGain(parseFloat(e.target.value))}
                            className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
                          />
                          <div className="flex justify-between text-xs text-gray-400 mt-1">
                            <span>-10dB</span>
                            <span>+10dB</span>
                          </div>
                        </div>
                      </div>

                      {/* 输出格式和采样率 */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium mb-2">输出格式:</label>
                          <select
                            value={responseFormat}
                            onChange={(e) => {
                              setResponseFormat(e.target.value);
                              // 根据格式自动调整采样率
                              if (e.target.value === 'opus') {
                                setSampleRate(48000);
                              } else if (e.target.value === 'mp3' && sampleRate > 44100) {
                                setSampleRate(44100);
                              }
                            }}
                            className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2"
                          >
                            <option value="mp3">MP3 (推荐)</option>
                            <option value="wav">WAV (无损)</option>
                            <option value="opus">Opus (高压缩)</option>
                            <option value="pcm">PCM (原始)</option>
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium mb-2">采样率:</label>
                          <select
                            value={sampleRate}
                            onChange={(e) => setSampleRate(parseInt(e.target.value))}
                            className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2"
                          >
                            {responseFormat === 'opus' ? (
                              <option value="48000">48kHz (仅支持)</option>
                            ) : responseFormat === 'mp3' ? (
                              <>
                                <option value="44100">44.1kHz (默认)</option>
                                <option value="32000">32kHz</option>
                              </>
                            ) : (
                              <>
                                <option value="44100">44.1kHz (默认)</option>
                                <option value="32000">32kHz</option>
                                <option value="24000">24kHz</option>
                                <option value="16000">16kHz</option>
                                <option value="8000">8kHz</option>
                              </>
                            )}
                          </select>
                        </div>
                      </div>

                      {/* 参数预览 */}
                      <div className={`p-3 rounded-lg border text-sm ${
                        validateAdvancedParams().valid
                          ? 'bg-blue-900/20 border-blue-600/30 text-blue-400'
                          : 'bg-red-900/20 border-red-600/30 text-red-400'
                      }`}>
                        <strong>当前配置:</strong> {
                          validateAdvancedParams().valid
                            ? `速度: ${speechSpeed.toFixed(2)}x | 增益: ${speechGain >= 0 ? '+' : ''}${speechGain}dB | 格式: ${responseFormat.toUpperCase()} | 采样率: ${(sampleRate/1000).toFixed(1)}kHz`
                            : validateAdvancedParams().error
                        }
                      </div>
                    </div>
                  )}
                </div>

              {error && (
                <div className="mt-6 p-4 bg-red-900/20 border border-red-600/30 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <AlertTriangle className="h-5 w-5 text-red-400" />
                    <p className="text-red-400 text-sm">{error}</p>
                  </div>
                </div>
              )}
            </Card>

            {/* Step 3: Generate and Play */}
            <Card glass glow>
              <div className="flex items-center space-x-3 mb-6">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                  audioUrl ? 'bg-green-500 text-white' : generating ? 'bg-yellow-500 text-white' : 'bg-purple-500 text-white'
                }`}>
                  3
                </div>
                <div>
                  <h3 className="text-xl font-semibold">生成和播放</h3>
                  <p className="text-gray-400 text-sm">生成语音并播放试听</p>
                </div>
                {audioUrl && (
                  <CheckCircle className="h-6 w-6 text-green-500 ml-auto" />
                )}
              </div>

              <div className="space-y-6">
                {/* Generate Button */}
                <div className="text-center">
                  <Button
                    variant="primary"
                    size="lg"
                    glow
                    className="w-full mb-4"
                    onClick={handleGenerate}
                    disabled={generating || !text.trim()}
                  >
                    {generating ? (
                      <>
                        <Pause className="h-5 w-5 mr-2" />
                        生成中...
                      </>
                    ) : (
                      <>
                        <Play className="h-5 w-5 mr-2" />
                        生成语音
                      </>
                    )}
                  </Button>

                  <p className="text-sm text-gray-400">
                    预计消耗 {new TextEncoder().encode(text).length} UTF-8 字节
                  </p>
                </div>

                {/* Audio Player */}
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Waveform className="h-5 w-5 text-green-500" />
                    <h4 className="text-lg font-semibold">音频播放器</h4>
                  </div>

                  <AudioPlayer
                    audioUrl={audioUrl}
                    isGenerating={generating}
                    onDownload={handleDownload}
                  />
                </div>

                <div className="text-center">
                  <span className={`text-sm ${audioUrl ? 'text-green-400' : 'text-gray-500'}`}>
                    {audioUrl ? '✓ 音频已生成，可以播放和下载' : '等待生成音频'}
                  </span>
                </div>
              </div>
            </Card>

            {/* Developer Tools (Collapsible) */}
            <Card glass>
              <div
                className="flex items-center justify-between cursor-pointer"
                onClick={() => setShowDeveloperTools(!showDeveloperTools)}
              >
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-full bg-yellow-500 text-white flex items-center justify-center text-sm font-bold">
                    <Code className="h-4 w-4" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold">开发者工具</h3>
                    <p className="text-gray-400 text-sm">查看API调用代码示例</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-400">
                    {showDeveloperTools ? '收起' : '展开'}
                  </span>
                  {showDeveloperTools ? (
                    <ChevronUp className="h-5 w-5 text-gray-400" />
                  ) : (
                    <ChevronDown className="h-5 w-5 text-gray-400" />
                  )}
                </div>
              </div>

              {showDeveloperTools && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                  className="mt-6 space-y-4"
                >
                  {/* Language Tabs */}
                  <div className="flex space-x-2 border-b border-gray-700">
                    {['python', 'javascript', 'curl'].map((lang) => (
                      <button
                        key={lang}
                        onClick={() => setActiveTab(lang)}
                        className={`px-4 py-2 text-sm font-medium transition-all duration-200 rounded-t-lg ${
                          activeTab === lang
                            ? 'text-purple-400 bg-gray-800 border-b-2 border-purple-400'
                            : 'text-gray-400 hover:text-gray-300 hover:bg-gray-800/50'
                        }`}
                      >
                        {lang.charAt(0).toUpperCase() + lang.slice(1)}
                      </button>
                    ))}
                  </div>

                  {/* Code Display */}
                  <div className="relative">
                    <pre className="bg-gray-900 p-6 rounded-lg text-sm overflow-x-auto border border-gray-700">
                      <code className="text-green-400 font-mono">
                        {codeExamples[activeTab as keyof typeof codeExamples]}
                      </code>
                    </pre>

                    <button
                      onClick={handleCopy}
                      className="absolute top-3 right-3 p-2 bg-gray-800 rounded-lg hover:bg-gray-700 transition-all duration-200 border border-gray-600"
                      title={copied ? '已复制!' : '复制代码'}
                    >
                      {copied ? (
                        <CheckCircle className="h-4 w-4 text-green-400" />
                      ) : (
                        <Copy className="h-4 w-4 text-gray-400" />
                      )}
                    </button>
                  </div>

                  <div className="flex items-center justify-between text-xs">
                    <span className="text-gray-500">
                      代码会根据您的配置自动更新
                    </span>
                    <span className="text-gray-400">
                      点击右上角按钮复制代码
                    </span>
                  </div>
                </motion.div>
              )}
            </Card>
          </div>
        </motion.div>
      </div>

      {/* 删除确认对话框 */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center space-x-3 mb-4">
              <div className="p-2 bg-red-500/20 rounded-full">
                <AlertTriangle className="h-6 w-6 text-red-500" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white">确认删除音色</h3>
                <p className="text-sm text-gray-400">此操作无法撤销</p>
              </div>
            </div>

            <div className="mb-6">
              <p className="text-gray-300">
                您确定要删除音色 <span className="font-medium text-white">"{showDeleteConfirm.voiceName}"</span> 吗？
              </p>
              <p className="text-sm text-gray-400 mt-2">
                这将同时删除数据库记录和 SiliconFlow 平台上的音色数据。
              </p>
            </div>

            <div className="flex justify-end space-x-3">
              <Button
                variant="ghost"
                onClick={cancelDeleteVoice}
                disabled={deletingVoice === showDeleteConfirm.voiceId}
              >
                取消
              </Button>
              <Button
                variant="primary"
                onClick={confirmDeleteVoice}
                disabled={deletingVoice === showDeleteConfirm.voiceId}
                className="bg-red-600 hover:bg-red-700"
              >
                {deletingVoice === showDeleteConfirm.voiceId ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    删除中...
                  </>
                ) : (
                  <>
                    <Trash2 className="h-4 w-4 mr-2" />
                    确认删除
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// 添加滑块样式
const sliderStyles = `
  .slider::-webkit-slider-thumb {
    appearance: none;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #8b5cf6;
    cursor: pointer;
    border: 2px solid #ffffff;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  }

  .slider::-moz-range-thumb {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #8b5cf6;
    cursor: pointer;
    border: 2px solid #ffffff;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  }

  .slider::-webkit-slider-track {
    height: 8px;
    border-radius: 4px;
    background: #374151;
  }

  .slider::-moz-range-track {
    height: 8px;
    border-radius: 4px;
    background: #374151;
  }
`;

// 注入样式
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = sliderStyles;
  if (!document.head.querySelector('style[data-slider-styles]')) {
    styleElement.setAttribute('data-slider-styles', 'true');
    document.head.appendChild(styleElement);
  }
}

export default VoiceLab;