import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  BarChart3, 
  TrendingUp, 
  Calendar, 
  Download,
  Filter,
  Activity,
  Clock,
  Zap,
  DollarSign
} from 'lucide-react';
import { Sidebar } from '../components/layout/Sidebar';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { useAuth } from '../contexts/AuthContext';
import { UsageService } from '../services/usageService';
import { SubscriptionService } from '../services/subscriptionService';

export const Usage: React.FC = () => {
  const { user } = useAuth();
  const [timeRange, setTimeRange] = useState('30d');
  const [usageData, setUsageData] = useState({
    current: 0,
    total: 10000,
    percentage: 0,
    trend: '+0%',
    cost: 0
  });
  const [dailyUsage, setDailyUsage] = useState([]);
  const [apiCalls, setApiCalls] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadUsageData();
    }
  }, [user, timeRange]);

  const loadUsageData = async () => {
    try {
      setLoading(true);
      const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90;
      const stats = await UsageService.getUserUsageStats(user!.id, days);
      
      // 获取用户余额信息以获取正确的总额度
      const balance = await SubscriptionService.getUserBalance(user!.id);
      const totalBytes = balance ? balance.total_bytes : 10000;

      setUsageData({
        current: stats.totalBytes,
        total: totalBytes,
        percentage: (stats.totalBytes / totalBytes) * 100,
        trend: '+12%', // 这里可以计算实际趋势
        cost: stats.totalCost
      });
      
      setDailyUsage(stats.dailyUsage.map(day => ({
        date: new Date(day.date).toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }),
        bytes: day.characters, // characters 字段现在存储字节数
        cost: day.cost
      })));
      
      setApiCalls(stats.records.map(record => ({
        time: new Date(record.created_at).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
        endpoint: record.service_type === 'tts' ? '/v1/tts' : '/v1/clone',
        bytes: record.characters_used, // characters_used 字段现在存储字节数
        status: 'success', // 假设都是成功的，实际可以从记录中获取
        latency: '245ms' // 这里可以存储实际的延迟数据
      })));
    } catch (error) {
      console.error('Error loading usage data:', error);
    } finally {
      setLoading(false);
    }
  };
  

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <Sidebar />
      
      <div className="ml-64 p-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="space-y-8"
        >
          {/* Header */}
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold mb-2">用量统计</h1>
              <p className="text-gray-400">监控您的 API 使用情况和成本</p>
            </div>
            <div className="flex space-x-3">
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value)}
                className="px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="7d">最近 7 天</option>
                <option value="30d">最近 30 天</option>
                <option value="90d">最近 90 天</option>
              </select>
              <Button variant="glass">
                <Download className="h-4 w-4 mr-2" />
                导出报告
              </Button>
            </div>
          </div>

          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card glass glow>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-purple-500/20 rounded-lg">
                  <BarChart3 className="h-6 w-6 text-purple-500" />
                </div>
                <div>
                  <p className="text-sm text-gray-400">本月用量</p>
                  <p className="text-2xl font-bold">{usageData.current.toLocaleString()}</p>
                  <p className="text-xs text-gray-500">/ {usageData.total.toLocaleString()} UTF-8 字节</p>
                </div>
              </div>
            </Card>

            <Card glass glow>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-green-500/20 rounded-lg">
                  <TrendingUp className="h-6 w-6 text-green-500" />
                </div>
                <div>
                  <p className="text-sm text-gray-400">增长趋势</p>
                  <p className="text-2xl font-bold text-green-400">{usageData.trend}</p>
                  <p className="text-xs text-gray-500">相比上月</p>
                </div>
              </div>
            </Card>

            <Card glass glow>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-500/20 rounded-lg">
                  <Activity className="h-6 w-6 text-blue-500" />
                </div>
                <div>
                  <p className="text-sm text-gray-400">API 调用</p>
                  <p className="text-2xl font-bold">1,247</p>
                  <p className="text-xs text-gray-500">本月总计</p>
                </div>
              </div>
            </Card>

            <Card glass glow>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-yellow-500/20 rounded-lg">
                  <DollarSign className="h-6 w-6 text-yellow-500" />
                </div>
                <div>
                  <p className="text-sm text-gray-400">本月费用</p>
                  <p className="text-2xl font-bold">${usageData.cost.toFixed(2)}</p>
                  <p className="text-xs text-gray-500">预计账单</p>
                </div>
              </div>
            </Card>
          </div>

          {/* Usage Chart */}
          <Card glass glow>
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold">每日使用量</h3>
              <Button variant="ghost" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                筛选
              </Button>
            </div>
            
            <div className="space-y-4">
              {/* Chart Placeholder */}
              {loading ? (
                <div className="h-64 bg-gray-800 rounded-lg flex items-center justify-center">
                  <p className="text-gray-400">加载中...</p>
                </div>
              ) : dailyUsage.length === 0 ? (
                <div className="h-64 bg-gray-800 rounded-lg flex items-center justify-center">
                  <p className="text-gray-400">暂无数据</p>
                </div>
              ) : (
              <div className="h-64 bg-gray-800 rounded-lg flex items-end justify-center space-x-2 p-4 relative">
                {(() => {
                  // 计算动态最大值
                  const maxBytes = Math.max(...dailyUsage.map(day => day.bytes));
                  const dynamicMax = maxBytes > 0 ? Math.ceil(maxBytes * 1.2) : 100; // 增加20%的空间
                  const chartHeight = 200; // 图表可用高度

                  return (
                    <>
                      {/* Y轴刻度线和标签 */}
                      <div className="absolute left-2 top-4 bottom-16 flex flex-col justify-between text-xs text-gray-500">
                        <span>{dynamicMax.toLocaleString()}</span>
                        <span>{Math.round(dynamicMax * 0.75).toLocaleString()}</span>
                        <span>{Math.round(dynamicMax * 0.5).toLocaleString()}</span>
                        <span>{Math.round(dynamicMax * 0.25).toLocaleString()}</span>
                        <span>0</span>
                      </div>

                      {/* 水平网格线 */}
                      <div className="absolute left-12 right-4 top-4 bottom-16">
                        {[0.25, 0.5, 0.75, 1].map((ratio, index) => (
                          <div
                            key={index}
                            className="absolute w-full border-t border-gray-700/50"
                            style={{ bottom: `${ratio * chartHeight}px` }}
                          />
                        ))}
                      </div>

                      {/* 柱状图 */}
                      <div className="flex items-end justify-center space-x-2 ml-12">
                        {dailyUsage.map((day, index) => {
                          const heightPercentage = maxBytes > 0 ? (day.bytes / dynamicMax) : 0;
                          const barHeight = heightPercentage * chartHeight;

                          return (
                            <div key={index} className="flex flex-col items-center space-y-2 group relative">
                              {/* 悬浮提示 */}
                              <div className="absolute bottom-full mb-2 hidden group-hover:block bg-gray-700 text-white text-xs rounded px-2 py-1 whitespace-nowrap z-10">
                                <div>{day.date}</div>
                                <div>{day.bytes.toLocaleString()} 字节</div>
                                <div>${day.cost.toFixed(4)}</div>
                                <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-700"></div>
                              </div>

                              <div
                                className="bg-gradient-to-t from-purple-500 to-blue-500 rounded-t transition-all duration-300 hover:from-purple-400 hover:to-blue-400"
                                style={{
                                  width: '24px',
                                  height: `${barHeight}px`,
                                  minHeight: day.bytes > 0 ? '4px' : '0px'
                                }}
                              />
                              <span className="text-xs text-gray-400">{day.date}</span>
                            </div>
                          );
                        })}
                      </div>
                    </>
                  );
                })()}
              </div>
              
              )}
            </div>
          </Card>

          {/* Recent API Calls */}
          <Card glass glow>
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold">最近 API 调用</h3>
              <Button variant="ghost" size="sm">
                查看全部
              </Button>
            </div>
            
            <div className="space-y-3">
              {loading ? (
                <div className="text-center py-4">
                  <p className="text-gray-400">加载中...</p>
                </div>
              ) : apiCalls.length === 0 ? (
                <div className="text-center py-4">
                  <p className="text-gray-400">暂无 API 调用记录</p>
                </div>
              ) : (
              <>
                {apiCalls.map((call, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-300">{call.time}</span>
                    </div>
                    <code className="text-sm bg-gray-700 px-2 py-1 rounded">
                      {call.endpoint}
                    </code>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <span className="text-sm text-gray-400">
                      {call.bytes > 0 ? `${call.bytes} 字节` : '-'}
                    </span>
                    <span className="text-sm text-gray-400">
                      {call.latency}
                    </span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      call.status === 'success' 
                        ? 'bg-green-500/20 text-green-400' 
                        : 'bg-red-500/20 text-red-400'
                    }`}>
                      {call.status === 'success' ? '成功' : '失败'}
                    </span>
                  </div>
                </div>
                ))}
              </>
              )}
            </div>
          </Card>

          {/* Usage Breakdown */}
          <div className="grid md:grid-cols-2 gap-6">
            <Card glass glow>
              <h3 className="text-lg font-semibold mb-4">按功能分类</h3>
              <div className="space-y-3">
                {[
                  { name: '语音合成', usage: 6800, percentage: 90.7, color: 'bg-purple-500' },
                  { name: '语音克隆', usage: 500, percentage: 6.7, color: 'bg-blue-500' },
                  { name: '其他', usage: 200, percentage: 2.6, color: 'bg-gray-500' },
                ].map((item, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>{item.name}</span>
                      <span>{item.usage.toLocaleString()} 字节 ({item.percentage}%)</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div 
                        className={`${item.color} h-2 rounded-full`}
                        style={{ width: `${item.percentage}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            <Card glass glow>
              <h3 className="text-lg font-semibold mb-4">性能指标</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Zap className="h-4 w-4 text-yellow-500" />
                    <span className="text-sm">平均响应时间</span>
                  </div>
                  <span className="text-sm font-medium">234ms</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Activity className="h-4 w-4 text-green-500" />
                    <span className="text-sm">成功率</span>
                  </div>
                  <span className="text-sm font-medium text-green-400">99.2%</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <BarChart3 className="h-4 w-4 text-blue-500" />
                    <span className="text-sm">峰值 QPS</span>
                  </div>
                  <span className="text-sm font-medium">12.5</span>
                </div>
              </div>
            </Card>
          </div>
        </motion.div>
      </div>
    </div>
  );
};