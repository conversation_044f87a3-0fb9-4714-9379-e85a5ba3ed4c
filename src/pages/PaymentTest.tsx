import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { CreditCard, Clock, CheckCircle, XCircle, RefreshCw } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { WechatPayModal } from '../components/payment/WechatPayModal';
import { WechatPayService, type WechatPayOrder } from '../services/wechatPayService';

export const PaymentTest: React.FC = () => {
  const { user } = useAuth();
  const [showWechatPay, setShowWechatPay] = useState(false);
  const [orders, setOrders] = useState<WechatPayOrder[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedAmount, setSelectedAmount] = useState(50);

  useEffect(() => {
    if (user) {
      loadOrders();
    }
  }, [user]);

  const loadOrders = async () => {
    if (!user) return;
    
    setLoading(true);
    try {
      const result = await WechatPayService.getUserOrders(1, 10);
      if (result.success && result.data) {
        setOrders(result.data.orders);
      }
    } catch (error) {
      console.error('加载订单失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePaymentSuccess = (order: WechatPayOrder) => {
    console.log('支付成功:', order);
    // 刷新订单列表
    loadOrders();
    // 显示成功提示
    alert(`支付成功！订单号：${order.out_trade_no}`);
  };

  const handlePaymentError = (error: string) => {
    console.error('支付失败:', error);
    alert(`支付失败：${error}`);
  };

  const predefinedAmounts = [10, 25, 50, 100, 200];

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <Card glass>
          <div className="text-center p-8">
            <h2 className="text-xl font-semibold text-white mb-4">请先登录</h2>
            <p className="text-gray-400">您需要登录后才能使用支付功能</p>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="max-w-4xl mx-auto">
            {/* 页面标题 */}
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-white mb-4">
                微信支付测试
              </h1>
              <p className="text-gray-400">
                测试微信支付 API v3 集成功能
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* 支付测试区域 */}
              <Card glass>
                <div className="p-6">
                  <h2 className="text-xl font-semibold text-white mb-6 flex items-center">
                    <CreditCard className="mr-2" size={24} />
                    创建支付订单
                  </h2>

                  {/* 金额选择 */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-300 mb-3">
                      选择充值金额
                    </label>
                    <div className="grid grid-cols-3 gap-3">
                      {predefinedAmounts.map((amount) => (
                        <button
                          key={amount}
                          onClick={() => setSelectedAmount(amount)}
                          className={`p-3 rounded-lg border transition-colors ${
                            selectedAmount === amount
                              ? 'border-purple-500 bg-purple-500/20 text-purple-300'
                              : 'border-gray-600 bg-gray-800 text-gray-300 hover:border-gray-500'
                          }`}
                        >
                          ${amount}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* 自定义金额 */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      或输入自定义金额 ($)
                    </label>
                    <input
                      type="number"
                      min="1"
                      max="1000"
                      value={selectedAmount}
                      onChange={(e) => setSelectedAmount(parseInt(e.target.value) || 1)}
                      className="w-full p-3 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                      placeholder="输入金额"
                    />
                  </div>

                  {/* 支付按钮 */}
                  <Button
                    variant="primary"
                    glow
                    onClick={() => setShowWechatPay(true)}
                    className="w-full"
                    disabled={selectedAmount < 1}
                  >
                    <CreditCard className="mr-2" size={20} />
                    微信支付 ¥{(selectedAmount * 6.8).toFixed(2)}
                  </Button>

                  <p className="text-xs text-gray-500 mt-2 text-center">
                    * 测试环境，按汇率 1:6.8 计算
                  </p>
                </div>
              </Card>

              {/* 订单历史 */}
              <Card glass>
                <div className="p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-xl font-semibold text-white flex items-center">
                      <Clock className="mr-2" size={24} />
                      订单历史
                    </h2>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={loadOrders}
                      disabled={loading}
                    >
                      <RefreshCw className={`mr-1 ${loading ? 'animate-spin' : ''}`} size={16} />
                      刷新
                    </Button>
                  </div>

                  {loading ? (
                    <div className="text-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mx-auto"></div>
                      <p className="text-gray-400 mt-2">加载中...</p>
                    </div>
                  ) : orders.length === 0 ? (
                    <div className="text-center py-8">
                      <p className="text-gray-400">暂无订单记录</p>
                    </div>
                  ) : (
                    <div className="space-y-4 max-h-96 overflow-y-auto">
                      {orders.map((order) => (
                        <div
                          key={order.id}
                          className="p-4 bg-gray-800 rounded-lg border border-gray-700"
                        >
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium text-white">
                              ¥{WechatPayService.formatAmount(order.amount)}
                            </span>
                            <div className="flex items-center">
                              {order.status === 'paid' && (
                                <CheckCircle size={16} className="text-green-500 mr-1" />
                              )}
                              {order.status === 'failed' && (
                                <XCircle size={16} className="text-red-500 mr-1" />
                              )}
                              {order.status === 'pending' && (
                                <Clock size={16} className="text-yellow-500 mr-1" />
                              )}
                              <span className={`text-sm ${WechatPayService.getStatusColor(order.status)}`}>
                                {WechatPayService.formatStatus(order.status)}
                              </span>
                            </div>
                          </div>
                          
                          <p className="text-sm text-gray-400 mb-1">
                            {order.description}
                          </p>
                          
                          <div className="flex justify-between items-center text-xs text-gray-500">
                            <span>订单号: {order.out_trade_no}</span>
                            <span>
                              {new Date(order.created_at).toLocaleString('zh-CN')}
                            </span>
                          </div>

                          {order.paid_at && (
                            <div className="text-xs text-green-400 mt-1">
                              支付时间: {new Date(order.paid_at).toLocaleString('zh-CN')}
                            </div>
                          )}

                          {WechatPayService.isOrderExpired(order) && (
                            <div className="text-xs text-orange-400 mt-1">
                              订单已过期
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </Card>
            </div>

            {/* 功能说明 */}
            <Card glass className="mt-8">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-white mb-4">
                  功能说明
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-400">
                  <div>
                    <h4 className="font-medium text-white mb-2">支付流程</h4>
                    <ul className="space-y-1">
                      <li>• 选择或输入充值金额</li>
                      <li>• 点击微信支付按钮</li>
                      <li>• 扫描二维码完成支付</li>
                      <li>• 系统自动更新订单状态</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-medium text-white mb-2">技术特性</h4>
                    <ul className="space-y-1">
                      <li>• 微信支付 API v3 集成</li>
                      <li>• 实时订单状态轮询</li>
                      <li>• 安全的签名验证</li>
                      <li>• 完整的错误处理</li>
                    </ul>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </motion.div>

        {/* 微信支付模态框 */}
        <WechatPayModal
          isOpen={showWechatPay}
          onClose={() => setShowWechatPay(false)}
          amount={selectedAmount * 100} // 转换为分
          description={`SoulVoice 测试充值 $${selectedAmount}`}
          onSuccess={handlePaymentSuccess}
          onError={handlePaymentError}
        />
      </div>
    </div>
  );
};
