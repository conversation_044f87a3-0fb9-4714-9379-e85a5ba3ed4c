import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Key, 
  Plus, 
  Co<PERSON>, 
  Eye, 
  EyeOff, 
  Trash2, 
  Refresh<PERSON>w, 
  CheckCircle,
  AlertTriangle,
  Calendar,
  Activity
} from 'lucide-react';
import { Sidebar } from '../components/layout/Sidebar';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Input } from '../components/ui/Input';
import { useAuth } from '../contexts/AuthContext';
import { ApiKeyService } from '../services/apiKeyService';
import type { ApiKey } from '../lib/supabase';

export const ApiKeys: React.FC = () => {
  const { user } = useAuth();
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);

  const [showKeys, setShowKeys] = useState<{ [key: string]: boolean }>({});
  const [copied, setCopied] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newKeyName, setNewKeyName] = useState('');
  const [newlyCreatedKey, setNewlyCreatedKey] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      loadApiKeys();
    }
  }, [user]);

  const loadApiKeys = async () => {
    try {
      setLoading(true);
      const keys = await ApiKeyService.getApiKeys(user!.id);
      setApiKeys(keys);
    } catch (error) {
      console.error('Error loading API keys:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleKeyVisibility = (id: string) => {
    setShowKeys(prev => ({ ...prev, [id]: !prev[id] }));
  };

  const copyKey = (keyText: string, id: string) => {
    navigator.clipboard.writeText(keyText);
    setCopied(id);
    setTimeout(() => setCopied(null), 2000);
  };

  const createNewKey = async () => {
    if (!newKeyName.trim()) return;
    
    try {
      setCreating(true);
      const { apiKey, key } = await ApiKeyService.createApiKey(user!.id, newKeyName);
      setApiKeys(prev => [apiKey, ...prev]);
      setNewlyCreatedKey(key);
      setNewKeyName('');
      setShowCreateModal(false);
    } catch (error) {
      console.error('Error creating API key:', error);
    } finally {
      setCreating(false);
    }
  };

  const deleteKey = async (id: string) => {
    try {
      await ApiKeyService.deleteApiKey(id, user!.id);
      setApiKeys(prev => prev.filter(key => key.id !== id));
    } catch (error) {
      console.error('Error deleting API key:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <Sidebar />
      
      <div className="ml-64 p-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="space-y-8"
        >
          {/* Header */}
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold mb-2">API 密钥管理</h1>
              <p className="text-gray-400">管理您的 API 密钥，确保应用安全访问</p>
            </div>
            <Button
              variant="primary"
              glow
              onClick={() => setShowCreateModal(true)}
            >
              <Plus className="h-4 w-4 mr-2" />
              创建新密钥
            </Button>
          </div>

          {/* Security Notice */}
          <Card glass className="border-yellow-600/30 bg-yellow-900/10">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5" />
              <div>
                <h3 className="font-semibold text-yellow-400 mb-2">安全提醒</h3>
                <ul className="text-sm text-gray-300 space-y-1">
                  <li>• 请妥善保管您的 API 密钥，不要在客户端代码中暴露</li>
                  <li>• 建议为不同环境创建不同的密钥</li>
                  <li>• 定期轮换密钥以提高安全性</li>
                  <li>• 如发现密钥泄露，请立即删除并重新创建</li>
                </ul>
              </div>
            </div>
          </Card>

          {/* API Keys List */}
          <div className="space-y-4">
            {loading ? (
              <Card glass glow className="text-center py-8">
                <p className="text-gray-400">加载中...</p>
              </Card>
            ) : apiKeys.length === 0 ? (
              <Card glass glow className="text-center py-8">
                <Key className="h-12 w-12 text-gray-500 mx-auto mb-4" />
                <p className="text-gray-400 mb-4">您还没有创建任何 API 密钥</p>
                <Button
                  variant="primary"
                  onClick={() => setShowCreateModal(true)}
                >
                  创建第一个密钥
                </Button>
              </Card>
            ) : (
            <>
            {apiKeys.map((apiKey) => (
              <Card key={apiKey.id} glass glow>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-purple-500/20 rounded-lg">
                        <Key className="h-5 w-5 text-purple-500" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold">{apiKey.name}</h3>
                        <div className="flex items-center space-x-4 text-sm text-gray-400">
                          <span className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            创建于 {new Date(apiKey.created_at).toLocaleDateString()}
                          </span>
                          <span className="flex items-center">
                            <Activity className="h-4 w-4 mr-1" />
                            最后使用：{apiKey.last_used_at ? new Date(apiKey.last_used_at).toLocaleString() : '从未使用'}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        apiKey.is_active
                          ? 'bg-green-500/20 text-green-400' 
                          : 'bg-gray-500/20 text-gray-400'
                      }`}>
                        {apiKey.is_active ? '活跃' : '停用'}
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <code className="flex-1 bg-gray-800 p-3 rounded-lg font-mono text-sm">
                      {showKeys[apiKey.id]
                        ? (newlyCreatedKey && newlyCreatedKey.startsWith(apiKey.key_prefix.substring(0, 12))
                           ? newlyCreatedKey
                           : (apiKey.key_prefix.includes('...')
                              ? apiKey.key_prefix + ' (请重新生成以查看完整密钥)'
                              : apiKey.key_prefix))
                        : '••••••••••••••••••••••••••••••••'}
                    </code>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleKeyVisibility(apiKey.id)}
                    >
                      {showKeys[apiKey.id] ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        const keyToCopy = newlyCreatedKey && newlyCreatedKey.startsWith(apiKey.key_prefix.substring(0, 12))
                          ? newlyCreatedKey
                          : (apiKey.key_prefix.includes('...') ? '' : apiKey.key_prefix);
                        if (keyToCopy) {
                          copyKey(keyToCopy, apiKey.id);
                        }
                      }}
                      disabled={apiKey.key_prefix.includes('...') && !(newlyCreatedKey && newlyCreatedKey.startsWith(apiKey.key_prefix.substring(0, 12)))}
                      className={copied === apiKey.id ? 'text-green-400' : ''}
                      title={apiKey.key_prefix.includes('...') ? '请重新生成密钥以复制完整内容' : '复制密钥'}
                    >
                      {copied === apiKey.id ? <CheckCircle className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-red-400 hover:text-red-300"
                      onClick={() => deleteKey(apiKey.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="flex justify-between text-sm text-gray-400">
                    <span>密钥 ID：{apiKey.id.substring(0, 8)}...</span>
                    <Button variant="ghost" size="sm">
                      <RefreshCw className="h-4 w-4 mr-1" />
                      重新生成
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
            </>
            )}
          </div>

          {/* Create Key Modal */}
          {showCreateModal && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
              <Card glass className="w-full max-w-md mx-4">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-xl font-semibold mb-2">创建新的 API 密钥</h3>
                    <p className="text-gray-400 text-sm">
                      为您的应用创建一个新的 API 密钥
                    </p>
                  </div>

                  <Input
                    label="密钥名称"
                    placeholder="例如：生产环境密钥"
                    value={newKeyName}
                    onChange={(e) => setNewKeyName(e.target.value)}
                  />

                  <div className="flex space-x-3">
                    <Button
                      variant="ghost"
                      className="flex-1"
                      onClick={() => setShowCreateModal(false)}
                    >
                      取消
                    </Button>
                    <Button
                      variant="primary"
                      className="flex-1"
                      glow
                      onClick={createNewKey}
                      disabled={!newKeyName.trim() || creating}
                    >
                      {creating ? '创建中...' : '创建密钥'}
                    </Button>
                  </div>
                </div>
              </Card>
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
};