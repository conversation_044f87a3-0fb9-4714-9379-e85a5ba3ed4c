import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { User, Mail, Building, Calendar, Edit3, Save, X, Camera, Shield, Crown, Activity, Zap } from 'lucide-react';
import { Sidebar } from '../components/layout/Sidebar';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Input } from '../components/ui/Input';
import { AvatarUploadButton } from '../components/ui/AvatarUploadButton';
import { ProfileEdit } from '../components/profile/ProfileEdit';
import { useAuth } from '../contexts/AuthContext';
import { SubscriptionService, UserSubscription } from '../services/subscriptionService';
import { supabase } from '../lib/supabase';

export const Profile: React.FC = () => {
  const { user, profile } = useAuth();
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [subscription, setSubscription] = useState<UserSubscription | null>(null);
  const [usageStats, setUsageStats] = useState({
    totalCalls: 0,
    totalCharacters: 0,
    usagePercentage: 0
  });

  const handleAvatarUpdate = async (avatarUrl: string) => {
    setRefreshing(true);
    // 延迟一下再刷新，让用户看到成功状态
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  };

  const handleEditSave = () => {
    setRefreshing(true);
    // 编辑保存后刷新页面
    setTimeout(() => {
      window.location.reload();
    }, 500);
  };

  // 加载订阅和用量信息
  useEffect(() => {
    if (user) {
      loadSubscriptionData();
    }
  }, [user]);

  const loadSubscriptionData = async () => {
    if (!user) return;

    try {
      // 获取用户订阅信息
      const userSubscription = await SubscriptionService.getUserSubscription(user.id);
      setSubscription(userSubscription);

      // 从 usage_records 表获取真实的使用量数据
      const { data: usageData, error: usageError } = await supabase
        .from('usage_records')
        .select('characters_used')
        .eq('user_id', user.id);

      if (usageError) {
        console.error('Error fetching usage data:', usageError);
      }

      const totalCharacters = usageData?.reduce((sum, record) => sum + (record.characters_used || 0), 0) || 0;
      const totalCalls = usageData?.length || 0;

      // 计算用量统计
      if (userSubscription) {
        const usagePercentage = Math.round((totalCharacters / userSubscription.bytes_quota) * 100);
        setUsageStats({
          totalCalls,
          totalCharacters,
          usagePercentage
        });

        // 同时更新订阅记录中的 bytes_used（如果不同步的话）
        if (userSubscription.bytes_used !== totalCharacters) {
          await supabase
            .from('user_subscriptions')
            .update({ bytes_used: totalCharacters })
            .eq('id', userSubscription.id);

          // 更新本地状态
          setSubscription({
            ...userSubscription,
            bytes_used: totalCharacters
          });
        }
      } else {
        setUsageStats({
          totalCalls,
          totalCharacters,
          usagePercentage: 0
        });
      }
    } catch (error) {
      console.error('Error loading subscription data:', error);
    }
  };



  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (!user || !profile) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4"></div>
          <p>加载中...</p>
        </div>
      </div>
    );
  }

  if (refreshing) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
          <p>更新中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <Sidebar />

      <div className="lg:ml-64 p-6 md:p-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-7xl mx-auto"
        >
          {/* 页面标题 - 更加大气 */}
          <div className="mb-12 text-center">
            <motion.h1
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 bg-clip-text text-transparent mb-4"
            >
              个人中心
            </motion.h1>
            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-xl text-gray-400 max-w-2xl mx-auto"
            >
              管理您的个人信息和账户设置，打造专属的语音体验
            </motion.p>
          </div>

          <div className="grid lg:grid-cols-4 gap-8 lg:gap-12">
            {/* 左侧：头像和基本信息 - 更大气的设计 */}
            <div className="lg:col-span-1 order-1 lg:order-1">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
              >
                <Card glass className="p-8 text-center relative overflow-hidden">
                  {/* 背景装饰 */}
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 via-transparent to-blue-500/10"></div>

                  {/* 头像区域 - 更大更突出 */}
                  <div className="relative mb-8">
                    <div className="relative inline-block">
                      <div className="w-40 h-40 rounded-full bg-gradient-to-br from-purple-500 via-pink-500 to-blue-500 p-1 shadow-2xl">
                        <div className="w-full h-full rounded-full bg-gray-900 flex items-center justify-center overflow-hidden">
                          {profile.avatar_url ? (
                            <img
                              src={profile.avatar_url}
                              alt="用户头像"
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <span className="text-5xl font-bold text-white">
                              {profile.name.charAt(0).toUpperCase()}
                            </span>
                          )}
                        </div>
                      </div>
                      <AvatarUploadButton
                        onAvatarUpdate={handleAvatarUpdate}
                        className="absolute bottom-2 right-2"
                      />
                    </div>
                  </div>

                  {/* 基本信息 - 更优雅的布局 */}
                  <div className="relative">
                    <h2 className="text-2xl font-bold mb-2 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                      {profile.name}
                    </h2>
                    <p className="text-gray-400 mb-6 text-sm">{profile.email}</p>

                    {/* 订阅状态显示 */}
                    <div className="inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30 mb-6">
                      <Crown className="h-4 w-4 text-blue-400" />
                      <span className="text-sm text-blue-400 font-medium">
                        {subscription ? subscription.plan?.name || '基础版' : '未订阅'}
                      </span>
                    </div>

                    {/* 注册时间 */}
                    <div className="text-sm text-gray-500">
                      <div className="flex items-center justify-center space-x-2">
                        <Calendar className="h-4 w-4" />
                        <span>加入于 {formatDate(profile.created_at)}</span>
                      </div>
                    </div>
                  </div>
                </Card>
              </motion.div>
            </div>

            {/* 右侧：详细信息和编辑 - 更宽敞的布局 */}
            <div className="lg:col-span-3 space-y-8 order-2 lg:order-2">
              {/* 个人信息卡片 - 重新设计 */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <Card glass className="p-8 relative overflow-hidden">
                  {/* 背景装饰 */}
                  <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-purple-500/10 to-transparent rounded-full -translate-y-16 translate-x-16"></div>

                  <div className="flex items-center justify-between mb-8">
                    <div>
                      <h3 className="text-2xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                        个人信息
                      </h3>
                      <p className="text-gray-400 mt-1">管理您的基本信息</p>
                    </div>
                    <Button
                      variant="ghost"
                      onClick={() => setIsEditModalOpen(true)}
                      className="text-purple-400 hover:text-purple-300 hover:bg-purple-500/10 px-6 py-3 rounded-xl transition-all duration-200"
                    >
                      <Edit3 className="h-5 w-5 mr-2" />
                      编辑信息
                    </Button>
                  </div>

                  <div className="grid md:grid-cols-2 gap-8">
                    {/* 姓名 */}
                    <div className="group">
                      <div className="flex items-center space-x-3 p-6 rounded-2xl bg-gradient-to-br from-gray-800/50 to-gray-900/50 border border-gray-700/50 hover:border-purple-500/30 transition-all duration-200">
                        <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-purple-500/20 to-blue-500/20 flex items-center justify-center">
                          <User className="h-6 w-6 text-purple-400" />
                        </div>
                        <div className="flex-1">
                          <label className="block text-sm font-medium text-gray-400 mb-1">
                            姓名
                          </label>
                          <p className="text-lg font-semibold text-white">{profile.name}</p>
                        </div>
                      </div>
                    </div>

                    {/* 邮箱 */}
                    <div className="group">
                      <div className="flex items-center space-x-3 p-6 rounded-2xl bg-gradient-to-br from-gray-800/50 to-gray-900/50 border border-gray-700/50 hover:border-blue-500/30 transition-all duration-200">
                        <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-500/20 flex items-center justify-center">
                          <Mail className="h-6 w-6 text-blue-400" />
                        </div>
                        <div className="flex-1">
                          <label className="block text-sm font-medium text-gray-400 mb-1">
                            邮箱地址
                          </label>
                          <p className="text-lg font-semibold text-white">{profile.email}</p>
                          <p className="text-xs text-gray-500 mt-1">邮箱地址不可修改</p>
                        </div>
                      </div>
                    </div>

                    {/* 公司 */}
                    <div className="group">
                      <div className="flex items-center space-x-3 p-6 rounded-2xl bg-gradient-to-br from-gray-800/50 to-gray-900/50 border border-gray-700/50 hover:border-green-500/30 transition-all duration-200">
                        <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-green-500/20 to-emerald-500/20 flex items-center justify-center">
                          <Building className="h-6 w-6 text-green-400" />
                        </div>
                        <div className="flex-1">
                          <label className="block text-sm font-medium text-gray-400 mb-1">
                            公司名称
                          </label>
                          <p className="text-lg font-semibold text-white">{profile.company || '未填写'}</p>
                        </div>
                      </div>
                    </div>

                    {/* 注册时间 */}
                    <div className="group">
                      <div className="flex items-center space-x-3 p-6 rounded-2xl bg-gradient-to-br from-gray-800/50 to-gray-900/50 border border-gray-700/50 hover:border-orange-500/30 transition-all duration-200">
                        <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-orange-500/20 to-red-500/20 flex items-center justify-center">
                          <Calendar className="h-6 w-6 text-orange-400" />
                        </div>
                        <div className="flex-1">
                          <label className="block text-sm font-medium text-gray-400 mb-1">
                            注册时间
                          </label>
                          <p className="text-lg font-semibold text-white">{formatDate(profile.created_at)}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>
              </motion.div>

              {/* 账户统计信息 - 重新设计为更大气的样式 */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
              >
                <Card glass className="p-8 relative overflow-hidden">
                  {/* 背景装饰 */}
                  <div className="absolute bottom-0 left-0 w-40 h-40 bg-gradient-to-tr from-blue-500/10 to-transparent rounded-full translate-y-20 -translate-x-20"></div>

                  <div className="mb-8">
                    <h3 className="text-2xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                      账户统计
                    </h3>
                    <p className="text-gray-400 mt-1">您的使用情况概览</p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {/* 订阅等级 */}
                    <div className="group">
                      <div className="p-6 rounded-2xl bg-gradient-to-br from-purple-500/10 via-purple-500/5 to-transparent border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 hover:scale-105 min-h-[180px] flex flex-col">
                        <div className="flex items-center justify-between mb-4">
                          <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center shadow-lg">
                            <Crown className="h-6 w-6 text-white" />
                          </div>
                          <div className="text-right">
                            <div className="text-2xl font-bold text-purple-400">
                              {subscription ? subscription.plan?.name || '基础版' : '未订阅'}
                            </div>
                            <div className="text-sm text-gray-400">订阅等级</div>
                          </div>
                        </div>
                        <div className="flex-1">
                          <div className="text-sm font-medium text-gray-300">当前订阅</div>
                          <div className="text-xs text-gray-500 mt-1">
                            {subscription ? `到期时间: ${new Date(subscription.end_date).toLocaleDateString()}` : '升级享受更多服务'}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* 字节使用情况 */}
                    <div className="group">
                      <div className="p-6 rounded-2xl bg-gradient-to-br from-blue-500/10 via-blue-500/5 to-transparent border border-blue-500/20 hover:border-blue-500/40 transition-all duration-300 hover:scale-105 min-h-[180px] flex flex-col">
                        <div className="flex items-center justify-between mb-4">
                          <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center shadow-lg">
                            <Activity className="h-6 w-6 text-white" />
                          </div>
                          <div className="text-right">
                            <div className="text-2xl font-bold text-blue-400">
                              {SubscriptionService.formatBytes(usageStats.totalCharacters)}
                            </div>
                            <div className="text-sm text-gray-400">已使用</div>
                          </div>
                        </div>
                        <div className="flex-1">
                          <div className="text-sm font-medium text-gray-300">字节使用量</div>
                          <div className="text-xs text-gray-500 mt-1">
                            总额度: {subscription ? SubscriptionService.formatBytes(subscription.bytes_quota) : '0 字节'}
                          </div>
                          {/* 使用进度条 */}
                          {subscription && (
                            <div className="mt-3">
                              <div className="w-full bg-gray-700 rounded-full h-2">
                                <div
                                  className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-300"
                                  style={{ width: `${Math.min((usageStats.totalCharacters / subscription.bytes_quota) * 100, 100)}%` }}
                                ></div>
                              </div>
                              <div className="text-xs text-gray-500 mt-1">
                                使用率: {Math.round((usageStats.totalCharacters / subscription.bytes_quota) * 100)}%
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* 剩余配额 */}
                    <div className="group">
                      <div className="p-6 rounded-2xl bg-gradient-to-br from-green-500/10 via-green-500/5 to-transparent border border-green-500/20 hover:border-green-500/40 transition-all duration-300 hover:scale-105 min-h-[180px] flex flex-col">
                        <div className="flex items-center justify-between mb-4">
                          <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center shadow-lg">
                            <Zap className="h-6 w-6 text-white" />
                          </div>
                          <div className="text-right">
                            <div className="text-2xl font-bold text-green-400">
                              {subscription ? SubscriptionService.formatBytes(subscription.bytes_quota - usageStats.totalCharacters) : '0 字节'}
                            </div>
                            <div className="text-sm text-gray-400">剩余</div>
                          </div>
                        </div>
                        <div className="flex-1">
                          <div className="text-sm font-medium text-gray-300">可用配额</div>
                          <div className="text-xs text-gray-500 mt-1">
                            {subscription && subscription.end_date ?
                              `${Math.max(0, Math.ceil((new Date(subscription.end_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)))} 天后到期` :
                              '立即升级获取更多配额'
                            }
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>
              </motion.div>
            </div>
          </div>
        </motion.div>
      </div>

      {/* 编辑模态框 */}
      <ProfileEdit
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onSave={handleEditSave}
      />
    </div>
  );
};
