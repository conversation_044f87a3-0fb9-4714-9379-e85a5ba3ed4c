import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { RefreshCw, Database, BarChart3, AlertCircle } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { UsageService } from '../services/usageService';
import { SubscriptionService } from '../services/subscriptionService';

export const UsageDebug: React.FC = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any>(null);

  useEffect(() => {
    if (user) {
      loadDebugData();
    }
  }, [user]);

  const loadDebugData = async () => {
    if (!user) return;
    
    setLoading(true);
    try {
      // 获取 usage_records 统计
      const usageStats = await UsageService.getCurrentMonthUsage(user.id);
      const userStats = await UsageService.getUserUsageStats(user.id, 30);
      
      // 获取 user_balances 数据
      const balance = await SubscriptionService.getUserBalance(user.id);
      
      setData({
        usageRecords: {
          currentMonth: usageStats,
          last30Days: userStats,
        },
        userBalance: balance,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('加载调试数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const syncData = async () => {
    if (!user) return;
    
    setLoading(true);
    try {
      // 模拟消费一些字节数来测试同步
      await SubscriptionService.consumeUserBytes(user.id, 100);
      
      // 重新加载数据
      await loadDebugData();
      
      alert('数据同步完成！');
    } catch (error) {
      console.error('同步数据失败:', error);
      alert(`同步失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <Card glass>
          <div className="text-center p-8">
            <h2 className="text-xl font-semibold text-white mb-4">请先登录</h2>
            <p className="text-gray-400">您需要登录后才能查看调试信息</p>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="max-w-6xl mx-auto">
            {/* 页面标题 */}
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-white mb-4">
                用量数据调试
              </h1>
              <p className="text-gray-400">
                检查 usage_records 和 user_balances 数据同步状态
              </p>
              
              <div className="mt-4 flex justify-center space-x-4">
                <Button
                  variant="primary"
                  onClick={loadDebugData}
                  disabled={loading}
                >
                  <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                  刷新数据
                </Button>
                <Button
                  variant="glass"
                  onClick={syncData}
                  disabled={loading}
                >
                  <Database className="mr-2 h-4 w-4" />
                  测试同步
                </Button>
              </div>
            </div>

            {loading && (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mx-auto"></div>
                <p className="text-gray-400 mt-2">加载中...</p>
              </div>
            )}

            {data && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* usage_records 数据 */}
                <Card glass>
                  <div className="p-6">
                    <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
                      <BarChart3 className="mr-2" size={24} />
                      Usage Records 表
                    </h2>
                    
                    <div className="space-y-4">
                      <div>
                        <h3 className="font-medium text-white mb-2">当月统计</h3>
                        <div className="bg-gray-800 p-3 rounded text-sm">
                          <p className="text-gray-300">
                            总字节数: <span className="text-white">{data.usageRecords.currentMonth.totalBytes.toLocaleString()}</span>
                          </p>
                          <p className="text-gray-300">
                            总费用: <span className="text-white">${data.usageRecords.currentMonth.totalCost.toFixed(4)}</span>
                          </p>
                        </div>
                      </div>

                      <div>
                        <h3 className="font-medium text-white mb-2">最近30天统计</h3>
                        <div className="bg-gray-800 p-3 rounded text-sm">
                          <p className="text-gray-300">
                            总字节数: <span className="text-white">{data.usageRecords.last30Days.totalBytes.toLocaleString()}</span>
                          </p>
                          <p className="text-gray-300">
                            总费用: <span className="text-white">${data.usageRecords.last30Days.totalCost.toFixed(4)}</span>
                          </p>
                          <p className="text-gray-300">
                            记录数: <span className="text-white">{data.usageRecords.last30Days.records.length}</span>
                          </p>
                        </div>
                      </div>

                      <div>
                        <h3 className="font-medium text-white mb-2">最近记录</h3>
                        <div className="bg-gray-800 p-3 rounded text-sm max-h-40 overflow-y-auto">
                          {data.usageRecords.last30Days.records.slice(0, 5).map((record: any, index: number) => (
                            <div key={index} className="mb-2 pb-2 border-b border-gray-700 last:border-b-0">
                              <p className="text-gray-300">
                                {new Date(record.created_at).toLocaleString('zh-CN')}
                              </p>
                              <p className="text-white">
                                {record.service_type}: {record.characters_used} 字节, ${record.cost.toFixed(4)}
                              </p>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>

                {/* user_balances 数据 */}
                <Card glass>
                  <div className="p-6">
                    <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
                      <Database className="mr-2" size={24} />
                      User Balances 表
                    </h2>
                    
                    {data.userBalance ? (
                      <div className="space-y-4">
                        <div className="bg-gray-800 p-4 rounded">
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <p className="text-gray-400">总字节数</p>
                              <p className="text-white font-medium">
                                {data.userBalance.total_bytes.toLocaleString()}
                              </p>
                            </div>
                            <div>
                              <p className="text-gray-400">已使用</p>
                              <p className="text-white font-medium">
                                {data.userBalance.used_bytes.toLocaleString()}
                              </p>
                            </div>
                            <div>
                              <p className="text-gray-400">可用字节</p>
                              <p className="text-green-400 font-medium">
                                {(data.userBalance.total_bytes - data.userBalance.used_bytes).toLocaleString()}
                              </p>
                            </div>
                            <div>
                              <p className="text-gray-400">免费字节</p>
                              <p className="text-blue-400 font-medium">
                                {data.userBalance.free_bytes.toLocaleString()}
                              </p>
                            </div>
                            <div>
                              <p className="text-gray-400">订阅等级</p>
                              <p className="text-purple-400 font-medium">
                                {SubscriptionService.formatSubscriptionLevel(data.userBalance.subscription_level)}
                              </p>
                            </div>
                            <div>
                              <p className="text-gray-400">更新时间</p>
                              <p className="text-gray-300 text-xs">
                                {new Date(data.userBalance.updated_at).toLocaleString('zh-CN')}
                              </p>
                            </div>
                          </div>
                        </div>

                        {/* 使用率进度条 */}
                        <div>
                          <div className="flex justify-between text-sm mb-2">
                            <span className="text-gray-400">使用率</span>
                            <span className="text-white">
                              {((data.userBalance.used_bytes / data.userBalance.total_bytes) * 100).toFixed(1)}%
                            </span>
                          </div>
                          <div className="w-full bg-gray-700 rounded-full h-2">
                            <div 
                              className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                              style={{ 
                                width: `${Math.min((data.userBalance.used_bytes / data.userBalance.total_bytes) * 100, 100)}%` 
                              }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <AlertCircle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
                        <p className="text-yellow-400">未找到用户余额记录</p>
                        <p className="text-gray-400 text-sm mt-2">
                          可能需要创建用户余额记录
                        </p>
                      </div>
                    )}
                  </div>
                </Card>

                {/* 数据对比 */}
                <Card glass className="lg:col-span-2">
                  <div className="p-6">
                    <h2 className="text-xl font-semibold text-white mb-4">数据对比分析</h2>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="bg-gray-800 p-4 rounded">
                        <h3 className="font-medium text-white mb-2">Usage Records</h3>
                        <p className="text-2xl font-bold text-blue-400">
                          {data.usageRecords.currentMonth.totalBytes.toLocaleString()}
                        </p>
                        <p className="text-gray-400 text-sm">当月使用字节数</p>
                      </div>
                      
                      <div className="bg-gray-800 p-4 rounded">
                        <h3 className="font-medium text-white mb-2">User Balances</h3>
                        <p className="text-2xl font-bold text-purple-400">
                          {data.userBalance ? data.userBalance.used_bytes.toLocaleString() : '0'}
                        </p>
                        <p className="text-gray-400 text-sm">累计使用字节数</p>
                      </div>
                      
                      <div className="bg-gray-800 p-4 rounded">
                        <h3 className="font-medium text-white mb-2">数据状态</h3>
                        <p className={`text-2xl font-bold ${
                          data.userBalance && 
                          Math.abs(data.usageRecords.last30Days.totalBytes - data.userBalance.used_bytes) < 100
                            ? 'text-green-400' 
                            : 'text-red-400'
                        }`}>
                          {data.userBalance && 
                           Math.abs(data.usageRecords.last30Days.totalBytes - data.userBalance.used_bytes) < 100
                            ? '同步' 
                            : '不同步'
                          }
                        </p>
                        <p className="text-gray-400 text-sm">数据同步状态</p>
                      </div>
                    </div>

                    <div className="mt-4 text-xs text-gray-500">
                      <p>更新时间: {data.timestamp}</p>
                      <p>用户ID: {user.id}</p>
                    </div>
                  </div>
                </Card>
              </div>
            )}
          </div>
        </motion.div>
      </div>
    </div>
  );
};
