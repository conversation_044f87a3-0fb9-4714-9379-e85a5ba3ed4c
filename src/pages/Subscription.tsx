import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Check, Crown, Zap, Star, Gift } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { WechatPayModal } from '../components/payment/WechatPayModal';
import { SubscriptionService, type SubscriptionPlan, type UserBalance, type UserSubscription } from '../services/subscriptionService';
import { WechatPayService, type WechatPayOrder } from '../services/wechatPayService';

export const Subscription: React.FC = () => {
  const { user } = useAuth();
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [userBalance, setUserBalance] = useState<UserBalance | null>(null);
  const [currentSubscription, setCurrentSubscription] = useState<UserSubscription | null>(null);
  const [loading, setLoading] = useState(true);
  const [showWechatPay, setShowWechatPay] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null);

  useEffect(() => {
    if (user) {
      loadData();
    }
  }, [user]);

  const loadData = async () => {
    if (!user) return;
    
    setLoading(true);
    try {
      const [plansData, balanceData, subscriptionData] = await Promise.all([
        SubscriptionService.getSubscriptionPlans(),
        SubscriptionService.getUserBalance(user.id),
        SubscriptionService.getUserSubscription(user.id),
      ]);
      
      setPlans(plansData);
      setUserBalance(balanceData);
      setCurrentSubscription(subscriptionData);
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubscribe = (plan: SubscriptionPlan) => {
    setSelectedPlan(plan);
    setShowWechatPay(true);
  };

  const handlePaymentSuccess = async (order: WechatPayOrder) => {
    if (!selectedPlan || !user) return;

    try {
      // 创建订阅
      await SubscriptionService.createSubscription({
        planId: selectedPlan.id,
        userId: user.id,
        orderId: order.id,
      });

      // 刷新数据
      await loadData();
      
      alert(`订阅成功！您已成功订阅${selectedPlan.name}`);
    } catch (error) {
      console.error('创建订阅失败:', error);
      alert('订阅创建失败，请联系客服');
    }
  };

  const getPlanIcon = (planName: string) => {
    switch (planName) {
      case '基础版': return <Zap className="w-8 h-8" />;
      case '标准版': return <Star className="w-8 h-8" />;
      case '专业版': return <Crown className="w-8 h-8" />;
      default: return <Gift className="w-8 h-8" />;
    }
  };

  const getPlanColor = (planName: string) => {
    switch (planName) {
      case '基础版': return 'from-blue-500 to-blue-600';
      case '标准版': return 'from-purple-500 to-purple-600';
      case '专业版': return 'from-yellow-500 to-yellow-600';
      default: return 'from-gray-500 to-gray-600';
    }
  };

  const isCurrentPlan = (plan: SubscriptionPlan) => {
    return currentSubscription?.plan_id === plan.id;
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <Card glass>
          <div className="text-center p-8">
            <h2 className="text-xl font-semibold text-white mb-4">请先登录</h2>
            <p className="text-gray-400">您需要登录后才能查看订阅计划</p>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* 页面标题 */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-white mb-4">
              选择您的订阅计划
            </h1>
            <p className="text-xl text-gray-400 mb-8">
              解锁更多语音合成功能，享受专业级服务
            </p>
            
            {/* 用户当前状态 */}
            {userBalance && (
              <div className="max-w-md mx-auto">
                <Card glass>
                  <div className="p-6 text-center">
                    <h3 className="text-lg font-semibold text-white mb-2">当前状态</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-400">订阅等级:</span>
                        <span className={`font-medium ${SubscriptionService.getSubscriptionLevelColor(userBalance.subscription_level)}`}>
                          {SubscriptionService.formatSubscriptionLevel(userBalance.subscription_level)}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-400">可用字节:</span>
                        <span className="text-white font-medium">
                          {SubscriptionService.formatBytes(userBalance.total_bytes - userBalance.used_bytes)}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-400">总字节数:</span>
                        <span className="text-gray-300">
                          {SubscriptionService.formatBytes(userBalance.total_bytes)}
                        </span>
                      </div>
                    </div>
                  </div>
                </Card>
              </div>
            )}
          </div>

          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto"></div>
              <p className="text-gray-400 mt-4">加载中...</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-7xl mx-auto">
              {/* 免费版卡片 */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                <Card glass className="h-full">
                  <div className="p-6 text-center h-full flex flex-col">
                    <div className="mb-6">
                      <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-gray-500 to-gray-600 rounded-full flex items-center justify-center">
                        <Gift className="w-8 h-8 text-white" />
                      </div>
                      <h3 className="text-xl font-bold text-white mb-2">免费版</h3>
                      <div className="text-3xl font-bold text-white mb-1">¥0</div>
                      <p className="text-gray-400 text-sm">永久免费</p>
                    </div>

                    <div className="flex-1 space-y-3 mb-6">
                      <div className="flex items-center text-sm">
                        <Check className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        <span className="text-gray-300">10,000 字节</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <Check className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        <span className="text-gray-300">基础语音合成</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <Check className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        <span className="text-gray-300">标准音质</span>
                      </div>
                    </div>

                    <Button 
                      variant="glass" 
                      className="w-full"
                      disabled
                    >
                      当前计划
                    </Button>
                  </div>
                </Card>
              </motion.div>

              {/* 付费计划卡片 */}
              {plans.map((plan, index) => (
                <motion.div
                  key={plan.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 + index * 0.1 }}
                >
                  <Card glass className={`h-full ${isCurrentPlan(plan) ? 'ring-2 ring-purple-500' : ''}`}>
                    <div className="p-6 text-center h-full flex flex-col">
                      <div className="mb-6">
                        <div className={`w-16 h-16 mx-auto mb-4 bg-gradient-to-br ${getPlanColor(plan.name)} rounded-full flex items-center justify-center`}>
                          {getPlanIcon(plan.name)}
                        </div>
                        <h3 className="text-xl font-bold text-white mb-2">{plan.name}</h3>
                        <div className="text-3xl font-bold text-white mb-1">¥{plan.price}</div>
                        <p className="text-gray-400 text-sm">每月</p>
                      </div>

                      <div className="flex-1 space-y-3 mb-6">
                        <div className="flex items-center text-sm">
                          <Check className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                          <span className="text-gray-300">
                            {SubscriptionService.formatBytes(plan.bytes_quota)}/月
                          </span>
                        </div>
                        {plan.features.map((feature, featureIndex) => (
                          <div key={featureIndex} className="flex items-center text-sm">
                            <Check className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                            <span className="text-gray-300">{feature}</span>
                          </div>
                        ))}
                      </div>

                      <Button 
                        variant={isCurrentPlan(plan) ? "glass" : "primary"}
                        glow={!isCurrentPlan(plan)}
                        className="w-full"
                        onClick={() => handleSubscribe(plan)}
                        disabled={isCurrentPlan(plan)}
                      >
                        {isCurrentPlan(plan) ? '当前计划' : '立即订阅'}
                      </Button>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </div>
          )}

          {/* 特性说明 */}
          <div className="mt-16 max-w-4xl mx-auto">
            <Card glass>
              <div className="p-8">
                <h3 className="text-2xl font-bold text-white mb-6 text-center">
                  为什么选择 SoulVoice？
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                      <Zap className="w-6 h-6 text-white" />
                    </div>
                    <h4 className="font-semibold text-white mb-2">极速合成</h4>
                    <p className="text-gray-400 text-sm">毫秒级响应，实时生成高质量语音</p>
                  </div>
                  <div className="text-center">
                    <div className="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center">
                      <Star className="w-6 h-6 text-white" />
                    </div>
                    <h4 className="font-semibold text-white mb-2">专业音质</h4>
                    <p className="text-gray-400 text-sm">业界领先的语音合成技术，音质清晰自然</p>
                  </div>
                  <div className="text-center">
                    <div className="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-full flex items-center justify-center">
                      <Crown className="w-6 h-6 text-white" />
                    </div>
                    <h4 className="font-semibold text-white mb-2">专属服务</h4>
                    <p className="text-gray-400 text-sm">专业客服支持，API 优先级保障</p>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </motion.div>

        {/* 微信支付模态框 */}
        {selectedPlan && (
          <WechatPayModal
            isOpen={showWechatPay}
            onClose={() => {
              setShowWechatPay(false);
              setSelectedPlan(null);
            }}
            amount={selectedPlan.price * 100} // 转换为分
            description={`SoulVoice ${selectedPlan.name} 订阅`}
            onSuccess={handlePaymentSuccess}
            onError={(error: string) => {
              console.error('支付失败:', error);
              alert(`支付失败：${error}`);
            }}
          />
        )}
      </div>
    </div>
  );
};
