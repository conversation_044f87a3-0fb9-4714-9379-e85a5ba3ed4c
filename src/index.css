@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局深色滚动条样式 */
* {
  scrollbar-width: thin;
  scrollbar-color: #4b5563 #1f2937;
}

/* Webkit 浏览器滚动条样式 */
*::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

*::-webkit-scrollbar-track {
  background-color: #1f2937;
  border-radius: 4px;
}

*::-webkit-scrollbar-thumb {
  background-color: #4b5563;
  border-radius: 4px;
  border: 1px solid #374151;
}

*::-webkit-scrollbar-thumb:hover {
  background-color: #6b7280;
}

*::-webkit-scrollbar-thumb:active {
  background-color: #9ca3af;
}

*::-webkit-scrollbar-corner {
  background-color: #1f2937;
}

/* 自定义滚动条样式类 */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: #4b5563 #1f2937;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

/* 音频播放器滑块样式 */
.slider {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}

.slider::-webkit-slider-track {
  background: transparent;
  height: 4px;
  border-radius: 2px;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  height: 12px;
  width: 12px;
  border-radius: 50%;
  background: #8b5cf6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 4px 8px rgba(139, 92, 246, 0.3);
}

.slider::-moz-range-track {
  background: transparent;
  height: 4px;
  border-radius: 2px;
  border: none;
}

.slider::-moz-range-thumb {
  height: 12px;
  width: 12px;
  border-radius: 50%;
  background: #8b5cf6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background-color: #1f2937;
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: #4b5563;
  border-radius: 3px;
  border: 1px solid #374151;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: #6b7280;
}

.scrollbar-thin::-webkit-scrollbar-corner {
  background-color: #1f2937;
}

/* 兼容旧的类名 */
.scrollbar-thumb-gray-600::-webkit-scrollbar-thumb {
  background-color: #4b5563;
  border-radius: 0.375rem;
}

.scrollbar-track-gray-800::-webkit-scrollbar-track {
  background-color: #1f2937;
}
