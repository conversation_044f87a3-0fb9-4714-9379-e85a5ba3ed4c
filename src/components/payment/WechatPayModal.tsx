import React, { useState, useEffect } from 'react';
import { X, RefreshCw, CheckCircle, XCir<PERSON>, Clock } from 'lucide-react';
import QRCode from 'qrcode';
import { WechatPayService, type WechatPayOrder } from '../../services/wechatPayService';
import { useAuth } from '../../contexts/AuthContext';

interface WechatPayModalProps {
  isOpen: boolean;
  onClose: () => void;
  amount: number; // 金额（分）
  description: string;
  onSuccess?: (order: WechatPayOrder) => void;
  onError?: (error: string) => void;
}

export const WechatPayModal: React.FC<WechatPayModalProps> = ({
  isOpen,
  onClose,
  amount,
  description,
  onSuccess,
  onError,
}) => {
  const { user } = useAuth();
  const [qrCode, setQrCode] = useState<string>('');
  const [qrCodeDataUrl, setQrCodeDataUrl] = useState<string>('');
  const [orderId, setOrderId] = useState<string>('');
  const [outTradeNo, setOutTradeNo] = useState<string>('');
  const [status, setStatus] = useState<'loading' | 'pending' | 'success' | 'failed' | 'expired'>('loading');
  const [error, setError] = useState<string>('');
  const [timeLeft, setTimeLeft] = useState<number>(30 * 60); // 30分钟倒计时

  useEffect(() => {
    if (isOpen && user) {
      createOrder();
    }
    
    return () => {
      // 清理状态
      setQrCode('');
      setOrderId('');
      setOutTradeNo('');
      setStatus('loading');
      setError('');
      setTimeLeft(30 * 60);
    };
  }, [isOpen, user]);

  // 倒计时
  useEffect(() => {
    if (status === 'pending' && timeLeft > 0) {
      const timer = setTimeout(() => {
        setTimeLeft(prev => prev - 1);
      }, 1000);
      
      return () => clearTimeout(timer);
    } else if (timeLeft === 0 && status === 'pending') {
      setStatus('expired');
    }
  }, [status, timeLeft]);

  const createOrder = async () => {
    if (!user) return;

    setStatus('loading');
    setError('');
    
    try {
      const result = await WechatPayService.createOrder({
        amount,
        description,
        userId: user.id,
      });

      if (result.success && result.data) {
        setQrCode(result.data.qrCode);
        setOrderId(result.data.orderId);
        setOutTradeNo(result.data.outTradeNo);
        setStatus('pending');
        setTimeLeft(30 * 60); // 重置倒计时

        // 生成二维码图片
        generateQRCodeImage(result.data.qrCode);

        // 开始轮询订单状态
        startPolling(result.data.orderId);
      } else {
        setStatus('failed');
        setError(result.error || '创建订单失败');
        onError?.(result.error || '创建订单失败');
      }
    } catch (error) {
      setStatus('failed');
      setError('网络错误，请稍后重试');
      onError?.('网络错误，请稍后重试');
    }
  };

  const generateQRCodeImage = async (url: string) => {
    try {
      const dataUrl = await QRCode.toDataURL(url, {
        width: 200,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });
      setQrCodeDataUrl(dataUrl);
    } catch (error) {
      console.error('生成二维码失败:', error);
    }
  };

  const startPolling = (orderId: string) => {
    WechatPayService.pollOrderStatus(
      orderId,
      (order) => {
        if (order.status === 'paid') {
          setStatus('success');
          onSuccess?.(order);
          // 2秒后自动关闭
          setTimeout(() => {
            onClose();
          }, 2000);
        } else if (order.status === 'failed') {
          setStatus('failed');
          setError('支付失败');
          onError?.('支付失败');
        }
      },
      150, // 5分钟
      2000 // 2秒间隔
    );
  };

  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleRetry = () => {
    createOrder();
  };

  const handleClose = () => {
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 relative">
        {/* 关闭按钮 */}
        <button
          onClick={handleClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
        >
          <X size={24} />
        </button>

        <div className="text-center">
          <h3 className="text-lg font-semibold mb-4 text-gray-900">微信支付</h3>
          
          {/* 加载状态 */}
          {status === 'loading' && (
            <div className="py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500 mx-auto"></div>
              <p className="mt-2 text-gray-600">正在创建订单...</p>
            </div>
          )}

          {/* 待支付状态 */}
          {status === 'pending' && qrCode && (
            <div>
              <div className="bg-gray-100 p-4 rounded-lg mb-4">
                {qrCodeDataUrl ? (
                  <img
                    src={qrCodeDataUrl}
                    alt="支付二维码"
                    className="w-48 h-48 mx-auto"
                  />
                ) : (
                  <div className="w-48 h-48 mx-auto flex items-center justify-center bg-gray-200 rounded">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-500"></div>
                  </div>
                )}
              </div>
              
              <div className="mb-4">
                <p className="text-sm text-gray-600 mb-2">
                  请使用微信扫描二维码完成支付
                </p>
                <p className="text-lg font-semibold text-green-600">
                  ¥{WechatPayService.formatAmount(amount)}
                </p>
                <p className="text-sm text-gray-500 mt-1">{description}</p>
              </div>

              {/* 倒计时 */}
              <div className="flex items-center justify-center text-sm text-gray-500 mb-4">
                <Clock size={16} className="mr-1" />
                <span>剩余时间: {formatTime(timeLeft)}</span>
              </div>

              {/* 订单信息 */}
              <div className="text-xs text-gray-400 mb-4">
                <p>订单号: {outTradeNo}</p>
              </div>
            </div>
          )}

          {/* 支付成功 */}
          {status === 'success' && (
            <div className="py-8">
              <CheckCircle size={48} className="text-green-500 mx-auto mb-4" />
              <p className="text-green-600 font-semibold text-lg">支付成功！</p>
              <p className="text-sm text-gray-500 mt-2">页面将自动关闭...</p>
            </div>
          )}

          {/* 支付失败 */}
          {status === 'failed' && (
            <div className="py-8">
              <XCircle size={48} className="text-red-500 mx-auto mb-4" />
              <p className="text-red-600 font-semibold text-lg">支付失败</p>
              {error && <p className="text-sm text-gray-500 mt-2">{error}</p>}
              <button
                onClick={handleRetry}
                className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 flex items-center mx-auto"
              >
                <RefreshCw size={16} className="mr-2" />
                重试
              </button>
            </div>
          )}

          {/* 订单过期 */}
          {status === 'expired' && (
            <div className="py-8">
              <Clock size={48} className="text-orange-500 mx-auto mb-4" />
              <p className="text-orange-600 font-semibold text-lg">订单已过期</p>
              <p className="text-sm text-gray-500 mt-2">请重新创建订单</p>
              <button
                onClick={handleRetry}
                className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 flex items-center mx-auto"
              >
                <RefreshCw size={16} className="mr-2" />
                重新创建
              </button>
            </div>
          )}

          {/* 底部按钮 */}
          <div className="mt-6 flex justify-center space-x-4">
            <button
              onClick={handleClose}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50"
            >
              取消
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
