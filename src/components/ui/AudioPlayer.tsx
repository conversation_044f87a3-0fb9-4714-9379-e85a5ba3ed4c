import React, { useRef, useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Play, Pause, Download, Volume2, VolumeX } from 'lucide-react';

interface AudioPlayerProps {
  audioUrl: string | null;
  isGenerating?: boolean;
  onDownload?: () => void;
  className?: string;
}

export const AudioPlayer: React.FC<AudioPlayerProps> = ({
  audioUrl,
  isGenerating = false,
  onDownload,
  className = '',
}) => {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);

  // 重置状态当音频URL改变时
  useEffect(() => {
    if (!audioUrl) {
      setIsPlaying(false);
      setCurrentTime(0);
      setDuration(0);
    }
  }, [audioUrl]);

  const handlePlay = () => {
    if (audioRef.current && audioUrl) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
    }
  };

  const handleTimeUpdate = () => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime);
    }
  };

  const handleLoadedMetadata = () => {
    if (audioRef.current) {
      setDuration(audioRef.current.duration);
    }
  };

  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (audioRef.current && duration > 0) {
      const rect = e.currentTarget.getBoundingClientRect();
      const clickX = e.clientX - rect.left;
      const newTime = (clickX / rect.width) * duration;
      audioRef.current.currentTime = newTime;
      setCurrentTime(newTime);
    }
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(e.target.value);
    setVolume(newVolume);
    if (audioRef.current) {
      audioRef.current.volume = newVolume;
    }
    setIsMuted(newVolume === 0);
  };

  const toggleMute = () => {
    if (audioRef.current) {
      if (isMuted) {
        audioRef.current.volume = volume;
        setIsMuted(false);
      } else {
        audioRef.current.volume = 0;
        setIsMuted(true);
      }
    }
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Waveform Visualization */}
      <div className="h-24 bg-gray-800 rounded-lg flex items-center justify-center relative overflow-hidden">
        {audioUrl ? (
          <div className="flex space-x-1 items-end">
            {Array.from({ length: 60 }, (_, i) => {
              const height = Math.random() * 50 + 15;
              const progress = duration > 0 ? currentTime / duration : 0;
              const isActive = i < progress * 60;
              
              return (
                <motion.div
                  key={i}
                  className={`rounded-full transition-all duration-300 ${
                    isActive 
                      ? 'bg-gradient-to-t from-purple-500 to-blue-400' 
                      : 'bg-purple-500/30'
                  }`}
                  style={{
                    width: '2px',
                    height: `${height}px`,
                  }}
                  animate={{
                    scaleY: isPlaying && isActive ? [1, 1.4, 1] : 1,
                    opacity: isActive ? 1 : 0.3,
                  }}
                  transition={{
                    duration: 0.6 + Math.random() * 0.4,
                    repeat: isPlaying && isActive ? Infinity : 0,
                    ease: "easeInOut",
                    delay: i * 0.02,
                  }}
                />
              );
            })}
          </div>
        ) : (
          <div className="text-gray-500 text-sm text-center">
            {isGenerating ? (
              <div className="flex items-center space-x-2">
                <motion.div 
                  className="w-5 h-5 border-2 border-purple-500 border-t-transparent rounded-full"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                />
                <span>正在生成音频...</span>
              </div>
            ) : (
              '点击生成语音按钮开始'
            )}
          </div>
        )}
      </div>

      {/* Hidden Audio Element */}
      {audioUrl && (
        <audio
          ref={audioRef}
          src={audioUrl}
          onEnded={() => {
            setIsPlaying(false);
            setCurrentTime(0);
          }}
          onPlay={() => setIsPlaying(true)}
          onPause={() => setIsPlaying(false)}
          onTimeUpdate={handleTimeUpdate}
          onLoadedMetadata={handleLoadedMetadata}
        />
      )}

      {/* Controls */}
      <div className="flex items-center space-x-4">
        {/* Play/Pause Button */}
        <motion.button
          onClick={handlePlay}
          disabled={!audioUrl}
          className={`p-3 rounded-full transition-all duration-200 ${
            audioUrl
              ? 'bg-purple-500 hover:bg-purple-600 shadow-lg hover:shadow-purple-500/25'
              : 'bg-gray-600 cursor-not-allowed'
          }`}
          whileHover={audioUrl ? { scale: 1.05 } : {}}
          whileTap={audioUrl ? { scale: 0.95 } : {}}
        >
          <motion.div
            animate={isPlaying ? { scale: [1, 1.1, 1] } : { scale: 1 }}
            transition={{ duration: 0.6, repeat: isPlaying ? Infinity : 0 }}
          >
            {isPlaying ? (
              <Pause className="h-6 w-6" />
            ) : (
              <Play className="h-6 w-6 ml-0.5" />
            )}
          </motion.div>
        </motion.button>

        {/* Progress Section */}
        <div className="flex-1 space-y-2">
          {/* Progress Bar */}
          <div 
            className="bg-gray-700 rounded-full h-3 relative overflow-hidden cursor-pointer group"
            onClick={handleProgressClick}
          >
            <motion.div
              className="bg-gradient-to-r from-purple-500 to-blue-500 h-3 rounded-full relative"
              style={{ 
                width: duration > 0 ? `${(currentTime / duration) * 100}%` : '0%' 
              }}
              transition={{ duration: 0.1 }}
            >
              {/* Progress indicator */}
              <div className="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 w-3 h-3 bg-white rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-opacity" />
            </motion.div>
            {/* Hover effect */}
            <div className="absolute inset-0 bg-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-full" />
          </div>
          
          {/* Time Display */}
          <div className="flex justify-between text-xs text-gray-400">
            <span>{formatTime(currentTime)}</span>
            <span>{formatTime(duration)}</span>
          </div>
        </div>

        {/* Volume Control */}
        <div className="flex items-center space-x-2">
          <button
            onClick={toggleMute}
            className="p-2 rounded-full hover:bg-gray-700 transition-colors"
          >
            {isMuted || volume === 0 ? (
              <VolumeX className="h-4 w-4 text-gray-400" />
            ) : (
              <Volume2 className="h-4 w-4 text-gray-400" />
            )}
          </button>
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={isMuted ? 0 : volume}
            onChange={handleVolumeChange}
            className="w-16 h-1 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
            style={{
              background: `linear-gradient(to right, #8b5cf6 0%, #8b5cf6 ${(isMuted ? 0 : volume) * 100}%, #374151 ${(isMuted ? 0 : volume) * 100}%, #374151 100%)`
            }}
          />
        </div>

        {/* Download Button */}
        {onDownload && (
          <motion.button
            onClick={onDownload}
            disabled={!audioUrl}
            className={`p-2 rounded-full transition-all duration-200 ${
              audioUrl
                ? 'hover:bg-gray-700 text-gray-400 hover:text-white'
                : 'text-gray-600 cursor-not-allowed'
            }`}
            whileHover={audioUrl ? { scale: 1.05 } : {}}
            whileTap={audioUrl ? { scale: 0.95 } : {}}
          >
            <Download className="h-5 w-5" />
          </motion.button>
        )}
      </div>
    </div>
  );
};
