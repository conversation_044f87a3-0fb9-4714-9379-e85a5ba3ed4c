import React, { useState, useRef } from 'react';
import { Camera, Upload, X, Check } from 'lucide-react';
import { Button } from './Button';
import { supabase } from '../../lib/supabase';
import { useAuth } from '../../contexts/AuthContext';

interface AvatarUploadProps {
  currentAvatarUrl?: string;
  onAvatarUpdate: (avatarUrl: string) => void;
  className?: string;
}

export const AvatarUpload: React.FC<AvatarUploadProps> = ({
  currentAvatarUrl,
  onAvatarUpdate,
  className = '',
}) => {
  const { user, profile } = useAuth();
  const [uploading, setUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      alert('请选择图片文件');
      return;
    }

    // 验证文件大小 (5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('图片大小不能超过 5MB');
      return;
    }

    setSelectedFile(file);
    
    // 创建预览URL
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreviewUrl(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  const handleUpload = async () => {
    if (!selectedFile || !user) return;

    try {
      setUploading(true);

      // 生成唯一文件名
      const fileExt = selectedFile.name.split('.').pop();
      const fileName = `${user.id}-${Date.now()}.${fileExt}`;
      const filePath = `${user.id}/${fileName}`;

      // 删除旧头像（如果存在）
      if (currentAvatarUrl && currentAvatarUrl.includes('supabase')) {
        try {
          const oldFileName = currentAvatarUrl.split('/').pop();
          if (oldFileName) {
            await supabase.storage
              .from('avatars')
              .remove([`${user.id}/${oldFileName}`]);
          }
        } catch (deleteError) {
          console.warn('Failed to delete old avatar:', deleteError);
        }
      }

      // 上传到 Supabase Storage
      const { data, error } = await supabase.storage
        .from('avatars')
        .upload(filePath, selectedFile, {
          cacheControl: '3600',
          upsert: true, // 允许覆盖
        });

      if (error) {
        console.error('Upload error:', error);
        throw new Error(`上传失败: ${error.message}`);
      }

      // 获取公共URL
      const { data: { publicUrl } } = supabase.storage
        .from('avatars')
        .getPublicUrl(filePath);

      if (!publicUrl) {
        throw new Error('无法获取头像URL');
      }

      // 更新用户资料
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ avatar_url: publicUrl })
        .eq('id', user.id);

      if (updateError) {
        console.error('Profile update error:', updateError);
        throw new Error(`更新资料失败: ${updateError.message}`);
      }

      onAvatarUpdate(publicUrl);
      setPreviewUrl(null);
      setSelectedFile(null);

    } catch (error) {
      console.error('Error uploading avatar:', error);
      const errorMessage = error instanceof Error ? error.message : '头像上传失败，请重试';
      alert(errorMessage);
    } finally {
      setUploading(false);
    }
  };

  const handleCancel = () => {
    setPreviewUrl(null);
    setSelectedFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const triggerFileSelect = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className={`relative ${className}`}>
      {/* 头像显示区域 */}
      <div className="relative inline-block">
        <div className="w-40 h-40 rounded-full bg-gradient-to-br from-purple-500 via-pink-500 to-blue-500 p-1 shadow-2xl">
          <div className="w-full h-full rounded-full bg-gray-900 flex items-center justify-center overflow-hidden">
            {previewUrl ? (
              <img
                src={previewUrl}
                alt="头像预览"
                className="w-full h-full object-cover"
              />
            ) : currentAvatarUrl ? (
              <img
                src={currentAvatarUrl}
                alt="用户头像"
                className="w-full h-full object-cover"
              />
            ) : (
              <span className="text-5xl font-bold text-white">
                {profile?.name?.charAt(0).toUpperCase() || 'U'}
              </span>
            )}
          </div>
        </div>
        
        {/* 上传按钮 */}
        <button
          onClick={triggerFileSelect}
          disabled={uploading}
          className="absolute bottom-2 right-2 w-12 h-12 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center hover:scale-110 transition-all duration-200 shadow-lg disabled:opacity-50"
        >
          <Camera className="h-5 w-5 text-white" />
        </button>
      </div>

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* 预览和操作按钮 */}
      {previewUrl && (
        <div className="mt-4 p-4 bg-gray-800/50 rounded-lg">
          <div className="flex items-center justify-between mb-3">
            <span className="text-sm text-gray-300">预览新头像</span>
            <div className="flex space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCancel}
                disabled={uploading}
              >
                <X className="h-4 w-4 mr-1" />
                取消
              </Button>
              <Button
                variant="primary"
                size="sm"
                onClick={handleUpload}
                disabled={uploading}
                glow
              >
                {uploading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-1"></div>
                    上传中...
                  </>
                ) : (
                  <>
                    <Check className="h-4 w-4 mr-1" />
                    确认上传
                  </>
                )}
              </Button>
            </div>
          </div>
          
          {selectedFile && (
            <div className="text-xs text-gray-400">
              文件名: {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
            </div>
          )}
        </div>
      )}

      {/* 上传提示 */}
      {!previewUrl && (
        <div className="mt-2 text-xs text-gray-400 text-center">
          点击相机图标上传头像
          <br />
          支持 JPG、PNG 格式，最大 5MB
        </div>
      )}
    </div>
  );
};
