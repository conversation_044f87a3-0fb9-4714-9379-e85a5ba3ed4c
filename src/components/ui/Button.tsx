import React from 'react';
import { clsx } from 'clsx';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'ghost' | 'glass';
  size?: 'sm' | 'md' | 'lg';
  glow?: boolean;
  children: React.ReactNode;
}

export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  glow = false,
  className,
  children,
  ...props
}) => {
  return (
    <button
      className={clsx(
        'relative inline-flex items-center justify-center font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed',
        {
          // Variants
          'bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white': variant === 'primary',
          'bg-gray-700 hover:bg-gray-600 text-white': variant === 'secondary',
          'bg-transparent hover:bg-gray-800 text-gray-300': variant === 'ghost',
          'backdrop-blur-sm bg-white/10 hover:bg-white/20 text-white border border-white/20': variant === 'glass',
          
          // Sizes
          'px-3 py-1.5 text-sm rounded-md': size === 'sm',
          'px-4 py-2 text-sm rounded-lg': size === 'md',
          'px-6 py-3 text-base rounded-xl': size === 'lg',
          
          // Glow effect
          'shadow-lg shadow-purple-500/25 hover:shadow-purple-500/40': glow && variant === 'primary',
        },
        className
      )}
      {...props}
    >
      {children}
    </button>
  );
};