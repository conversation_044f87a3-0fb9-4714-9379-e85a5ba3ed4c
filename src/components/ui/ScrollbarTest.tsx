import React from 'react';

export const ScrollbarTest: React.FC = () => {
  return (
    <div className="p-8 bg-gray-900 text-white min-h-screen">
      <h1 className="text-2xl font-bold mb-6">滚动条样式测试</h1>
      
      {/* 默认滚动条测试 */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-4">默认滚动条（全局样式）</h2>
        <div className="h-40 overflow-y-auto bg-gray-800 p-4 rounded-lg">
          {Array.from({ length: 20 }, (_, i) => (
            <div key={i} className="py-2 border-b border-gray-700">
              这是第 {i + 1} 行内容，用于测试默认滚动条样式
            </div>
          ))}
        </div>
      </div>

      {/* 细滚动条测试 */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-4">细滚动条（scrollbar-thin）</h2>
        <div className="h-40 overflow-y-auto bg-gray-800 p-4 rounded-lg scrollbar-thin">
          {Array.from({ length: 20 }, (_, i) => (
            <div key={i} className="py-2 border-b border-gray-700">
              这是第 {i + 1} 行内容，用于测试细滚动条样式
            </div>
          ))}
        </div>
      </div>

      {/* 水平滚动条测试 */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-4">水平滚动条测试</h2>
        <div className="h-20 overflow-x-auto bg-gray-800 p-4 rounded-lg">
          <div className="w-[800px] flex space-x-4">
            {Array.from({ length: 10 }, (_, i) => (
              <div key={i} className="flex-shrink-0 w-32 h-12 bg-purple-600 rounded flex items-center justify-center">
                项目 {i + 1}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 双向滚动条测试 */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-4">双向滚动条测试</h2>
        <div className="h-40 w-80 overflow-auto bg-gray-800 p-4 rounded-lg">
          <div className="w-[600px]">
            {Array.from({ length: 15 }, (_, i) => (
              <div key={i} className="py-2 border-b border-gray-700 whitespace-nowrap">
                这是第 {i + 1} 行内容，内容很长很长很长很长很长很长很长很长
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="text-sm text-gray-400">
        <p>✅ 滚动条应该显示为深色主题</p>
        <p>✅ 滚动条轨道为深灰色 (#1f2937)</p>
        <p>✅ 滚动条滑块为中灰色 (#4b5563)</p>
        <p>✅ 悬停时滑块变亮 (#6b7280)</p>
      </div>
    </div>
  );
};
