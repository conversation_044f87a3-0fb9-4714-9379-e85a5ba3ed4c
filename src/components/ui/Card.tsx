import React from 'react';
import { clsx } from 'clsx';

interface CardProps {
  className?: string;
  children: React.ReactNode;
  glass?: boolean;
  glow?: boolean;
}

export const Card: React.FC<CardProps> = ({ className, children, glass = false, glow = false }) => {
  return (
    <div
      className={clsx(
        'rounded-xl p-6 transition-all duration-300',
        {
          'backdrop-blur-sm bg-white/5 border border-white/10': glass,
          'bg-gray-800 border border-gray-700': !glass,
          'shadow-xl shadow-purple-500/10 hover:shadow-purple-500/20': glow,
          'hover:border-purple-500/30': glow,
        },
        className
      )}
    >
      {children}
    </div>
  );
};