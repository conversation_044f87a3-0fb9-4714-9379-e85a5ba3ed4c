import React, { useState, useEffect } from 'react';
import { User, Mail, Building, Save, X, AlertCircle } from 'lucide-react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Card } from '../ui/Card';
import { useAuth } from '../../contexts/AuthContext';
import type { Profile } from '../../lib/supabase';

interface ProfileEditProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
}

interface FormData {
  name: string;
  company: string;
}

interface FormErrors {
  name?: string;
  company?: string;
}

export const ProfileEdit: React.FC<ProfileEditProps> = ({
  isOpen,
  onClose,
  onSave,
}) => {
  const { profile, updateProfile } = useAuth();
  const [formData, setFormData] = useState<FormData>({
    name: '',
    company: '',
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [saving, setSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    if (profile) {
      const initialData = {
        name: profile.name || '',
        company: profile.company || '',
      };
      setFormData(initialData);
      setHasChanges(false);
    }
  }, [profile, isOpen]);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // 验证姓名
    if (!formData.name.trim()) {
      newErrors.name = '姓名不能为空';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = '姓名至少需要2个字符';
    } else if (formData.name.trim().length > 50) {
      newErrors.name = '姓名不能超过50个字符';
    }

    // 验证公司名称（可选）
    if (formData.company && formData.company.length > 100) {
      newErrors.company = '公司名称不能超过100个字符';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // 检查是否有变更
    const originalData = {
      name: profile?.name || '',
      company: profile?.company || '',
    };
    const newData = { ...formData, [field]: value };
    setHasChanges(
      newData.name !== originalData.name || 
      newData.company !== originalData.company
    );

    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setSaving(true);
      
      await updateProfile({
        name: formData.name.trim(),
        company: formData.company.trim() || null,
      });

      onSave();
      onClose();
    } catch (error) {
      console.error('Error updating profile:', error);
      // 这里可以添加错误提示
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    if (hasChanges) {
      if (window.confirm('您有未保存的更改，确定要取消吗？')) {
        onClose();
      }
    } else {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <Card glass className="w-full max-w-lg max-h-[90vh] overflow-y-auto p-8 relative">
        {/* 背景装饰 */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-purple-500/20 to-transparent rounded-full -translate-y-16 translate-x-16"></div>
        <div className="relative flex items-center justify-between mb-8">
          <div>
            <h2 className="text-2xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              编辑个人信息
            </h2>
            <p className="text-gray-400 mt-1">更新您的基本信息</p>
          </div>
          <button
            onClick={handleCancel}
            className="text-gray-400 hover:text-white transition-colors p-2 hover:bg-gray-800/50 rounded-lg"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="relative space-y-6">
          {/* 姓名输入 */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-3">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-purple-500/20 to-blue-500/20 flex items-center justify-center">
                  <User className="h-4 w-4 text-purple-400" />
                </div>
                <span>姓名 *</span>
              </div>
            </label>
            <Input
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="请输入您的姓名"
              error={errors.name}
              className="text-lg"
            />
            {errors.name && (
              <div className="flex items-center mt-2 text-red-400 text-sm">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.name}
              </div>
            )}
          </div>

          {/* 邮箱显示（只读） */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-3">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500/20 to-cyan-500/20 flex items-center justify-center">
                  <Mail className="h-4 w-4 text-blue-400" />
                </div>
                <span>邮箱地址</span>
              </div>
            </label>
            <div className="px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-xl text-gray-400 text-lg">
              {profile?.email}
            </div>
            <p className="text-xs text-gray-500 mt-2">邮箱地址不可修改</p>
          </div>

          {/* 公司输入 */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-3">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-green-500/20 to-emerald-500/20 flex items-center justify-center">
                  <Building className="h-4 w-4 text-green-400" />
                </div>
                <span>公司名称</span>
              </div>
            </label>
            <Input
              value={formData.company}
              onChange={(e) => handleInputChange('company', e.target.value)}
              placeholder="请输入您的公司名称（可选）"
              error={errors.company}
              className="text-lg"
            />
            {errors.company && (
              <div className="flex items-center mt-2 text-red-400 text-sm">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.company}
              </div>
            )}
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-end space-x-3 mt-8">
          <Button
            variant="ghost"
            onClick={handleCancel}
            disabled={saving}
          >
            取消
          </Button>
          <Button
            variant="primary"
            onClick={handleSave}
            disabled={saving || !hasChanges || Object.keys(errors).length > 0}
            glow
          >
            {saving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                保存中...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                保存更改
              </>
            )}
          </Button>
        </div>

        {/* 提示信息 */}
        {hasChanges && (
          <div className="mt-4 p-3 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
            <div className="flex items-center text-yellow-400 text-sm">
              <AlertCircle className="h-4 w-4 mr-2" />
              您有未保存的更改
            </div>
          </div>
        )}
      </Card>
    </div>
  );
};
