<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音色列表测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        .voice-item {
            background: #2a2a2a;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #8b5cf6;
        }
        .voice-name {
            font-weight: bold;
            color: #8b5cf6;
        }
        .voice-uri {
            font-family: monospace;
            font-size: 12px;
            color: #888;
            word-break: break-all;
        }
        .voice-preview {
            color: #ccc;
            font-style: italic;
        }
        .loading {
            text-align: center;
            color: #8b5cf6;
        }
        .error {
            color: #ef4444;
            background: #2a1a1a;
            padding: 10px;
            border-radius: 8px;
            border-left: 4px solid #ef4444;
        }
        button {
            background: #8b5cf6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: #7c3aed;
        }
        button:disabled {
            background: #555;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1>🎵 音色列表测试</h1>
    <p>测试 SoulVoice 音色列表 API 功能</p>
    
    <div>
        <button onclick="loadVoices()">加载音色列表</button>
        <button onclick="clearResults()">清除结果</button>
    </div>
    
    <div id="status" class="loading">点击"加载音色列表"开始测试</div>
    <div id="results"></div>

    <script>
        const API_URL = 'https://api.siliconflow.cn/v1/audio/voice/list';
        const API_KEY = 'sk-ynabzdlcumjklweexfyruujoydkzfnqctcnlsiifbloqgdcw';

        async function loadVoices() {
            const statusEl = document.getElementById('status');
            const resultsEl = document.getElementById('results');
            
            statusEl.innerHTML = '<div class="loading">🔄 正在加载音色列表...</div>';
            resultsEl.innerHTML = '';

            try {
                const response = await fetch(API_URL, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${API_KEY}`,
                        'Content-Type': 'application/json',
                        'User-Agent': 'SoulVoice/1.0.0',
                    },
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                if (!data.result || !Array.isArray(data.result)) {
                    throw new Error('响应格式无效：未找到 result 数组');
                }

                statusEl.innerHTML = `<div style="color: #10b981;">✅ 成功加载 ${data.result.length} 个音色</div>`;
                
                // 显示音色列表
                data.result.forEach((voice, index) => {
                    const voiceEl = document.createElement('div');
                    voiceEl.className = 'voice-item';
                    voiceEl.innerHTML = `
                        <div class="voice-name">${index + 1}. ${voice.customName || '未命名音色'}</div>
                        <div class="voice-preview">"${voice.text || '无预览文本'}"</div>
                        <div style="margin: 8px 0;">
                            <strong>模型:</strong> ${voice.model}<br>
                            <strong>URI:</strong> <span class="voice-uri">${voice.uri}</span>
                        </div>
                        <button onclick="testVoice('${voice.uri}', '${voice.customName}')">测试此音色</button>
                    `;
                    resultsEl.appendChild(voiceEl);
                });

            } catch (error) {
                statusEl.innerHTML = `<div class="error">❌ 加载失败: ${error.message}</div>`;
                console.error('加载音色列表失败:', error);
            }
        }

        async function testVoice(uri, name) {
            const testText = '你好，这是音色测试。';
            
            try {
                const response = await fetch('https://api.siliconflow.cn/v1/audio/speech', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${API_KEY}`,
                        'Content-Type': 'application/json',
                        'User-Agent': 'SoulVoice/1.0.0',
                    },
                    body: JSON.stringify({
                        model: 'fnlp/MOSS-TTSD-v0.5',
                        input: testText,
                        voice: uri,
                    }),
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const audioData = await response.arrayBuffer();
                const blob = new Blob([audioData], { type: 'audio/mpeg' });
                const audioUrl = URL.createObjectURL(blob);
                
                const audio = new Audio(audioUrl);
                audio.play();
                
                alert(`✅ 音色 "${name}" 测试成功！正在播放音频...`);
                
            } catch (error) {
                alert(`❌ 音色 "${name}" 测试失败: ${error.message}`);
            }
        }

        function clearResults() {
            document.getElementById('status').innerHTML = '点击"加载音色列表"开始测试';
            document.getElementById('results').innerHTML = '';
        }
    </script>
</body>
</html>
