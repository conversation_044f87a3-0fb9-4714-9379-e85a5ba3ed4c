# ===========================================
# SoulVoice 环境变量配置模板
# ===========================================
# 复制此文件为 .env.local 并填入实际值

# ===========================================
# Supabase 配置 (必需)
# ===========================================
# 从 Supabase 项目设置中获取
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key

# ===========================================
# API 配置 (必需)
# ===========================================
# API 基础 URL，生产环境通常是 Supabase Functions URL
VITE_API_BASE_URL=https://your-project-id.supabase.co/functions/v1

# ===========================================
# 语音服务配置 (必需)
# ===========================================
# SiliconFlow API 密钥，用于语音合成服务
VITE_SILICONFLOW_API_KEY=sk-your-siliconflow-api-key

# ===========================================
# 支付配置 (可选)
# ===========================================
# 微信支付商户号
VITE_WECHAT_PAY_MERCHANT_ID=your-wechat-merchant-id

# 微信支付应用 ID
VITE_WECHAT_PAY_APP_ID=your-wechat-app-id

# ===========================================
# 应用配置 (可选)
# ===========================================
# 应用环境 (development/production)
NODE_ENV=production

# 应用版本
VITE_APP_VERSION=1.0.0

# 应用标题
VITE_APP_TITLE=SoulVoice

# ===========================================
# 调试配置 (开发环境)
# ===========================================
# 启用调试模式 (true/false)
VITE_DEBUG_MODE=false

# 启用详细日志 (true/false)
VITE_VERBOSE_LOGGING=false

# ===========================================
# 第三方服务配置 (可选)
# ===========================================
# Google Analytics ID
VITE_GA_TRACKING_ID=G-XXXXXXXXXX

# Sentry DSN (错误监控)
VITE_SENTRY_DSN=https://your-sentry-dsn

# ===========================================
# 部署配置说明
# ===========================================
# 1. 在 Vercel 中设置环境变量时，去掉 VITE_ 前缀
# 2. 例如：VITE_SUPABASE_URL 在 Vercel 中设置为 vite_supabase_url
# 3. 必需的环境变量：
#    - vite_supabase_url
#    - vite_supabase_anon_key
#    - vite_api_base_url
#    - vite_siliconflow_api_key
# 4. 可选的环境变量根据功能需求添加
