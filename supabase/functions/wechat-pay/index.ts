import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { corsHeaders, handleCors } from '../_shared/cors.ts';
import { createSupabaseClient, createErrorResponse } from '../_shared/auth.ts';
import { Logger } from '../_shared/logger.ts';
import { WechatPayClient, type CreateOrderRequest } from '../_shared/wechatpay.ts';
import { initWechatPayConfig, validateWechatPayConfig, getConfigMapping, type WechatPayConfig } from '../_shared/wechat-config.ts';

// 全局配置变量
let wechatPayConfig: WechatPayConfig | null = null;

serve(async (req) => {
  const startTime = Date.now();
  const logContext = {
    method: req.method,
    url: req.url,
    userAgent: req.headers.get('user-agent'),
    requestId: crypto.randomUUID(),
  };

  Logger.logRequestStart(req.method, req.url, logContext);

  // 处理 CORS
  const corsResponse = handleCors(req);
  if (corsResponse) {
    return corsResponse;
  }

  try {
    // 初始化配置（如果还未初始化）
    if (!wechatPayConfig) {
      try {
        wechatPayConfig = await initWechatPayConfig();

        if (!validateWechatPayConfig(wechatPayConfig)) {
          console.error('微信支付配置验证失败');
          console.error('当前配置状态:', getConfigMapping());
          return createErrorResponse('微信支付配置验证失败', 500, 'CONFIG_INVALID');
        }

        Logger.logInfo('微信支付配置初始化成功', {
          appId: wechatPayConfig.appId,
          mchId: wechatPayConfig.mchId,
          serialNo: wechatPayConfig.serialNo,
        });
      } catch (error) {
        console.error('微信支付配置初始化失败:', error);
        console.error('当前配置状态:', getConfigMapping());
        return createErrorResponse(`微信支付配置错误: ${error.message}`, 500, 'CONFIG_ERROR');
      }
    }

    const url = new URL(req.url);
    const path = url.pathname;

    // 路由处理
    if (path.includes('/create-order') && req.method === 'POST') {
      return await handleCreateOrder(req, logContext);
    } else if (path.includes('/query-order') && req.method === 'GET') {
      return await handleQueryOrder(req, logContext);
    } else if (path.includes('/notify') && req.method === 'POST') {
      return await handlePaymentNotify(req, logContext);
    } else if (path.includes('/orders') && req.method === 'GET') {
      return await handleGetUserOrders(req, logContext);
    }

    return createErrorResponse('接口不存在', 404, 'NOT_FOUND');
  } catch (error) {
    const duration = Date.now() - startTime;
    Logger.error('Unhandled error in wechat-pay function', error, {
      ...logContext,
      duration,
    });

    return createErrorResponse('服务器内部错误', 500, 'INTERNAL_ERROR');
  }
});

/**
 * 创建支付订单
 */
async function handleCreateOrder(req: Request, logContext: any): Promise<Response> {
  const startTime = Date.now();
  
  try {
    const supabase = createSupabaseClient();
    
    // 验证用户身份
    const authHeader = req.headers.get('authorization');
    if (!authHeader) {
      return createErrorResponse('缺少认证信息', 401, 'UNAUTHORIZED');
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      Logger.error('Authentication failed', authError, logContext);
      return createErrorResponse('认证失败', 401, 'UNAUTHORIZED');
    }

    logContext.userId = user.id;

    // 解析请求体
    const { amount, description, userId } = await req.json();

    // 验证参数
    if (!amount || !description || !userId || userId !== user.id) {
      return createErrorResponse('参数错误', 400, 'INVALID_PARAMS');
    }

    if (amount < 1 || amount > 100000000) { // 1分到1000万分
      return createErrorResponse('金额超出范围', 400, 'INVALID_AMOUNT');
    }

    // 生成订单号
    const outTradeNo = `SV_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // 创建微信支付客户端
    const wxpayClient = new WechatPayClient(wechatPayConfig!);
    
    // 调用微信支付 API 创建订单
    const notifyUrl = `${Deno.env.get('SUPABASE_URL')}/functions/v1/wechat-pay/notify`;
    
    const createOrderRequest: CreateOrderRequest = {
      out_trade_no: outTradeNo,
      description,
      amount: { total: amount },
      notify_url: notifyUrl,
    };

    Logger.logInfo('Creating WeChat Pay order', {
      ...logContext,
      outTradeNo,
      amount,
      description: description.substring(0, 50),
    });

    const wechatPayResponse = await wxpayClient.createNativeOrder(createOrderRequest);

    // 保存订单到数据库
    const { data: order, error: dbError } = await supabase
      .from('wechat_pay_orders')
      .insert({
        user_id: userId,
        amount,
        description,
        out_trade_no: outTradeNo,
        prepay_id: wechatPayResponse.prepay_id,
        status: 'pending',
      })
      .select()
      .single();

    if (dbError) {
      Logger.error('Failed to save order to database', dbError, logContext);
      return createErrorResponse('保存订单失败', 500, 'DATABASE_ERROR');
    }

    const duration = Date.now() - startTime;
    Logger.logRequestEnd('POST', '/create-order', 200, duration, {
      ...logContext,
      orderId: order.id,
      outTradeNo,
    });

    return new Response(
      JSON.stringify({
        success: true,
        data: {
          orderId: order.id,
          qrCode: wechatPayResponse.code_url,
          outTradeNo,
          amount,
          description,
        },
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders,
        },
      }
    );
  } catch (error) {
    const duration = Date.now() - startTime;
    Logger.error('Create order failed', error, {
      ...logContext,
      duration,
    });

    return createErrorResponse(
      error.message || '创建订单失败',
      500,
      'CREATE_ORDER_ERROR'
    );
  }
}

/**
 * 查询订单状态
 */
async function handleQueryOrder(req: Request, logContext: any): Promise<Response> {
  try {
    const supabase = createSupabaseClient();
    
    // 验证用户身份
    const authHeader = req.headers.get('authorization');
    if (!authHeader) {
      return createErrorResponse('缺少认证信息', 401, 'UNAUTHORIZED');
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return createErrorResponse('认证失败', 401, 'UNAUTHORIZED');
    }

    // 从 URL 中获取订单 ID
    const url = new URL(req.url);
    const orderId = url.pathname.split('/').pop();
    
    if (!orderId) {
      return createErrorResponse('缺少订单ID', 400, 'MISSING_ORDER_ID');
    }

    // 查询订单
    const { data: order, error: dbError } = await supabase
      .from('wechat_pay_orders')
      .select('*')
      .eq('id', orderId)
      .eq('user_id', user.id)
      .single();

    if (dbError || !order) {
      return createErrorResponse('订单不存在', 404, 'ORDER_NOT_FOUND');
    }

    // 如果订单还是 pending 状态，尝试查询微信支付状态
    if (order.status === 'pending') {
      try {
        const wxpayClient = new WechatPayClient(wechatPayConfig!);
        const wxPayOrder = await wxpayClient.queryOrder(order.out_trade_no);
        
        // 更新订单状态
        if (wxPayOrder.trade_state === 'SUCCESS') {
          const { data: updatedOrder } = await supabase
            .from('wechat_pay_orders')
            .update({
              status: 'paid',
              transaction_id: wxPayOrder.transaction_id,
              paid_at: new Date().toISOString(),
            })
            .eq('id', orderId)
            .select()
            .single();
          
          if (updatedOrder) {
            order.status = 'paid';
            order.transaction_id = wxPayOrder.transaction_id;
            order.paid_at = updatedOrder.paid_at;
          }
        } else if (wxPayOrder.trade_state === 'CLOSED' || wxPayOrder.trade_state === 'PAYERROR') {
          await supabase
            .from('wechat_pay_orders')
            .update({ status: 'failed' })
            .eq('id', orderId);
          
          order.status = 'failed';
        }
      } catch (error) {
        Logger.error('Failed to query WeChat Pay order status', error, logContext);
        // 不影响返回本地订单状态
      }
    }

    return new Response(
      JSON.stringify({
        success: true,
        data: {
          order,
        },
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders,
        },
      }
    );
  } catch (error) {
    Logger.error('Query order failed', error, logContext);
    return createErrorResponse('查询订单失败', 500, 'QUERY_ORDER_ERROR');
  }
}

/**
 * 处理微信支付通知
 */
async function handlePaymentNotify(req: Request, logContext: any): Promise<Response> {
  try {
    const supabase = createSupabaseClient();

    // 获取请求头
    const timestamp = req.headers.get('wechatpay-timestamp');
    const nonce = req.headers.get('wechatpay-nonce');
    const signature = req.headers.get('wechatpay-signature');
    const serialNo = req.headers.get('wechatpay-serial');

    if (!timestamp || !nonce || !signature || !serialNo) {
      Logger.error('Missing required headers in payment notification', null, logContext);
      return new Response(
        JSON.stringify({ code: 'FAIL', message: '缺少必要的请求头' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // 获取请求体
    const body = await req.text();

    Logger.logInfo('Received payment notification', {
      ...logContext,
      timestamp,
      nonce,
      serialNo,
      bodyLength: body.length,
    });

    // 创建微信支付客户端
    const wxpayClient = new WechatPayClient(wechatPayConfig!);

    // 验证签名
    const isValidSignature = await wxpayClient.verifyNotification(
      timestamp,
      nonce,
      body,
      signature
    );

    if (!isValidSignature) {
      Logger.error('Invalid signature in payment notification', null, logContext);
      return new Response(
        JSON.stringify({ code: 'FAIL', message: '签名验证失败' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // 解析通知数据
    const notification = JSON.parse(body);

    // 解密资源数据
    const decryptedData = await wxpayClient.decryptNotification(
      notification.resource.ciphertext,
      notification.resource.associated_data,
      notification.resource.nonce
    );

    const { out_trade_no, transaction_id, trade_state, trade_state_desc } = decryptedData;

    Logger.logInfo('Decrypted payment notification', {
      ...logContext,
      outTradeNo: out_trade_no,
      transactionId: transaction_id,
      tradeState: trade_state,
    });

    // 查找订单
    const { data: order, error: findError } = await supabase
      .from('wechat_pay_orders')
      .select('*')
      .eq('out_trade_no', out_trade_no)
      .single();

    if (findError || !order) {
      Logger.error('Order not found for payment notification', findError, {
        ...logContext,
        outTradeNo: out_trade_no,
      });
      return new Response(
        JSON.stringify({ code: 'FAIL', message: '订单不存在' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // 更新订单状态
    const updateData: any = {
      transaction_id,
      notify_data: notification,
    };

    if (trade_state === 'SUCCESS') {
      updateData.status = 'paid';
      updateData.paid_at = new Date().toISOString();
    } else if (trade_state === 'CLOSED' || trade_state === 'PAYERROR') {
      updateData.status = 'failed';
    }

    const { error: updateError } = await supabase
      .from('wechat_pay_orders')
      .update(updateData)
      .eq('id', order.id);

    if (updateError) {
      Logger.error('Failed to update order status', updateError, {
        ...logContext,
        orderId: order.id,
        tradeState: trade_state,
      });
      return new Response(
        JSON.stringify({ code: 'FAIL', message: '更新订单状态失败' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }

    Logger.logInfo('Payment notification processed successfully', {
      ...logContext,
      orderId: order.id,
      tradeState: trade_state,
      amount: order.amount,
    });

    // 返回成功响应
    return new Response(
      JSON.stringify({ code: 'SUCCESS', message: '成功' }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      }
    );
  } catch (error) {
    Logger.error('Payment notification processing failed', error, logContext);
    return new Response(
      JSON.stringify({ code: 'FAIL', message: '处理失败' }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}

/**
 * 获取用户订单列表
 */
async function handleGetUserOrders(req: Request, logContext: any): Promise<Response> {
  try {
    const supabase = createSupabaseClient();

    // 验证用户身份
    const authHeader = req.headers.get('authorization');
    if (!authHeader) {
      return createErrorResponse('缺少认证信息', 401, 'UNAUTHORIZED');
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);

    if (authError || !user) {
      return createErrorResponse('认证失败', 401, 'UNAUTHORIZED');
    }

    // 获取查询参数
    const url = new URL(req.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = Math.min(parseInt(url.searchParams.get('limit') || '20'), 100);
    const status = url.searchParams.get('status');

    // 构建查询
    let query = supabase
      .from('wechat_pay_orders')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (status) {
      query = query.eq('status', status);
    }

    // 分页
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    const { data: orders, error: queryError, count } = await query;

    if (queryError) {
      Logger.error('Failed to query user orders', queryError, logContext);
      return createErrorResponse('查询订单失败', 500, 'QUERY_ERROR');
    }

    return new Response(
      JSON.stringify({
        success: true,
        data: {
          orders: orders || [],
          pagination: {
            page,
            limit,
            total: count || 0,
            totalPages: Math.ceil((count || 0) / limit),
          },
        },
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders,
        },
      }
    );
  } catch (error) {
    Logger.error('Get user orders failed', error, logContext);
    return createErrorResponse('获取订单列表失败', 500, 'GET_ORDERS_ERROR');
  }
}
