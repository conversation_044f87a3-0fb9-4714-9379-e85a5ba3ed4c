import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { corsHeaders, handleCors } from '../_shared/cors.ts';
import { createSupabaseClient, createErrorResponse } from '../_shared/auth.ts';
import { Logger } from '../_shared/logger.ts';

/**
 * 订阅 Webhook 处理器
 * 处理支付成功后的订阅创建和用户余额更新
 */
serve(async (req) => {
  const startTime = Date.now();
  const logContext = {
    method: req.method,
    url: req.url,
    userAgent: req.headers.get('user-agent'),
    requestId: crypto.randomUUID(),
  };

  Logger.logRequestStart(req.method, req.url, logContext);

  // 处理 CORS
  const corsResponse = handleCors(req);
  if (corsResponse) {
    return corsResponse;
  }

  try {
    const url = new URL(req.url);
    const path = url.pathname;

    if (path.includes('/process-payment') && req.method === 'POST') {
      return await handleProcessPayment(req, logContext);
    }

    return createErrorResponse('接口不存在', 404, 'NOT_FOUND');
  } catch (error) {
    const duration = Date.now() - startTime;
    Logger.error('Unhandled error in subscription-webhook function', error, {
      ...logContext,
      duration,
    });

    return createErrorResponse('服务器内部错误', 500, 'INTERNAL_ERROR');
  }
});

/**
 * 处理支付成功后的订阅创建
 */
async function handleProcessPayment(req: Request, logContext: any): Promise<Response> {
  const startTime = Date.now();
  
  try {
    const supabase = createSupabaseClient();
    
    const { orderId, planId, userId } = await req.json();

    if (!orderId || !planId || !userId) {
      return createErrorResponse('缺少必需参数', 400, 'MISSING_PARAMS');
    }

    Logger.logInfo('Processing subscription payment', {
      ...logContext,
      orderId,
      planId,
      userId,
    });

    // 验证支付订单状态
    const { data: order, error: orderError } = await supabase
      .from('wechat_pay_orders')
      .select('*')
      .eq('id', orderId)
      .eq('user_id', userId)
      .eq('status', 'paid')
      .single();

    if (orderError || !order) {
      Logger.error('Payment order not found or not paid', orderError, logContext);
      return createErrorResponse('支付订单不存在或未支付', 400, 'INVALID_ORDER');
    }

    // 获取订阅计划信息
    const { data: plan, error: planError } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('id', planId)
      .eq('is_active', true)
      .single();

    if (planError || !plan) {
      Logger.error('Subscription plan not found', planError, logContext);
      return createErrorResponse('订阅计划不存在', 400, 'INVALID_PLAN');
    }

    // 检查是否已经创建过订阅
    const { data: existingSubscription } = await supabase
      .from('user_subscriptions')
      .select('id')
      .eq('order_id', orderId)
      .single();

    if (existingSubscription) {
      Logger.logInfo('Subscription already exists for this order', {
        ...logContext,
        subscriptionId: existingSubscription.id,
      });
      return new Response(
        JSON.stringify({
          success: true,
          message: '订阅已存在',
          subscriptionId: existingSubscription.id,
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders,
          },
        }
      );
    }

    // 计算订阅结束时间
    const startDate = new Date();
    const endDate = new Date(startDate.getTime() + plan.duration_days * 24 * 60 * 60 * 1000);

    // 创建订阅记录
    const { data: subscription, error: subscriptionError } = await supabase
      .from('user_subscriptions')
      .insert({
        user_id: userId,
        plan_id: planId,
        order_id: orderId,
        status: 'active',
        bytes_quota: plan.bytes_quota,
        bytes_used: 0,
        start_date: startDate.toISOString(),
        end_date: endDate.toISOString(),
      })
      .select()
      .single();

    if (subscriptionError) {
      Logger.error('Failed to create subscription', subscriptionError, logContext);
      return createErrorResponse('创建订阅失败', 500, 'CREATE_SUBSCRIPTION_ERROR');
    }

    // 更新用户余额
    await updateUserBalance(supabase, userId, plan, logContext);

    const duration = Date.now() - startTime;
    Logger.logRequestEnd('POST', '/process-payment', 200, duration, {
      ...logContext,
      subscriptionId: subscription.id,
      planName: plan.name,
      bytesQuota: plan.bytes_quota,
    });

    return new Response(
      JSON.stringify({
        success: true,
        data: {
          subscriptionId: subscription.id,
          planName: plan.name,
          bytesQuota: plan.bytes_quota,
          endDate: endDate.toISOString(),
        },
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders,
        },
      }
    );
  } catch (error) {
    const duration = Date.now() - startTime;
    Logger.error('Process payment failed', error, {
      ...logContext,
      duration,
    });

    return createErrorResponse(
      error.message || '处理支付失败',
      500,
      'PROCESS_PAYMENT_ERROR'
    );
  }
}

/**
 * 更新用户余额
 */
async function updateUserBalance(
  supabase: any,
  userId: string,
  plan: any,
  logContext: any
): Promise<void> {
  try {
    // 获取当前用户余额
    const { data: currentBalance, error: balanceError } = await supabase
      .from('user_balances')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (balanceError && balanceError.code !== 'PGRST116') {
      throw balanceError;
    }

    // 确定订阅等级
    let subscriptionLevel: 'free' | 'basic' | 'standard' | 'professional' = 'free';
    if (plan.name === '基础版') subscriptionLevel = 'basic';
    else if (plan.name === '标准版') subscriptionLevel = 'standard';
    else if (plan.name === '专业版') subscriptionLevel = 'professional';

    if (currentBalance) {
      // 更新现有余额
      const { error: updateError } = await supabase
        .from('user_balances')
        .update({
          total_bytes: currentBalance.total_bytes + plan.bytes_quota,
          subscription_level: subscriptionLevel,
          updated_at: new Date().toISOString(),
        })
        .eq('user_id', userId);

      if (updateError) throw updateError;

      Logger.logInfo('User balance updated', {
        ...logContext,
        previousTotal: currentBalance.total_bytes,
        newTotal: currentBalance.total_bytes + plan.bytes_quota,
        addedBytes: plan.bytes_quota,
        subscriptionLevel,
      });
    } else {
      // 创建新的余额记录
      const { error: createError } = await supabase
        .from('user_balances')
        .insert({
          user_id: userId,
          total_bytes: 10000 + plan.bytes_quota, // 免费额度 + 订阅额度
          used_bytes: 0,
          free_bytes: 10000,
          subscription_level: subscriptionLevel,
        });

      if (createError) throw createError;

      Logger.logInfo('User balance created', {
        ...logContext,
        totalBytes: 10000 + plan.bytes_quota,
        freeBytes: 10000,
        subscriptionBytes: plan.bytes_quota,
        subscriptionLevel,
      });
    }
  } catch (error) {
    Logger.error('Failed to update user balance', error, logContext);
    throw error;
  }
}
