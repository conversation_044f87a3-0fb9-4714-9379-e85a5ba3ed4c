import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { corsHeaders, handleCors } from '../_shared/cors.ts';
import { initWechatPayConfig, validateWechatPayConfig, getConfigMapping } from '../_shared/wechat-config.ts';

/**
 * 微信支付配置测试工具
 * 用于验证环境变量配置是否正确
 */
serve(async (req) => {
  // 处理 CORS
  const corsResponse = handleCors(req);
  if (corsResponse) {
    return corsResponse;
  }

  try {
    const configStatus = getConfigMapping();
    
    let initResult = null;
    let validationResult = false;
    let errorMessage = null;

    try {
      // 尝试初始化配置
      const config = await initWechatPayConfig();
      initResult = {
        appId: config.appId,
        mchId: config.mchId,
        serialNo: config.serialNo,
        privateKeyLength: config.privateKey.length,
        privateKeyFormat: config.privateKey.includes('BEGIN PRIVATE KEY') ? 'PEM格式正确' : 'PEM格式错误',
        apiV3KeyLength: config.apiV3Key.length,
      };

      // 验证配置
      validationResult = validateWechatPayConfig(config);
    } catch (error) {
      errorMessage = error.message;
    }

    const response = {
      success: initResult !== null && validationResult,
      timestamp: new Date().toISOString(),
      configStatus,
      initResult,
      validationResult,
      errorMessage,
      recommendations: generateRecommendations(configStatus, errorMessage),
    };

    return new Response(
      JSON.stringify(response, null, 2),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders,
        },
      }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders,
        },
      }
    );
  }
});

function generateRecommendations(configStatus: Record<string, string>, errorMessage: string | null): string[] {
  const recommendations: string[] = [];

  // 检查必需的配置
  if (configStatus.WECHAT_APP_ID === '未配置') {
    recommendations.push('请配置 WECHAT_APP_ID 环境变量');
  } else if (!configStatus.WECHAT_APP_ID.startsWith('wx')) {
    recommendations.push('WECHAT_APP_ID 格式不正确，应以 wx 开头');
  }

  if (configStatus.WECHAT_MCH_ID === '未配置') {
    recommendations.push('请配置 WECHAT_MCH_ID 环境变量');
  } else if (!/^\d{8,10}$/.test(configStatus.WECHAT_MCH_ID)) {
    recommendations.push('WECHAT_MCH_ID 格式不正确，应为8-10位数字');
  }

  if (configStatus.WECHAT_CERT_SERIAL_NUMBER === '未配置') {
    recommendations.push('请配置 WECHAT_CERT_SERIAL_NUMBER 环境变量');
  }

  if (configStatus.WECHAT_API_V3_KEY === '未配置') {
    recommendations.push('请配置 WECHAT_API_V3_KEY 环境变量');
  }

  // 检查私钥配置
  if (configStatus.WECHAT_PRIVATE_KEY === '未配置' && configStatus.WECHAT_KEY_PATH === '未配置') {
    recommendations.push('请配置 WECHAT_PRIVATE_KEY 或 WECHAT_KEY_PATH 环境变量');
  }

  if (configStatus.WECHAT_KEY_PATH !== '未配置' && configStatus.WECHAT_PRIVATE_KEY === '未配置') {
    recommendations.push('检测到 WECHAT_KEY_PATH 配置，但在 Supabase Functions 中建议直接使用 WECHAT_PRIVATE_KEY');
  }

  // 检查证书配置
  if (configStatus.WECHAT_CERT_PATH === '未配置' && configStatus.WECHAT_PUBLIC_KEY === '未配置') {
    recommendations.push('建议配置 WECHAT_CERT_PATH 或 WECHAT_PUBLIC_KEY（用于验证微信支付平台签名）');
  }

  // 根据错误信息提供建议
  if (errorMessage) {
    if (errorMessage.includes('无法读取私钥文件')) {
      recommendations.push('私钥文件路径不正确或文件不存在，建议直接配置 WECHAT_PRIVATE_KEY');
    }
    if (errorMessage.includes('私钥格式不正确')) {
      recommendations.push('私钥应为完整的 PEM 格式，包含 -----BEGIN PRIVATE KEY----- 和 -----END PRIVATE KEY-----');
    }
  }

  if (recommendations.length === 0) {
    recommendations.push('配置看起来正确！可以开始测试支付功能');
  }

  return recommendations;
}
