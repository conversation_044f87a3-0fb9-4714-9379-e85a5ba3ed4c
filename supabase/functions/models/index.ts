import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { corsHeaders, handleCors } from '../_shared/cors.ts';
import { validateApiKey, createErrorResponse, createSupabaseClient } from '../_shared/auth.ts';
import { Logger } from '../_shared/logger.ts';

// 支持的模型列表
const supportedModels = [
  {
    id: 'FunAudioLLM/CosyVoice2-0.5B',
    name: 'CosyVoice2-0.5B',
    description: '高质量的中文语音合成模型，支持多种音色和情感表达',
    provider: 'FunAudioLLM',
    languages: ['zh-CN'],
    features: ['multi-voice', 'emotion', 'speed-control'],
    default: true,
    sample_rate: [16000, 22050, 24000],
    speed_range: { min: 0.5, max: 2.0 }
  },
  {
    id: 'fnlp/MOSS-TTSD-v0.5',
    name: 'MOSS-TTSD-v0.5',
    description: '基于 MOSS 的文本转语音模型，专注于中文语音合成',
    provider: 'fnlp',
    languages: ['zh-CN'],
    features: ['multi-voice', 'speed-control'],
    default: false,
    sample_rate: [16000, 22050, 24000],
    speed_range: { min: 0.5, max: 2.0 }
  }
];

// 音色兼容性映射
const voiceCompatibility = {
  'FunAudioLLM/CosyVoice2-0.5B': [
    'speech:mcg3fdnx:clzkyf4vy00e5qr6hywum4u84:bzznlkuhcjzpbosexitr',
    'speech:uk8wtvcp:clzkyf4vy00e5qr6hywum4u84:mkrflgylzvfoeppiebis',
    'speech:rnhqhqhq:clzkyf4vy00e5qr6hywum4u84:rnhqhqhqrnhqhqhq'
  ],
  'fnlp/MOSS-TTSD-v0.5': [
    'speech:mcg3fdnx:clzkyf4vy00e5qr6hywum4u84:bzznlkuhcjzpbosexitr',
    'speech:uk8wtvcp:clzkyf4vy00e5qr6hywum4u84:mkrflgylzvfoeppiebis'
  ]
};

serve(async (req) => {
  const startTime = Date.now();

  // 处理 CORS
  const corsResponse = handleCors(req);
  if (corsResponse) return corsResponse;

  // 只允许 GET 请求
  if (req.method !== 'GET') {
    return createErrorResponse('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
  }

  // 创建日志上下文
  const logContext = Logger.createContext();
  Logger.logRequestStart('GET', '/models', logContext);

  try {
    // 创建 Supabase 客户端
    const supabase = createSupabaseClient();

    // 验证 API 密钥
    const authHeader = req.headers.get('authorization');
    const authResult = await validateApiKey(supabase, authHeader);

    if (!authResult.isValid) {
      Logger.warn('API key validation failed', {
        ...logContext,
        error: authResult.error
      });
      return createErrorResponse(
        authResult.error || 'Unauthorized',
        401,
        'UNAUTHORIZED'
      );
    }

    const { userId } = authResult;
    logContext.userId = userId;

    // 获取查询参数
    const url = new URL(req.url);
    const includeVoices = url.searchParams.get('include_voices') === 'true';

    // 构建响应数据
    const responseData = {
      models: supportedModels.map(model => ({
        ...model,
        compatible_voices: includeVoices ? voiceCompatibility[model.id] || [] : undefined
      })),
      total: supportedModels.length,
      default_model: supportedModels.find(m => m.default)?.id || supportedModels[0].id
    };

    const duration = Date.now() - startTime;
    Logger.logRequestEnd('GET', '/models', 200, duration, {
      ...logContext,
      modelCount: supportedModels.length,
      includeVoices
    });

    return new Response(
      JSON.stringify(responseData),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'X-Request-ID': logContext.requestId,
          ...corsHeaders
        }
      }
    );

  } catch (error) {
    const duration = Date.now() - startTime;
    Logger.error('Models API error', error, logContext);
    Logger.logRequestEnd('GET', '/models', 500, duration, logContext);

    return createErrorResponse(
      'Internal server error',
      500,
      'INTERNAL_SERVER_ERROR'
    );
  }
});
