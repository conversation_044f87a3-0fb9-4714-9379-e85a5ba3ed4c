import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { corsHeaders, handleCors } from '../_shared/cors.ts';
import { validateApiKey, createErrorResponse, createSupabaseClient } from '../_shared/auth.ts';
import { Logger } from '../_shared/logger.ts';
import { SiliconFlowClient } from '../_shared/siliconflow.ts';

interface Voice {
  id: string;
  name: string;
  language: string;
  gender: string;
  preview_url?: string;
  type: 'system' | 'custom';
}

serve(async (req) => {
  const startTime = Date.now();

  // 处理 CORS
  const corsResponse = handleCors(req);
  if (corsResponse) return corsResponse;

  // 只允许 GET 请求
  if (req.method !== 'GET') {
    return createErrorResponse('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
  }

  // 创建日志上下文
  const logContext = Logger.createContext();
  Logger.logRequestStart('GET', '/voices', logContext);

  try {
    // 创建 Supabase 客户端
    const supabase = createSupabaseClient();

    // 验证 API 密钥
    const authHeader = req.headers.get('authorization');
    const authResult = await validateApiKey(supabase, authHeader);

    if (!authResult.isValid) {
      Logger.warn('API key validation failed', {
        ...logContext,
        error: authResult.error
      });
      return createErrorResponse(
        authResult.error || 'Unauthorized',
        401,
        'UNAUTHORIZED'
      );
    }

    const { userId } = authResult;
    logContext.userId = userId;

    // 获取系统音色和用户自定义音色
    const { data: voices, error } = await supabase
      .from('voices')
      .select('*')
      .or(`voice_type.eq.system,user_id.eq.${userId}`)
      .eq('is_active', true)
      .order('voice_type', { ascending: true })
      .order('name', { ascending: true });

    if (error) {
      console.error('Database error:', error);
      return createErrorResponse(
        'Failed to fetch voices', 
        500, 
        'DATABASE_ERROR'
      );
    }

    // 格式化响应
    const formattedVoices: Voice[] = (voices || []).map(voice => ({
      id: voice.uri || voice.id,
      name: voice.name,
      language: voice.language || 'zh-CN',
      gender: voice.gender || 'unknown',
      preview_url: voice.preview_url,
      type: voice.voice_type === 'system' ? 'system' : 'custom'
    }));

    // 如果没有音色，返回默认音色列表
    if (formattedVoices.length === 0) {
      Logger.info('No voices found in database, returning default voices', logContext);

      const siliconFlowClient = new SiliconFlowClient();
      const defaultVoices = siliconFlowClient.getAvailableVoices().map(voice => ({
        ...voice,
        type: 'system' as const
      }));

      const duration = Date.now() - startTime;
      Logger.logRequestEnd('GET', '/voices', 200, duration, {
        ...logContext,
        voiceCount: defaultVoices.length,
        source: 'default'
      });

      return new Response(
        JSON.stringify({
          voices: defaultVoices,
          total: defaultVoices.length
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
            'X-Request-ID': logContext.requestId,
            ...corsHeaders
          }
        }
      );
    }

    const duration = Date.now() - startTime;
    Logger.logRequestEnd('GET', '/voices', 200, duration, {
      ...logContext,
      voiceCount: formattedVoices.length,
      source: 'database'
    });

    return new Response(
      JSON.stringify({
        voices: formattedVoices,
        total: formattedVoices.length
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'X-Request-ID': logContext.requestId,
          ...corsHeaders
        }
      }
    );

  } catch (error) {
    const duration = Date.now() - startTime;
    Logger.error('Voices API error', error, logContext);
    Logger.logRequestEnd('GET', '/voices', 500, duration, logContext);

    return createErrorResponse(
      'Internal server error',
      500,
      'INTERNAL_SERVER_ERROR'
    );
  }
});
