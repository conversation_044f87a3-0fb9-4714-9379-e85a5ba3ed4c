import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { corsHeaders, handleCors } from '../_shared/cors.ts';

serve(async (req) => {
  // 处理 CORS
  const corsResponse = handleCors(req);
  if (corsResponse) return corsResponse;

  if (req.method !== 'POST') {
    return new Response('Method not allowed', { status: 405 });
  }

  try {
    // 检查环境变量
    const siliconFlowApiKey = Deno.env.get('SILICONFLOW_API_KEY');
    
    // 解析请求体
    const body = await req.json();
    
    // 构建调试信息
    const debugInfo = {
      timestamp: new Date().toISOString(),
      environment: {
        siliconFlowApiKey: siliconFlowApiKey ? `${siliconFlowApiKey.substring(0, 10)}...` : 'NOT_SET',
        denoVersion: Deno.version.deno,
      },
      request: {
        body: body,
        headers: Object.fromEntries(req.headers.entries())
      }
    };

    // 尝试调用 SiliconFlow API
    const siliconFlowRequest = {
      model: body.model || 'FunAudioLLM/CosyVoice2-0.5B', // 支持模型参数
      input: body.text || '测试',
      voice: body.voice || 'speech:mcg3fdnx:clzkyf4vy00e5qr6hywum4u84:bzznlkuhcjzpbosexitr',
      speed: body.speed || 1.0,
      response_format: 'mp3'
    };

    console.log('Debug info:', JSON.stringify(debugInfo, null, 2));
    console.log('SiliconFlow request:', JSON.stringify(siliconFlowRequest, null, 2));

    if (!siliconFlowApiKey) {
      return new Response(
        JSON.stringify({
          error: 'SILICONFLOW_API_KEY not set',
          debug: debugInfo
        }),
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders
          }
        }
      );
    }

    // 调用 SiliconFlow API
    const siliconFlowResponse = await fetch('https://api.siliconflow.cn/v1/audio/speech', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${siliconFlowApiKey}`,
        'Content-Type': 'application/json',
        'User-Agent': 'SoulVoice-Debug/1.0.0',
      },
      body: JSON.stringify(siliconFlowRequest),
    });

    console.log('SiliconFlow response status:', siliconFlowResponse.status);
    console.log('SiliconFlow response headers:', Object.fromEntries(siliconFlowResponse.headers.entries()));

    if (!siliconFlowResponse.ok) {
      const errorText = await siliconFlowResponse.text();
      console.error('SiliconFlow API error:', errorText);
      
      return new Response(
        JSON.stringify({
          error: 'SiliconFlow API error',
          status: siliconFlowResponse.status,
          statusText: siliconFlowResponse.statusText,
          response: errorText,
          debug: debugInfo,
          siliconFlowRequest: siliconFlowRequest
        }),
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders
          }
        }
      );
    }

    // 检查响应内容类型
    const contentType = siliconFlowResponse.headers.get('content-type');
    console.log('Response content type:', contentType);

    if (contentType?.includes('audio/')) {
      // 返回音频数据
      const audioData = await siliconFlowResponse.arrayBuffer();
      console.log('Audio data size:', audioData.byteLength);
      
      return new Response(audioData, {
        status: 200,
        headers: {
          'Content-Type': 'audio/mpeg',
          'Content-Length': audioData.byteLength.toString(),
          'X-Debug': 'success',
          ...corsHeaders
        }
      });
    } else {
      // 返回非音频响应
      const responseText = await siliconFlowResponse.text();
      console.log('Non-audio response:', responseText);
      
      return new Response(
        JSON.stringify({
          error: 'Unexpected response format',
          contentType: contentType,
          response: responseText,
          debug: debugInfo
        }),
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders
          }
        }
      );
    }

  } catch (error) {
    console.error('Debug TTS error:', error);
    
    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      }
    );
  }
});
