import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { corsHeaders, handleCors } from '../_shared/cors.ts';
import { validateApiKey, createErrorResponse, createSupabaseClient } from '../_shared/auth.ts';
import { Logger } from '../_shared/logger.ts';

interface UsageStats {
  current_month: {
    total_requests: number;
    total_characters: number;
    total_cost: number;
    tts_requests: number;
    clone_requests: number;
  };
  today: {
    total_requests: number;
    total_characters: number;
    total_cost: number;
  };
  recent_usage: Array<{
    date: string;
    requests: number;
    characters: number;
    cost: number;
  }>;
}

serve(async (req) => {
  const startTime = Date.now();

  // 处理 CORS
  const corsResponse = handleCors(req);
  if (corsResponse) return corsResponse;

  // 只允许 GET 请求
  if (req.method !== 'GET') {
    return createErrorResponse('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
  }

  // 创建日志上下文
  const logContext = Logger.createContext();
  Logger.logRequestStart('GET', '/usage', logContext);

  try {
    // 创建 Supabase 客户端
    const supabase = createSupabaseClient();

    // 验证 API 密钥
    const authHeader = req.headers.get('authorization');
    const authResult = await validateApiKey(supabase, authHeader);

    if (!authResult.isValid) {
      Logger.warn('API key validation failed', {
        ...logContext,
        error: authResult.error
      });
      return createErrorResponse(
        authResult.error || 'Unauthorized',
        401,
        'UNAUTHORIZED'
      );
    }

    const { userId } = authResult;
    logContext.userId = userId;

    // 获取当前月份的开始和结束时间
    const now = new Date();
    const currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const currentMonthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);
    
    // 获取今天的开始和结束时间
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const todayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);

    // 查询当前月份的用量统计
    const { data: monthlyUsage, error: monthlyError } = await supabase
      .from('usage_records')
      .select('service_type, characters_used, cost')
      .eq('user_id', userId)
      .gte('created_at', currentMonthStart.toISOString())
      .lte('created_at', currentMonthEnd.toISOString());

    if (monthlyError) {
      console.error('Monthly usage query error:', monthlyError);
      return createErrorResponse(
        'Failed to fetch usage data', 
        500, 
        'DATABASE_ERROR'
      );
    }

    // 查询今天的用量统计
    const { data: todayUsage, error: todayError } = await supabase
      .from('usage_records')
      .select('characters_used, cost')
      .eq('user_id', userId)
      .gte('created_at', todayStart.toISOString())
      .lte('created_at', todayEnd.toISOString());

    if (todayError) {
      console.error('Today usage query error:', todayError);
      return createErrorResponse(
        'Failed to fetch usage data', 
        500, 
        'DATABASE_ERROR'
      );
    }

    // 查询最近7天的用量统计
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const { data: recentUsage, error: recentError } = await supabase
      .from('usage_records')
      .select('created_at, characters_used, cost')
      .eq('user_id', userId)
      .gte('created_at', sevenDaysAgo.toISOString())
      .order('created_at', { ascending: true });

    if (recentError) {
      console.error('Recent usage query error:', recentError);
      return createErrorResponse(
        'Failed to fetch usage data', 
        500, 
        'DATABASE_ERROR'
      );
    }

    // 计算当前月份统计
    const monthlyStats = (monthlyUsage || []).reduce(
      (acc, record) => {
        acc.total_requests++;
        acc.total_characters += record.characters_used || 0;
        acc.total_cost += parseFloat(record.cost || '0');
        
        if (record.service_type === 'tts') {
          acc.tts_requests++;
        } else if (record.service_type === 'clone') {
          acc.clone_requests++;
        }
        
        return acc;
      },
      {
        total_requests: 0,
        total_characters: 0,
        total_cost: 0,
        tts_requests: 0,
        clone_requests: 0,
      }
    );

    // 计算今天统计
    const todayStats = (todayUsage || []).reduce(
      (acc, record) => {
        acc.total_requests++;
        acc.total_characters += record.characters_used || 0;
        acc.total_cost += parseFloat(record.cost || '0');
        return acc;
      },
      {
        total_requests: 0,
        total_characters: 0,
        total_cost: 0,
      }
    );

    // 按日期分组最近7天的用量
    const dailyUsage = new Map<string, { requests: number; characters: number; cost: number }>();
    
    (recentUsage || []).forEach(record => {
      const date = new Date(record.created_at).toISOString().split('T')[0];
      const existing = dailyUsage.get(date) || { requests: 0, characters: 0, cost: 0 };
      
      existing.requests++;
      existing.characters += record.characters_used || 0;
      existing.cost += parseFloat(record.cost || '0');
      
      dailyUsage.set(date, existing);
    });

    // 转换为数组格式
    const recentUsageArray = Array.from(dailyUsage.entries()).map(([date, stats]) => ({
      date,
      ...stats
    }));

    const response: UsageStats = {
      current_month: monthlyStats,
      today: todayStats,
      recent_usage: recentUsageArray
    };

    const duration = Date.now() - startTime;
    Logger.logRequestEnd('GET', '/usage', 200, duration, {
      ...logContext,
      monthlyRequests: response.current_month.total_requests,
      todayRequests: response.today.total_requests
    });

    return new Response(
      JSON.stringify(response),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'X-Request-ID': logContext.requestId,
          ...corsHeaders
        }
      }
    );

  } catch (error) {
    const duration = Date.now() - startTime;
    Logger.error('Usage API error', error, logContext);
    Logger.logRequestEnd('GET', '/usage', 500, duration, logContext);

    return createErrorResponse(
      'Internal server error',
      500,
      'INTERNAL_SERVER_ERROR'
    );
  }
});
