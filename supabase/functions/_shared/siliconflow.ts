import { Logger } from './logger.ts';
import type { TTSRequestBody } from './validation.ts';

export interface SiliconFlowRequest {
  model: string;
  input: string;
  voice: string;
  speed?: number;
  response_format?: string;
  sample_rate?: number;
}

export interface SiliconFlowResponse {
  success: boolean;
  audioData?: ArrayBuffer;
  error?: string;
}

// 默认音色映射表（作为后备）
const DEFAULT_VOICE_MAPPING: Record<string, string> = {
  'zh-CN-XiaoxiaoNeural': 'speech:mcg3fdnx:clzkyf4vy00e5qr6hywum4u84:bzznlkuhcjzpbosexitr',
  'zh-CN-YunxiNeural': 'speech:uk8wtvcp:clzkyf4vy00e5qr6hywum4u84:mkrflgylzvfoeppiebis',
  'zh-CN-XiaoyiNeural': 'speech:2c2hp73s:clzkyf4vy00e5qr6hywum4u84:itjmezhxyynkyzrhhjav',
  'zh-CN-YunjianNeural': 'speech:ugda7m5w:clzkyf4vy00e5qr6hywum4u84:ovfbgvptkblslfdboyia',
};

export class SiliconFlowClient {
  private static readonly API_URL = 'https://api.siliconflow.cn/v1/audio/speech';
  private static readonly DEFAULT_MODEL = 'FunAudioLLM/CosyVoice2-0.5B';
  private static readonly TIMEOUT_MS = 30000; // 30秒超时

  private apiKey: string;

  constructor(apiKey?: string) {
    this.apiKey = apiKey || Deno.env.get('SILICONFLOW_API_KEY') || '';
    if (!this.apiKey) {
      throw new Error('SiliconFlow API key is required');
    }
  }

  /**
   * 将用户友好的音色 ID 映射到 SiliconFlow 的音色 URI
   */
  private mapVoice(voice: string): string {
    // 如果已经是完整的 speech: URI，直接返回
    if (voice.startsWith('speech:')) {
      return voice;
    }

    // 否则使用默认映射
    return DEFAULT_VOICE_MAPPING[voice] || voice;
  }

  /**
   * 构建 SiliconFlow API 请求体
   */
  private buildRequest(data: TTSRequestBody): SiliconFlowRequest {
    // 使用第一个可用音色作为默认值
    const defaultVoice = 'speech:mcg3fdnx:clzkyf4vy00e5qr6hywum4u84:bzznlkuhcjzpbosexitr';

    const request: SiliconFlowRequest = {
      model: data.model || this.DEFAULT_MODEL,
      input: data.text,
      voice: this.mapVoice(data.voice || defaultVoice),
      speed: data.speed || 1.0,
      response_format: data.response_format || 'mp3'
    };

    // 添加可选参数
    if (data.sample_rate) {
      request.sample_rate = data.sample_rate;
    }

    return request;
  }

  /**
   * 调用 SiliconFlow TTS API
   */
  async generateSpeech(data: TTSRequestBody, requestId: string): Promise<SiliconFlowResponse> {
    const startTime = Date.now();
    
    try {
      const request = this.buildRequest(data);
      
      Logger.info('Calling SiliconFlow API', {
        requestId,
        model: request.model,
        voice: request.voice,
        textLength: request.input.length
      });

      // 创建 AbortController 用于超时控制
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.TIMEOUT_MS);

      const response = await fetch(this.API_URL, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'User-Agent': 'SoulVoice-API/1.0.0',
        },
        body: JSON.stringify(request),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        Logger.error('SiliconFlow API error', {
          status: response.status,
          statusText: response.statusText,
          error: errorText
        }, { requestId });

        return {
          success: false,
          error: `SiliconFlow API error: ${response.status} ${response.statusText}`
        };
      }

      // 检查响应内容类型
      const contentType = response.headers.get('content-type');
      if (!contentType?.includes('audio/')) {
        // 可能是 JSON 错误响应
        try {
          const jsonResponse = await response.json();
          Logger.error('SiliconFlow returned JSON instead of audio', jsonResponse, { requestId });
          return {
            success: false,
            error: jsonResponse.error?.message || 'Invalid response format'
          };
        } catch {
          return {
            success: false,
            error: 'Invalid response format from SiliconFlow'
          };
        }
      }

      // 获取音频数据
      const audioData = await response.arrayBuffer();
      const duration = Date.now() - startTime;

      Logger.info('SiliconFlow API call successful', {
        requestId,
        audioSize: audioData.byteLength,
        duration,
        contentType
      });

      return {
        success: true,
        audioData
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      
      if (error.name === 'AbortError') {
        Logger.error('SiliconFlow API timeout', error, { requestId, duration });
        return {
          success: false,
          error: 'Request timeout'
        };
      }

      Logger.error('SiliconFlow API call failed', error, { requestId, duration });
      return {
        success: false,
        error: 'Network error or service unavailable'
      };
    }
  }

  /**
   * 检查 SiliconFlow API 状态
   */
  async checkStatus(): Promise<{ available: boolean; latency?: number }> {
    const startTime = Date.now();
    
    try {
      const response = await fetch(this.API_URL, {
        method: 'HEAD',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
        },
      });

      const latency = Date.now() - startTime;
      return {
        available: response.ok,
        latency
      };
    } catch (error) {
      Logger.error('SiliconFlow status check failed', error);
      return {
        available: false
      };
    }
  }

  /**
   * 获取支持的音色列表
   */
  getAvailableVoices(): Array<{ id: string; name: string; language: string; gender: string }> {
    return [
      {
        id: 'zh-CN-XiaoxiaoNeural',
        name: '温柔知性女声',
        language: 'zh-CN',
        gender: 'female'
      },
      {
        id: 'zh-CN-YunxiNeural',
        name: '沉稳磁性男声',
        language: 'zh-CN',
        gender: 'male'
      },
      {
        id: 'zh-CN-XiaoyiNeural',
        name: '活泼青春女声',
        language: 'zh-CN',
        gender: 'female'
      },
      {
        id: 'zh-CN-YunjianNeural',
        name: '专业播音男声',
        language: 'zh-CN',
        gender: 'male'
      }
    ];
  }
}
