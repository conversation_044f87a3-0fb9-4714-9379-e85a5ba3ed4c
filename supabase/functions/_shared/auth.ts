import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.52.0';
import { corsHeaders } from './cors.ts';

// 创建 Supabase 客户端的工厂函数
export function createSupabaseClient() {
  return createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

export interface ApiKeyValidationResult {
  isValid: boolean;
  userId?: string;
  keyId?: string;
  error?: string;
}

export interface RateLimitResult {
  allowed: boolean;
  remaining?: number;
  resetTime?: number;
  error?: string;
}

/**
 * 验证 API 密钥
 */
export async function validateApiKey(
  supabase: any,
  authHeader: string | null
): Promise<ApiKeyValidationResult> {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return {
      isValid: false,
      error: 'Missing or invalid Authorization header'
    };
  }

  const apiKey = authHeader.replace('Bearer ', '');
  
  if (!apiKey || apiKey.length < 10) {
    return {
      isValid: false,
      error: 'Invalid API key format'
    };
  }

  try {
    // 使用现有的 ApiKeyService 逻辑
    const keyHash = await hashApiKey(apiKey);
    
    const { data: keyData, error } = await supabase
      .from('api_keys')
      .select('id, user_id, name, last_used_at')
      .eq('key_hash', keyHash)
      .eq('is_active', true)
      .single();

    if (error || !keyData) {
      return {
        isValid: false,
        error: 'Invalid API key'
      };
    }

    // 更新最后使用时间
    await supabase
      .from('api_keys')
      .update({ last_used_at: new Date().toISOString() })
      .eq('id', keyData.id);

    return {
      isValid: true,
      userId: keyData.user_id,
      keyId: keyData.id
    };
  } catch (error) {
    console.error('API key validation error:', error);
    return {
      isValid: false,
      error: 'Internal server error'
    };
  }
}

/**
 * 哈希 API 密钥（与前端保持一致）
 */
async function hashApiKey(key: string): Promise<string> {
  const encoder = new TextEncoder();
  const data = encoder.encode(key);
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
}

/**
 * 简单的内存限流实现（生产环境建议使用 Redis）
 */
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

export async function checkRateLimit(
  userId: string,
  limit: number = 60, // 每分钟60次请求
  windowMs: number = 60000 // 1分钟窗口
): Promise<RateLimitResult> {
  const now = Date.now();
  const key = `rate_limit:${userId}`;
  
  const current = rateLimitStore.get(key);
  
  if (!current || now > current.resetTime) {
    // 新窗口或窗口已过期
    rateLimitStore.set(key, {
      count: 1,
      resetTime: now + windowMs
    });
    
    return {
      allowed: true,
      remaining: limit - 1,
      resetTime: now + windowMs
    };
  }
  
  if (current.count >= limit) {
    return {
      allowed: false,
      remaining: 0,
      resetTime: current.resetTime,
      error: 'Rate limit exceeded'
    };
  }
  
  // 增加计数
  current.count++;
  rateLimitStore.set(key, current);
  
  return {
    allowed: true,
    remaining: limit - current.count,
    resetTime: current.resetTime
  };
}

/**
 * 创建错误响应
 */
export function createErrorResponse(
  message: string,
  status: number = 400,
  code?: string
): Response {
  return new Response(
    JSON.stringify({
      error: {
        message,
        code: code || 'INVALID_REQUEST',
        timestamp: new Date().toISOString()
      }
    }),
    {
      status,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    }
  );
}

/**
 * 记录 API 使用情况
 */
export async function logApiUsage(
  supabase: any,
  userId: string,
  apiKeyId: string,
  serviceType: 'tts' | 'clone',
  charactersUsed: number,
  cost: number
): Promise<void> {
  try {
    await supabase
      .from('usage_records')
      .insert({
        user_id: userId,
        api_key_id: apiKeyId,
        service_type: serviceType,
        characters_used: charactersUsed,
        cost: cost
      });
  } catch (error) {
    console.error('Failed to log API usage:', error);
    // 不抛出错误，避免影响主要功能
  }
}
