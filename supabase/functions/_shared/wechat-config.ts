/**
 * 微信支付配置工具
 * 处理证书文件读取和配置初始化
 */

export interface WechatPayConfig {
  appId: string;
  mchId: string;
  privateKey: string;
  publicKey?: string; // 微信支付平台证书
  serialNo: string;
  apiV3Key: string;
}

/**
 * 读取私钥文件内容
 */
async function readPrivateKey(): Promise<string> {
  const keyPath = Deno.env.get('WECHAT_KEY_PATH');
  const privateKeyContent = Deno.env.get('WECHAT_PRIVATE_KEY');

  // 如果直接配置了私钥内容，优先使用
  if (privateKeyContent) {
    return privateKeyContent;
  }

  // 否则尝试从文件路径读取
  if (keyPath) {
    try {
      const keyContent = await Deno.readTextFile(keyPath);
      return keyContent;
    } catch (error) {
      console.error('Failed to read private key file:', error);
      throw new Error(`无法读取私钥文件: ${keyPath}`);
    }
  }

  throw new Error('未配置私钥文件路径或私钥内容');
}

/**
 * 读取证书文件内容
 */
async function readPublicKey(): Promise<string | undefined> {
  const certPath = Deno.env.get('WECHAT_CERT_PATH');
  const publicKeyContent = Deno.env.get('WECHAT_PUBLIC_KEY');

  // 如果直接配置了证书内容，优先使用
  if (publicKeyContent) {
    return publicKeyContent;
  }

  // 否则尝试从文件路径读取
  if (certPath) {
    try {
      const certContent = await Deno.readTextFile(certPath);
      return certContent;
    } catch (error) {
      console.error('Failed to read certificate file:', error);
      // 证书文件不是必需的，只记录警告
      console.warn(`无法读取证书文件: ${certPath}`);
    }
  }

  return undefined;
}

/**
 * 初始化微信支付配置
 */
export async function initWechatPayConfig(): Promise<WechatPayConfig> {
  const appId = Deno.env.get('WECHAT_APP_ID');
  const mchId = Deno.env.get('WECHAT_MCH_ID');
  const serialNo = Deno.env.get('WECHAT_CERT_SERIAL_NUMBER');
  const apiV3Key = Deno.env.get('WECHAT_API_V3_KEY');
  
  // 验证必需的配置
  if (!appId || !mchId || !serialNo || !apiV3Key) {
    const missing = [];
    if (!appId) missing.push('WECHAT_APP_ID');
    if (!mchId) missing.push('WECHAT_MCH_ID');
    if (!serialNo) missing.push('WECHAT_CERT_SERIAL_NUMBER');
    if (!apiV3Key) missing.push('WECHAT_API_V3_KEY');
    
    throw new Error(`缺少必需的微信支付配置: ${missing.join(', ')}`);
  }
  
  // 读取私钥
  const privateKey = await readPrivateKey();

  // 读取证书（可选）
  const publicKey = await readPublicKey();

  return {
    appId,
    mchId,
    privateKey,
    publicKey,
    serialNo,
    apiV3Key,
  };
}

/**
 * 验证配置是否完整
 */
export function validateWechatPayConfig(config: WechatPayConfig): boolean {
  const required = ['appId', 'mchId', 'privateKey', 'serialNo', 'apiV3Key'];
  
  for (const key of required) {
    if (!config[key as keyof WechatPayConfig]) {
      console.error(`微信支付配置缺失: ${key}`);
      return false;
    }
  }
  
  // 验证私钥格式
  if (!config.privateKey.includes('BEGIN PRIVATE KEY')) {
    console.error('私钥格式不正确，应为 PEM 格式');
    return false;
  }
  
  // 验证商户号格式
  if (!/^\d{8,10}$/.test(config.mchId)) {
    console.error('商户号格式不正确');
    return false;
  }
  
  // 验证应用ID格式
  if (!/^wx[a-f0-9]{16}$/.test(config.appId)) {
    console.error('应用ID格式不正确');
    return false;
  }
  
  return true;
}

/**
 * 获取配置的环境变量映射
 */
export function getConfigMapping(): Record<string, string> {
  return {
    'WECHAT_APP_ID': Deno.env.get('WECHAT_APP_ID') || '未配置',
    'WECHAT_MCH_ID': Deno.env.get('WECHAT_MCH_ID') || '未配置',
    'WECHAT_CERT_SERIAL_NUMBER': Deno.env.get('WECHAT_CERT_SERIAL_NUMBER') || '未配置',
    'WECHAT_API_V3_KEY': Deno.env.get('WECHAT_API_V3_KEY') ? '已配置' : '未配置',
    'WECHAT_KEY_PATH': Deno.env.get('WECHAT_KEY_PATH') || '未配置',
    'WECHAT_PRIVATE_KEY': Deno.env.get('WECHAT_PRIVATE_KEY') ? '已配置' : '未配置',
    'WECHAT_CERT_PATH': Deno.env.get('WECHAT_CERT_PATH') || '未配置',
    'WECHAT_PUBLIC_KEY': Deno.env.get('WECHAT_PUBLIC_KEY') ? '已配置' : '未配置',
  };
}
