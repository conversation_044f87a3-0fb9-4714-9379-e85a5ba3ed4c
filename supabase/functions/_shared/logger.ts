export interface LogContext {
  userId?: string;
  apiKeyId?: string;
  requestId?: string;
  duration?: number;
  [key: string]: any;
}

export class Logger {
  private static generateRequestId(): string {
    return crypto.randomUUID();
  }

  static info(message: string, context?: LogContext): void {
    const logEntry = {
      level: 'info',
      message,
      timestamp: new Date().toISOString(),
      requestId: context?.requestId || this.generateRequestId(),
      ...context
    };
    console.log(JSON.stringify(logEntry));
  }

  // 添加 logInfo 别名方法以保持兼容性
  static logInfo(message: string, context?: LogContext): void {
    this.info(message, context);
  }

  static error(message: string, error?: any, context?: LogContext): void {
    const logEntry = {
      level: 'error',
      message,
      error: error?.message || error,
      stack: error?.stack,
      timestamp: new Date().toISOString(),
      requestId: context?.requestId || this.generateRequestId(),
      ...context
    };
    console.error(JSON.stringify(logEntry));
  }

  static warn(message: string, context?: LogContext): void {
    const logEntry = {
      level: 'warn',
      message,
      timestamp: new Date().toISOString(),
      requestId: context?.requestId || this.generateRequestId(),
      ...context
    };
    console.warn(JSON.stringify(logEntry));
  }

  static debug(message: string, context?: LogContext): void {
    // 只在开发环境输出 debug 日志
    if (Deno.env.get('ENVIRONMENT') === 'development') {
      const logEntry = {
        level: 'debug',
        message,
        timestamp: new Date().toISOString(),
        requestId: context?.requestId || this.generateRequestId(),
        ...context
      };
      console.debug(JSON.stringify(logEntry));
    }
  }

  /**
   * 创建带有请求 ID 的日志上下文
   */
  static createContext(userId?: string, apiKeyId?: string): LogContext & { requestId: string } {
    return {
      requestId: this.generateRequestId(),
      userId,
      apiKeyId
    };
  }

  /**
   * 记录 API 请求开始
   */
  static logRequestStart(method: string, path: string, context: LogContext): void {
    this.info(`${method} ${path} - Request started`, {
      ...context,
      method,
      path
    });
  }

  /**
   * 记录 API 请求结束
   */
  static logRequestEnd(
    method: string, 
    path: string, 
    status: number, 
    duration: number, 
    context: LogContext
  ): void {
    this.info(`${method} ${path} - Request completed`, {
      ...context,
      method,
      path,
      status,
      duration
    });
  }

  /**
   * 记录 API 使用情况
   */
  static logApiUsage(
    service: string,
    characters: number,
    cost: number,
    context: LogContext
  ): void {
    this.info(`API usage recorded`, {
      ...context,
      service,
      characters,
      cost
    });
  }

  /**
   * 记录请求开始
   */
  static logRequestStart(method: string, url: string, context?: LogContext): void {
    this.info(`Request started: ${method} ${url}`, context);
  }

  /**
   * 记录请求结束
   */
  static logRequestEnd(
    method: string,
    path: string,
    status: number,
    duration: number,
    context?: LogContext
  ): void {
    this.info(`Request completed: ${method} ${path} ${status} (${duration}ms)`, {
      ...context,
      status,
      duration
    });
  }
}
