import { createErrorResponse } from './auth.ts';

export interface TTSRequestBody {
  text: string;
  voice?: string;
  model?: string;
  speed?: number;
  pitch?: number;
  emotion?: string;
  response_format?: string;
  sample_rate?: number;
}

export interface ValidationResult {
  isValid: boolean;
  data?: TTSRequestBody;
  error?: Response;
}

/**
 * 验证 TTS 请求体
 */
export function validateTTSRequest(body: any): ValidationResult {
  // 检查必需字段
  if (!body.text || typeof body.text !== 'string') {
    return {
      isValid: false,
      error: createErrorResponse('Missing or invalid "text" parameter', 400, 'MISSING_TEXT')
    };
  }

  // 检查文本长度
  if (body.text.trim().length === 0) {
    return {
      isValid: false,
      error: createErrorResponse('Text cannot be empty', 400, 'EMPTY_TEXT')
    };
  }

  if (body.text.length > 5000) {
    return {
      isValid: false,
      error: createErrorResponse('Text too long (max 5000 characters)', 400, 'TEXT_TOO_LONG')
    };
  }

  // 验证可选参数
  const validatedData: TTSRequestBody = {
    text: body.text.trim()
  };

  // 验证音色
  if (body.voice !== undefined) {
    if (typeof body.voice !== 'string' || body.voice.length === 0) {
      return {
        isValid: false,
        error: createErrorResponse('Invalid voice parameter', 400, 'INVALID_VOICE')
      };
    }
    validatedData.voice = body.voice;
  }

  // 验证模型
  if (body.model !== undefined) {
    if (typeof body.model !== 'string' || body.model.length === 0) {
      return {
        isValid: false,
        error: createErrorResponse('Invalid model parameter', 400, 'INVALID_MODEL')
      };
    }

    // 检查模型是否在支持的列表中
    const supportedModels = [
      'FunAudioLLM/CosyVoice2-0.5B',
      'fnlp/MOSS-TTSD-v0.5'
    ];

    if (!supportedModels.includes(body.model)) {
      return {
        isValid: false,
        error: createErrorResponse(
          `Unsupported model. Supported models: ${supportedModels.join(', ')}`,
          400,
          'UNSUPPORTED_MODEL'
        )
      };
    }

    validatedData.model = body.model;
  }

  // 验证语速
  if (body.speed !== undefined) {
    const speed = Number(body.speed);
    if (isNaN(speed) || speed < 0.5 || speed > 2.0) {
      return {
        isValid: false,
        error: createErrorResponse('Speed must be between 0.5 and 2.0', 400, 'INVALID_SPEED')
      };
    }
    validatedData.speed = speed;
  }

  // 验证音调
  if (body.pitch !== undefined) {
    const pitch = Number(body.pitch);
    if (isNaN(pitch) || pitch < -20 || pitch > 20) {
      return {
        isValid: false,
        error: createErrorResponse('Pitch must be between -20 and 20', 400, 'INVALID_PITCH')
      };
    }
    validatedData.pitch = pitch;
  }

  // 验证情感
  if (body.emotion !== undefined) {
    const validEmotions = ['neutral', 'happy', 'sad', 'angry', 'excited'];
    if (typeof body.emotion !== 'string' || !validEmotions.includes(body.emotion)) {
      return {
        isValid: false,
        error: createErrorResponse(
          `Invalid emotion. Must be one of: ${validEmotions.join(', ')}`, 
          400, 
          'INVALID_EMOTION'
        )
      };
    }
    validatedData.emotion = body.emotion;
  }

  // 验证响应格式
  if (body.response_format !== undefined) {
    const validFormats = ['mp3', 'wav', 'flac'];
    if (typeof body.response_format !== 'string' || !validFormats.includes(body.response_format)) {
      return {
        isValid: false,
        error: createErrorResponse(
          `Invalid response_format. Must be one of: ${validFormats.join(', ')}`, 
          400, 
          'INVALID_FORMAT'
        )
      };
    }
    validatedData.response_format = body.response_format;
  }

  // 验证采样率
  if (body.sample_rate !== undefined) {
    const sampleRate = Number(body.sample_rate);
    const validRates = [16000, 22050, 44100, 48000];
    if (isNaN(sampleRate) || !validRates.includes(sampleRate)) {
      return {
        isValid: false,
        error: createErrorResponse(
          `Invalid sample_rate. Must be one of: ${validRates.join(', ')}`, 
          400, 
          'INVALID_SAMPLE_RATE'
        )
      };
    }
    validatedData.sample_rate = sampleRate;
  }

  return {
    isValid: true,
    data: validatedData
  };
}

/**
 * 安全地解析 JSON 请求体
 */
export async function parseJsonBody(request: Request): Promise<{ success: boolean; data?: any; error?: Response }> {
  try {
    const body = await request.json();
    return { success: true, data: body };
  } catch (error) {
    return {
      success: false,
      error: createErrorResponse('Invalid JSON in request body', 400, 'INVALID_JSON')
    };
  }
}

/**
 * 验证内容类型
 */
export function validateContentType(request: Request): Response | null {
  const contentType = request.headers.get('content-type');
  if (!contentType || !contentType.includes('application/json')) {
    return createErrorResponse(
      'Content-Type must be application/json', 
      400, 
      'INVALID_CONTENT_TYPE'
    );
  }
  return null;
}
