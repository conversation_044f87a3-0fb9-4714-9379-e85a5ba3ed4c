import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { corsHeaders, handleCors } from '../_shared/cors.ts';
import { createSupabaseClient } from '../_shared/auth.ts';
import { Logger } from '../_shared/logger.ts';
import { SiliconFlowClient } from '../_shared/siliconflow.ts';

interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  version: string;
  services: {
    database: {
      status: 'up' | 'down';
      latency?: number;
    };
    siliconflow: {
      status: 'up' | 'down';
      latency?: number;
    };
  };
  uptime: number;
}

const startTime = Date.now();

serve(async (req) => {
  // 处理 CORS
  const corsResponse = handleCors(req);
  if (corsResponse) return corsResponse;

  // 只允许 GET 请求
  if (req.method !== 'GET') {
    return new Response('Method not allowed', { status: 405 });
  }

  const logContext = Logger.createContext();
  Logger.logRequestStart('GET', '/health', logContext);

  try {
    const checkStartTime = Date.now();
    
    // 检查数据库连接（简化版本）
    let databaseStatus: { status: 'up' | 'down'; latency?: number } = { status: 'up' };
    try {
      const supabase = createSupabaseClient();
      const dbCheckStart = Date.now();

      // 简单的连接测试，不依赖特定表
      const { error } = await supabase.auth.getSession();

      databaseStatus = {
        status: 'up',
        latency: Date.now() - dbCheckStart
      };
    } catch (error) {
      Logger.error('Database health check failed', error, logContext);
      databaseStatus = { status: 'down' };
    }

    // 检查 SiliconFlow API（简化版本）
    let siliconflowStatus: { status: 'up' | 'down'; latency?: number } = { status: 'up' };
    try {
      // 检查 API 密钥是否存在
      const apiKey = Deno.env.get('SILICONFLOW_API_KEY');
      if (!apiKey) {
        siliconflowStatus = { status: 'down' };
      } else {
        // 简单的可用性检查
        siliconflowStatus = {
          status: 'up',
          latency: 0
        };
      }
    } catch (error) {
      Logger.error('SiliconFlow health check failed', error, logContext);
      siliconflowStatus = { status: 'down' };
    }

    // 确定整体状态
    let overallStatus: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    
    if (databaseStatus.status === 'down' || siliconflowStatus.status === 'down') {
      overallStatus = 'unhealthy';
    } else if (
      (databaseStatus.latency && databaseStatus.latency > 1000) ||
      (siliconflowStatus.latency && siliconflowStatus.latency > 2000)
    ) {
      overallStatus = 'degraded';
    }

    const healthStatus: HealthStatus = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      services: {
        database: databaseStatus,
        siliconflow: siliconflowStatus
      },
      uptime: Date.now() - startTime
    };

    const statusCode = overallStatus === 'healthy' ? 200 : 
                      overallStatus === 'degraded' ? 200 : 503;

    const duration = Date.now() - checkStartTime;
    Logger.logRequestEnd('GET', '/health', statusCode, duration, {
      ...logContext,
      overallStatus,
      databaseStatus: databaseStatus.status,
      siliconflowStatus: siliconflowStatus.status
    });

    return new Response(
      JSON.stringify(healthStatus, null, 2),
      {
        status: statusCode,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'X-Request-ID': logContext.requestId,
          ...corsHeaders
        }
      }
    );

  } catch (error) {
    Logger.error('Health check error', error, logContext);
    
    const errorStatus: HealthStatus = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      services: {
        database: { status: 'down' },
        siliconflow: { status: 'down' }
      },
      uptime: Date.now() - startTime
    };

    return new Response(
      JSON.stringify(errorStatus, null, 2),
      {
        status: 503,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'X-Request-ID': logContext.requestId,
          ...corsHeaders
        }
      }
    );
  }
});
