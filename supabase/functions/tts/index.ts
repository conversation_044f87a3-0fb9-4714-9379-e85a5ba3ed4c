import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { corsHeaders, handleCors } from '../_shared/cors.ts';
import {
  validateApiKey,
  checkRateLimit,
  createErrorResponse,
  logApiUsage,
  createSupabaseClient
} from '../_shared/auth.ts';
import { validateTTSRequest, parseJsonBody, validateContentType } from '../_shared/validation.ts';
import { Logger } from '../_shared/logger.ts';
import { SiliconFlowClient } from '../_shared/siliconflow.ts';

// 费用计算常量
const COST_PER_CHARACTER = 0.0002; // $0.0002 per character

serve(async (req) => {
  const startTime = Date.now();

  // 处理 CORS
  const corsResponse = handleCors(req);
  if (corsResponse) return corsResponse;

  // 只允许 POST 请求
  if (req.method !== 'POST') {
    return createErrorResponse('Method not allowed', 405, 'METHOD_NOT_ALLOWED');
  }

  // 创建日志上下文
  const logContext = Logger.createContext();
  Logger.logRequestStart('POST', '/tts', logContext);

  try {
    // 验证内容类型
    const contentTypeError = validateContentType(req);
    if (contentTypeError) {
      Logger.warn('Invalid content type', logContext);
      return contentTypeError;
    }

    // 创建 Supabase 客户端
    const supabase = createSupabaseClient();

    // 验证 API 密钥
    const authHeader = req.headers.get('authorization');
    const authResult = await validateApiKey(supabase, authHeader);

    if (!authResult.isValid) {
      Logger.warn('API key validation failed', {
        ...logContext,
        error: authResult.error
      });
      return createErrorResponse(
        authResult.error || 'Unauthorized',
        401,
        'UNAUTHORIZED'
      );
    }

    const { userId, keyId } = authResult;
    // 更新日志上下文
    logContext.userId = userId;
    logContext.apiKeyId = keyId;

    // 检查限流
    const rateLimitResult = await checkRateLimit(userId!);
    if (!rateLimitResult.allowed) {
      Logger.warn('Rate limit exceeded', {
        ...logContext,
        remaining: rateLimitResult.remaining,
        resetTime: rateLimitResult.resetTime
      });

      return new Response(
        JSON.stringify({
          error: {
            message: 'Rate limit exceeded',
            code: 'RATE_LIMIT_EXCEEDED',
            retry_after: Math.ceil((rateLimitResult.resetTime! - Date.now()) / 1000)
          }
        }),
        {
          status: 429,
          headers: {
            'Content-Type': 'application/json',
            'X-RateLimit-Remaining': rateLimitResult.remaining?.toString() || '0',
            'X-RateLimit-Reset': rateLimitResult.resetTime?.toString() || '',
            ...corsHeaders
          }
        }
      );
    }

    // 解析和验证请求体
    const parseResult = await parseJsonBody(req);
    if (!parseResult.success) {
      Logger.warn('Failed to parse JSON body', logContext);
      return parseResult.error!;
    }

    const validationResult = validateTTSRequest(parseResult.data);
    if (!validationResult.isValid) {
      Logger.warn('Request validation failed', {
        ...logContext,
        requestBody: parseResult.data
      });
      return validationResult.error!;
    }

    const requestData = validationResult.data!;

    // 获取 SiliconFlow API 密钥
    const siliconFlowApiKey = Deno.env.get('SILICONFLOW_API_KEY');
    if (!siliconFlowApiKey) {
      Logger.error('SiliconFlow API key not configured', {}, logContext);
      return createErrorResponse(
        'Service configuration error',
        500,
        'SERVICE_CONFIG_ERROR'
      );
    }

    // 构建 SiliconFlow 请求
    const siliconFlowRequest = {
      model: requestData.model || 'FunAudioLLM/CosyVoice2-0.5B', // 默认使用 CosyVoice2
      input: requestData.text,
      voice: requestData.voice || 'speech:mcg3fdnx:clzkyf4vy00e5qr6hywum4u84:bzznlkuhcjzpbosexitr',
      speed: requestData.speed || 1.0,
      response_format: requestData.response_format || 'mp3'
    };

    Logger.info('Calling SiliconFlow API', {
      ...logContext,
      model: siliconFlowRequest.model,
      voice: siliconFlowRequest.voice,
      textLength: siliconFlowRequest.input.length
    });

    // 调用 SiliconFlow API
    const siliconFlowResponse = await fetch('https://api.siliconflow.cn/v1/audio/speech', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${siliconFlowApiKey}`,
        'Content-Type': 'application/json',
        'User-Agent': 'SoulVoice/1.0.0',
      },
      body: JSON.stringify(siliconFlowRequest),
    });

    Logger.info('SiliconFlow response received', {
      ...logContext,
      status: siliconFlowResponse.status,
      contentType: siliconFlowResponse.headers.get('content-type')
    });

    if (!siliconFlowResponse.ok) {
      const errorText = await siliconFlowResponse.text();
      Logger.error('SiliconFlow API error', {
        status: siliconFlowResponse.status,
        statusText: siliconFlowResponse.statusText,
        error: errorText
      }, logContext);

      return createErrorResponse(
        'Speech generation failed',
        500,
        'SPEECH_GENERATION_FAILED'
      );
    }

    // 获取音频数据
    const audioData = await siliconFlowResponse.arrayBuffer();
    Logger.info('Audio data received', {
      ...logContext,
      audioSize: audioData.byteLength
    });

    // 计算费用
    const characters = requestData.text.length;
    const cost = characters * COST_PER_CHARACTER;

    // 记录使用情况
    try {
      await logApiUsage(supabase, userId!, keyId!, 'tts', characters, cost);
      Logger.logApiUsage('tts', characters, cost, logContext);
    } catch (error) {
      Logger.error('Failed to log API usage', error, logContext);
      // 不影响主要功能，继续执行
    }

    const duration = Date.now() - startTime;
    Logger.logRequestEnd('POST', '/tts', 200, duration, {
      ...logContext,
      characters,
      cost,
      audioSize: audioData.byteLength
    });

    // 返回音频数据
    return new Response(audioData, {
      status: 200,
      headers: {
        'Content-Type': 'audio/mpeg',
        'Content-Length': audioData.byteLength.toString(),
        'X-Usage-Characters': characters.toString(),
        'X-Usage-Cost': cost.toString(),
        'X-RateLimit-Remaining': rateLimitResult.remaining?.toString() || '0',
        'X-Request-ID': logContext.requestId,
        ...corsHeaders
      }
    });

  } catch (error) {
    const duration = Date.now() - startTime;
    Logger.error('TTS API error', error, { ...logContext, duration });
    Logger.logRequestEnd('POST', '/tts', 500, duration, logContext);

    return createErrorResponse(
      'Internal server error',
      500,
      'INTERNAL_SERVER_ERROR'
    );
  }
});
