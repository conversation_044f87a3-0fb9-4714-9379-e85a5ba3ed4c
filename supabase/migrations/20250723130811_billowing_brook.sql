/*
  # SoulVoice 数据库架构

  1. 新建表
    - `profiles` - 用户资料表
      - `id` (uuid, 主键, 关联 auth.users)
      - `email` (text)
      - `name` (text)
      - `company` (text, 可选)
      - `avatar_url` (text, 可选)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)
    
    - `api_keys` - API 密钥表
      - `id` (uuid, 主键)
      - `user_id` (uuid, 外键)
      - `name` (text)
      - `key_hash` (text, 存储哈希值)
      - `key_prefix` (text, 显示用的前缀)
      - `last_used_at` (timestamp)
      - `created_at` (timestamp)
      - `is_active` (boolean)
    
    - `voice_models` - 语音模型表
      - `id` (uuid, 主键)
      - `user_id` (uuid, 外键)
      - `name` (text)
      - `model_id` (text, 外部服务返回的ID)
      - `status` (text, processing/ready/failed)
      - `created_at` (timestamp)
    
    - `usage_records` - 用量记录表
      - `id` (uuid, 主键)
      - `user_id` (uuid, 外键)
      - `api_key_id` (uuid, 外键)
      - `service_type` (text, tts/clone)
      - `characters_used` (integer)
      - `cost` (decimal)
      - `created_at` (timestamp)
    
    - `user_settings` - 用户设置表
      - `id` (uuid, 主键)
      - `user_id` (uuid, 外键)
      - `notifications` (jsonb)
      - `preferences` (jsonb)
      - `updated_at` (timestamp)

  2. 安全策略
    - 启用所有表的 RLS
    - 用户只能访问自己的数据
    - API 密钥需要特殊处理

  3. 索引和约束
    - 为常用查询字段添加索引
    - 外键约束确保数据完整性
*/

-- 用户资料表
CREATE TABLE IF NOT EXISTS profiles (
  id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email text NOT NULL,
  name text NOT NULL,
  company text,
  avatar_url text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- API 密钥表
CREATE TABLE IF NOT EXISTS api_keys (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  name text NOT NULL,
  key_hash text NOT NULL,
  key_prefix text NOT NULL,
  last_used_at timestamptz,
  created_at timestamptz DEFAULT now(),
  is_active boolean DEFAULT true
);

-- 语音模型表
CREATE TABLE IF NOT EXISTS voice_models (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  name text NOT NULL,
  model_id text NOT NULL,
  status text DEFAULT 'processing' CHECK (status IN ('processing', 'ready', 'failed')),
  created_at timestamptz DEFAULT now()
);

-- 用量记录表
CREATE TABLE IF NOT EXISTS usage_records (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  api_key_id uuid REFERENCES api_keys(id) ON DELETE SET NULL,
  service_type text NOT NULL CHECK (service_type IN ('tts', 'clone')),
  characters_used integer DEFAULT 0,
  cost decimal(10,4) DEFAULT 0,
  created_at timestamptz DEFAULT now()
);

-- 用户设置表
CREATE TABLE IF NOT EXISTS user_settings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  notifications jsonb DEFAULT '{"emailNotifications": true, "usageAlerts": true, "securityAlerts": true, "productUpdates": false}'::jsonb,
  preferences jsonb DEFAULT '{"theme": "dark", "language": "zh-CN", "defaultVoice": "zh-CN-XiaoxiaoNeural"}'::jsonb,
  updated_at timestamptz DEFAULT now()
);

-- 启用 RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE voice_models ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;

-- 用户资料策略
CREATE POLICY "Users can read own profile"
  ON profiles
  FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Users can update own profile"
  ON profiles
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile"
  ON profiles
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = id);

-- API 密钥策略
CREATE POLICY "Users can manage own API keys"
  ON api_keys
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid());

-- 语音模型策略
CREATE POLICY "Users can manage own voice models"
  ON voice_models
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid());

-- 用量记录策略
CREATE POLICY "Users can read own usage records"
  ON usage_records
  FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "System can insert usage records"
  ON usage_records
  FOR INSERT
  TO authenticated
  WITH CHECK (user_id = auth.uid());

-- 用户设置策略
CREATE POLICY "Users can manage own settings"
  ON user_settings
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid());

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_api_keys_user_id ON api_keys(user_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_key_hash ON api_keys(key_hash);
CREATE INDEX IF NOT EXISTS idx_voice_models_user_id ON voice_models(user_id);
CREATE INDEX IF NOT EXISTS idx_usage_records_user_id ON usage_records(user_id);
CREATE INDEX IF NOT EXISTS idx_usage_records_created_at ON usage_records(created_at);
CREATE INDEX IF NOT EXISTS idx_user_settings_user_id ON user_settings(user_id);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表添加更新时间触发器
CREATE TRIGGER update_profiles_updated_at
  BEFORE UPDATE ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_settings_updated_at
  BEFORE UPDATE ON user_settings
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();