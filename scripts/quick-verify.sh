#!/bin/bash

# 快速验证 SoulVoice API 部署

BASE_URL="https://xfsvmyceleiafhqewdkj.supabase.co/functions/v1"

echo "🚀 SoulVoice API 快速验证"
echo "Base URL: $BASE_URL"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "\n${BLUE}1. 健康检查${NC}"
health_response=$(curl -s "$BASE_URL/health")
echo "$health_response"

if echo "$health_response" | grep -q '"status":"healthy"'; then
    echo -e "${GREEN}✅ 健康检查通过${NC}"
else
    echo -e "${RED}❌ 健康检查失败${NC}"
fi

echo -e "\n${BLUE}2. TTS API 认证测试${NC}"
tts_response=$(curl -s -X POST "$BASE_URL/tts" -H "Content-Type: application/json" -d '{"text":"test"}')
echo "$tts_response"

if echo "$tts_response" | grep -q '"code":"UNAUTHORIZED"'; then
    echo -e "${GREEN}✅ TTS API 认证正常${NC}"
else
    echo -e "${RED}❌ TTS API 认证异常${NC}"
fi

echo -e "\n${BLUE}3. Voices API 认证测试${NC}"
voices_response=$(curl -s "$BASE_URL/voices")
echo "$voices_response"

if echo "$voices_response" | grep -q '"code":"UNAUTHORIZED"'; then
    echo -e "${GREEN}✅ Voices API 认证正常${NC}"
else
    echo -e "${RED}❌ Voices API 认证异常${NC}"
fi

echo -e "\n${BLUE}4. Usage API 认证测试${NC}"
usage_response=$(curl -s "$BASE_URL/usage")
echo "$usage_response"

if echo "$usage_response" | grep -q '"code":"UNAUTHORIZED"'; then
    echo -e "${GREEN}✅ Usage API 认证正常${NC}"
else
    echo -e "${RED}❌ Usage API 认证异常${NC}"
fi

echo -e "\n${BLUE}5. Functions 部署状态${NC}"
supabase functions list

echo -e "\n${GREEN}🎉 验证完成！${NC}"
echo -e "\n${BLUE}📋 API 端点：${NC}"
echo "Health: $BASE_URL/health"
echo "TTS: $BASE_URL/tts"
echo "Voices: $BASE_URL/voices"
echo "Usage: $BASE_URL/usage"

echo -e "\n${YELLOW}💡 下一步：${NC}"
echo "1. 在 SoulVoice 前端创建 API 密钥"
echo "2. 使用 API 密钥测试完整功能"
echo "3. 更新前端配置使用新的 API 端点"
