#!/bin/bash

# SoulVoice API Functions 开发环境启动脚本

set -e

echo "🚀 启动 SoulVoice API Functions 开发环境..."

# 检查 Supabase CLI 是否安装
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI 未安装，请先安装："
    echo "npm install -g supabase"
    exit 1
fi

# 检查是否在项目根目录
if [ ! -f "supabase/config.toml" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

echo "🔧 设置开发环境变量..."
export ENVIRONMENT=development

# 检查 .env.local 文件
if [ -f ".env.local" ]; then
    echo "📄 加载 .env.local 文件..."
    source .env.local
else
    echo "⚠️  .env.local 文件不存在，使用默认配置"
fi

echo "🏗️  启动 Supabase 本地开发环境..."

# 启动 Supabase 本地服务
supabase start

echo "✅ Supabase 本地服务已启动！"

# 获取本地服务信息
echo ""
echo "🔗 本地开发端点："
echo "API URL: http://localhost:54321"
echo "GraphQL URL: http://localhost:54321/graphql/v1"
echo "Studio URL: http://localhost:54323"
echo "Inbucket URL: http://localhost:54324"
echo "JWT secret: super-secret-jwt-token-with-at-least-32-characters-long"
echo "anon key: $(supabase status | grep 'anon key' | awk '{print $3}')"
echo "service_role key: $(supabase status | grep 'service_role key' | awk '{print $3}')"

echo ""
echo "🔗 Edge Functions 端点："
echo "TTS API: http://localhost:54321/functions/v1/tts"
echo "Voices API: http://localhost:54321/functions/v1/voices"
echo "Usage API: http://localhost:54321/functions/v1/usage"
echo "Health Check: http://localhost:54321/functions/v1/health"

echo ""
echo "📚 测试示例："
echo "# 健康检查"
echo "curl http://localhost:54321/functions/v1/health"
echo ""
echo "# 获取音色列表（需要 API 密钥）"
echo "curl -H 'Authorization: Bearer YOUR_API_KEY' http://localhost:54321/functions/v1/voices"
echo ""
echo "# 语音合成（需要 API 密钥）"
echo "curl -X POST http://localhost:54321/functions/v1/tts \\"
echo "  -H 'Authorization: Bearer YOUR_API_KEY' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"text\": \"你好，SoulVoice！\", \"voice\": \"zh-CN-XiaoxiaoNeural\"}' \\"
echo "  --output test.mp3"

echo ""
echo "🎉 开发环境已就绪！按 Ctrl+C 停止服务。"

# 保持脚本运行，监听中断信号
trap 'echo ""; echo "🛑 停止开发环境..."; supabase stop; exit 0' INT

# 等待用户中断
while true; do
    sleep 1
done
