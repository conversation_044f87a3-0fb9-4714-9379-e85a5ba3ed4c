#!/bin/bash

# SoulVoice TTS API 完整功能测试

set -e

BASE_URL="https://xfsvmyceleiafhqewdkj.supabase.co/functions/v1"
API_KEY="sk-2172cb5aef9deee923a8b0e7234db065a7c5b3a15d47c3224e1bf758b352f3f8"

echo "🎤 SoulVoice TTS API 完整功能测试"
echo "Base URL: $BASE_URL"
echo "API Key: ${API_KEY:0:20}..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试计数器
total_tests=0
passed_tests=0

# 测试函数
test_tts() {
    local test_name=$1
    local text=$2
    local voice=$3
    local additional_params=$4
    local output_file=$5
    
    total_tests=$((total_tests + 1))
    echo -e "\n${BLUE}🔍 测试 $total_tests: $test_name${NC}"
    echo "文本: $text"
    echo "音色: $voice"
    echo "输出文件: $output_file"
    
    local request_body="{\"text\": \"$text\""
    if [ -n "$voice" ]; then
        request_body="$request_body, \"voice\": \"$voice\""
    fi
    if [ -n "$additional_params" ]; then
        request_body="$request_body, $additional_params"
    fi
    request_body="$request_body}"
    
    echo "请求体: $request_body"
    
    local start_time=$(date +%s.%N)
    local response=$(curl -s -w "\n%{http_code}\n%{time_total}\n%{size_download}" \
        -X POST "$BASE_URL/tts" \
        -H "Authorization: Bearer $API_KEY" \
        -H "Content-Type: application/json" \
        -d "$request_body" \
        --output "$output_file")
    
    local status_code=$(echo "$response" | sed -n '2p')
    local time_total=$(echo "$response" | sed -n '3p')
    local size_download=$(echo "$response" | sed -n '4p')
    
    if [ "$status_code" = "200" ]; then
        # 检查文件是否是有效的音频文件
        if file "$output_file" | grep -q "Audio\|MPEG\|MP3"; then
            echo -e "${GREEN}✅ 成功生成语音${NC}"
            echo "   状态码: $status_code"
            echo "   响应时间: ${time_total}s"
            echo "   文件大小: $size_download bytes"
            echo "   文件类型: $(file "$output_file" | cut -d: -f2)"
            passed_tests=$((passed_tests + 1))
            return 0
        else
            echo -e "${RED}❌ 生成的文件不是有效的音频文件${NC}"
            echo "   文件内容: $(head -c 200 "$output_file")"
            return 1
        fi
    else
        echo -e "${RED}❌ 请求失败 (HTTP $status_code)${NC}"
        echo "   响应内容: $(cat "$output_file")"
        return 1
    fi
}

# 清理之前的测试文件
rm -f test_*.mp3

echo -e "\n${BLUE}开始 TTS 功能测试...${NC}"

# 1. 获取可用音色列表
echo -e "\n${BLUE}📋 获取音色列表${NC}"
voices_response=$(curl -s -H "Authorization: Bearer $API_KEY" "$BASE_URL/voices")
echo "音色列表: $voices_response"

# 提取音色 ID
voice1=$(echo "$voices_response" | grep -o '"id":"[^"]*"' | head -n1 | cut -d'"' -f4)
voice2=$(echo "$voices_response" | grep -o '"id":"[^"]*"' | head -n2 | tail -n1 | cut -d'"' -f4)
voice3=$(echo "$voices_response" | grep -o '"id":"[^"]*"' | head -n3 | tail -n1 | cut -d'"' -f4)

echo "使用的音色:"
echo "  音色1: $voice1"
echo "  音色2: $voice2"
echo "  音色3: $voice3"

# 2. 基础语音合成测试
test_tts "基础语音合成" \
    "你好，欢迎使用 SoulVoice 语音合成服务！" \
    "$voice1" \
    "" \
    "test_basic.mp3"

# 3. 不同音色测试
test_tts "不同音色测试" \
    "这是使用不同音色的测试。" \
    "$voice2" \
    "" \
    "test_voice2.mp3"

# 4. 语速调节测试
test_tts "语速调节测试" \
    "这是一个语速调节的测试，速度设置为1.5倍。" \
    "$voice1" \
    "\"speed\": 1.5" \
    "test_speed.mp3"

# 5. 完整参数测试
test_tts "完整参数测试" \
    "这是一个包含所有参数的完整测试。" \
    "$voice3" \
    "\"speed\": 1.2, \"response_format\": \"mp3\", \"sample_rate\": 22050" \
    "test_full_params.mp3"

# 6. 长文本测试
long_text="这是一个较长的文本测试。人工智能技术正在快速发展，语音合成技术也越来越成熟。SoulVoice 提供了高质量的语音合成服务，支持多种音色和参数调节。通过 API 接口，开发者可以轻松地集成语音合成功能到自己的应用中。"
test_tts "长文本测试" \
    "$long_text" \
    "$voice1" \
    "" \
    "test_long_text.mp3"

# 7. 中英文混合测试
test_tts "中英文混合测试" \
    "Hello，这是一个中英文混合的测试。Welcome to SoulVoice！" \
    "$voice2" \
    "" \
    "test_mixed_lang.mp3"

# 8. 特殊字符测试
test_tts "特殊字符测试" \
    "这是特殊字符测试：123，456.78！@#￥%……&*（）" \
    "$voice1" \
    "" \
    "test_special_chars.mp3"

# 9. 错误处理测试
echo -e "\n${BLUE}🔍 错误处理测试${NC}"

# 空文本测试
total_tests=$((total_tests + 1))
echo -e "\n${BLUE}测试 $total_tests: 空文本错误处理${NC}"
empty_response=$(curl -s -w "%{http_code}" \
    -X POST "$BASE_URL/tts" \
    -H "Authorization: Bearer $API_KEY" \
    -H "Content-Type: application/json" \
    -d '{"text": ""}' \
    --output test_empty.mp3)

status_code=$(echo "$empty_response" | tail -c 4)
if [ "$status_code" = "400" ]; then
    echo -e "${GREEN}✅ 正确处理空文本错误${NC}"
    passed_tests=$((passed_tests + 1))
else
    echo -e "${RED}❌ 空文本错误处理失败 (HTTP $status_code)${NC}"
fi

# 文本过长测试
total_tests=$((total_tests + 1))
echo -e "\n${BLUE}测试 $total_tests: 文本过长错误处理${NC}"
long_text_5001=$(printf 'a%.0s' {1..5001})
long_response=$(curl -s -w "%{http_code}" \
    -X POST "$BASE_URL/tts" \
    -H "Authorization: Bearer $API_KEY" \
    -H "Content-Type: application/json" \
    -d "{\"text\": \"$long_text_5001\"}" \
    --output test_too_long.mp3)

status_code=$(echo "$long_response" | tail -c 4)
if [ "$status_code" = "400" ]; then
    echo -e "${GREEN}✅ 正确处理文本过长错误${NC}"
    passed_tests=$((passed_tests + 1))
else
    echo -e "${RED}❌ 文本过长错误处理失败 (HTTP $status_code)${NC}"
fi

# 10. 性能测试
echo -e "\n${BLUE}⚡ 性能测试${NC}"
echo "测试并发请求处理能力..."

for i in {1..3}; do
    (
        test_tts "并发测试 #$i" \
            "这是并发测试消息 $i" \
            "$voice1" \
            "" \
            "test_concurrent_$i.mp3" &
    )
done
wait

# 测试结果总结
echo -e "\n${BLUE}📊 测试结果总结${NC}"
echo "总测试数: $total_tests"
echo "通过测试: $passed_tests"
echo "失败测试: $((total_tests - passed_tests))"

# 列出生成的音频文件
echo -e "\n${BLUE}📁 生成的音频文件：${NC}"
for file in test_*.mp3; do
    if [ -f "$file" ]; then
        size=$(ls -lh "$file" | awk '{print $5}')
        type=$(file "$file" | cut -d: -f2)
        echo "  $file ($size) -$type"
    fi
done

if [ $passed_tests -eq $total_tests ]; then
    echo -e "\n${GREEN}🎉 所有测试通过！TTS API 功能完全正常！${NC}"
    
    echo -e "\n${BLUE}🎵 播放测试（如果系统支持）：${NC}"
    echo "你可以使用以下命令播放生成的音频文件："
    echo "# macOS:"
    echo "afplay test_basic.mp3"
    echo "# Linux:"
    echo "mpv test_basic.mp3"
    echo "# 或者直接在文件管理器中双击播放"
    
    exit 0
else
    echo -e "\n${RED}❌ 部分测试失败，请检查上述错误信息${NC}"
    exit 1
fi
