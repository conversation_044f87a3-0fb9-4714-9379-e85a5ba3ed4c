#!/bin/bash

# 简化的 API 测试脚本

BASE_URL="https://xfsvmyceleiafhqewdkj.supabase.co/functions/v1"

echo "🧪 SoulVoice API 简单测试"
echo "Base URL: $BASE_URL"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "\n${BLUE}1. 测试健康检查端点${NC}"
response=$(curl -s "$BASE_URL/health")
echo "Response: $response"

if echo "$response" | grep -q '"status"'; then
    echo -e "${GREEN}✅ Health endpoint 正常工作${NC}"
    
    # 检查服务状态
    if echo "$response" | grep -q '"status":"healthy"'; then
        echo -e "${GREEN}✅ 所有服务健康${NC}"
    elif echo "$response" | grep -q '"status":"degraded"'; then
        echo -e "${YELLOW}⚠️  服务降级${NC}"
    else
        echo -e "${YELLOW}⚠️  服务不健康，但 API 正常工作${NC}"
    fi
else
    echo -e "${RED}❌ Health endpoint 有问题${NC}"
fi

echo -e "\n${BLUE}2. 测试 Voices 端点（无 API 密钥）${NC}"
response=$(curl -s "$BASE_URL/voices")
echo "Response: $response"

if echo "$response" | grep -q '"code":"UNAUTHORIZED"'; then
    echo -e "${GREEN}✅ Voices endpoint 正常工作（正确返回认证错误）${NC}"
else
    echo -e "${RED}❌ Voices endpoint 有问题${NC}"
fi

echo -e "\n${BLUE}3. 测试 TTS 端点（无 API 密钥）${NC}"
response=$(curl -s -X POST "$BASE_URL/tts" -H "Content-Type: application/json" -d '{"text":"test"}')
echo "Response: $response"

if echo "$response" | grep -q '"code":"UNAUTHORIZED"'; then
    echo -e "${GREEN}✅ TTS endpoint 正常工作（正确返回认证错误）${NC}"
else
    echo -e "${RED}❌ TTS endpoint 有问题${NC}"
fi

echo -e "\n${BLUE}4. 测试 Usage 端点（无 API 密钥）${NC}"
response=$(curl -s "$BASE_URL/usage")
echo "Response: $response"

if echo "$response" | grep -q '"code":"UNAUTHORIZED"'; then
    echo -e "${GREEN}✅ Usage endpoint 正常工作（正确返回认证错误）${NC}"
else
    echo -e "${RED}❌ Usage endpoint 有问题${NC}"
fi

echo -e "\n${GREEN}🎉 基础 API 测试完成！${NC}"
echo -e "\n${BLUE}📝 总结：${NC}"
echo "- 所有 API 端点都已部署并可访问"
echo "- 认证机制正常工作"
echo "- 需要有效的 API 密钥来测试完整功能"

echo -e "\n${YELLOW}💡 下一步：${NC}"
echo "1. 在 SoulVoice 前端创建一个 API 密钥"
echo "2. 使用该密钥测试完整的 API 功能"
echo "3. 检查数据库连接权限"
