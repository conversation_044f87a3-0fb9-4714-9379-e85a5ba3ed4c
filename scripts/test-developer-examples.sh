#!/bin/bash

# 测试开发者工具中的示例代码

set -e

BASE_URL="https://xfsvmyceleiafhqewdkj.supabase.co/functions/v1"
API_KEY="sk-2172cb5aef9deee923a8b0e7234db065a7c5b3a15d47c3224e1bf758b352f3f8"

echo "🧪 测试开发者工具示例代码"
echo "Base URL: $BASE_URL"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试计数器
total_tests=0
passed_tests=0

# 测试函数
test_example() {
    local test_name=$1
    local endpoint=$2
    local data=$3
    local output_file=$4
    
    total_tests=$((total_tests + 1))
    echo -e "\n${BLUE}🔍 测试 $total_tests: $test_name${NC}"
    echo "端点: $endpoint"
    echo "数据: $data"
    
    local response=$(curl -s -w "\n%{http_code}\n%{time_total}\n%{size_download}" \
        -X POST "$BASE_URL/$endpoint" \
        -H "Authorization: Bearer $API_KEY" \
        -H "Content-Type: application/json" \
        -d "$data" \
        --output "$output_file")
    
    local status_code=$(echo "$response" | sed -n '2p')
    local time_total=$(echo "$response" | sed -n '3p')
    local size_download=$(echo "$response" | sed -n '4p')
    
    if [ "$status_code" = "200" ]; then
        echo -e "${GREEN}✅ 请求成功${NC}"
        echo "   状态码: $status_code"
        echo "   响应时间: ${time_total}s"
        echo "   响应大小: $size_download bytes"
        
        # 检查文件类型
        if file "$output_file" | grep -q "Audio\|MPEG\|MP3"; then
            echo "   文件类型: 有效的音频文件"
            passed_tests=$((passed_tests + 1))
            return 0
        else
            echo -e "${RED}   ❌ 生成的文件不是有效的音频文件${NC}"
            echo "   文件内容: $(head -c 100 "$output_file")"
            return 1
        fi
    else
        echo -e "${RED}❌ 请求失败 (HTTP $status_code)${NC}"
        echo "   响应内容: $(cat "$output_file")"
        return 1
    fi
}

# 清理之前的测试文件
rm -f example_*.mp3

echo -e "\n${BLUE}开始测试开发者工具示例代码...${NC}"

# 1. 测试基本示例（默认模型）
test_example "基本示例 - 默认模型" \
    "tts" \
    '{"text": "你好，欢迎使用 SoulVoice！", "voice": "speech:mcg3fdnx:clzkyf4vy00e5qr6hywum4u84:bzznlkuhcjzpbosexitr", "speed": 1.0}' \
    "example_basic.mp3"

# 2. 测试指定 CosyVoice2 模型
test_example "指定 CosyVoice2 模型" \
    "tts" \
    '{"text": "这是使用 CosyVoice2 模型的测试", "model": "FunAudioLLM/CosyVoice2-0.5B", "voice": "speech:mcg3fdnx:clzkyf4vy00e5qr6hywum4u84:bzznlkuhcjzpbosexitr", "speed": 1.0}' \
    "example_cosyvoice2.mp3"

# 3. 测试指定 MOSS 模型
test_example "指定 MOSS 模型" \
    "tts" \
    '{"text": "这是使用 MOSS 模型的测试", "model": "fnlp/MOSS-TTSD-v0.5", "voice": "speech:mcg3fdnx:clzkyf4vy00e5qr6hywum4u84:bzznlkuhcjzpbosexitr", "speed": 1.0}' \
    "example_moss.mp3"

# 4. 测试完整参数示例
test_example "完整参数示例" \
    "tts" \
    '{"text": "这是一个完整参数的测试示例", "model": "FunAudioLLM/CosyVoice2-0.5B", "voice": "speech:uk8wtvcp:clzkyf4vy00e5qr6hywum4u84:mkrflgylzvfoeppiebis", "speed": 1.2}' \
    "example_full.mp3"

# 5. 测试模型列表 API
echo -e "\n${BLUE}🔍 测试模型列表 API${NC}"
models_response=$(curl -s -w "\n%{http_code}" \
    -X GET "$BASE_URL/models" \
    -H "Authorization: Bearer $API_KEY" \
    --output "example_models.json")

models_status=$(echo "$models_response" | tail -n 1)
if [ "$models_status" = "200" ]; then
    echo -e "${GREEN}✅ 模型列表 API 成功${NC}"
    echo "   响应内容:"
    cat example_models.json | jq . 2>/dev/null || cat example_models.json
    passed_tests=$((passed_tests + 1))
    total_tests=$((total_tests + 1))
else
    echo -e "${RED}❌ 模型列表 API 失败 (HTTP $models_status)${NC}"
    echo "   响应内容: $(cat example_models.json)"
    total_tests=$((total_tests + 1))
fi

# 测试结果总结
echo -e "\n${BLUE}📊 测试结果总结${NC}"
echo "总测试数: $total_tests"
echo "通过测试: $passed_tests"
echo "失败测试: $((total_tests - passed_tests))"

# 显示生成的文件
echo -e "\n${BLUE}📁 生成的文件：${NC}"
for file in example_*.mp3 example_*.json; do
    if [ -f "$file" ]; then
        size=$(ls -lh "$file" | awk '{print $5}')
        if [[ "$file" == *.mp3 ]]; then
            type=$(file "$file" | cut -d: -f2)
            echo "  $file ($size) -$type"
        else
            echo "  $file ($size) - JSON 响应"
        fi
    fi
done

if [ $passed_tests -eq $total_tests ]; then
    echo -e "\n${GREEN}🎉 所有示例代码测试通过！开发者工具示例正确！${NC}"
    
    echo -e "\n${BLUE}📝 验证的示例：${NC}"
    echo "✅ Python 示例代码格式正确"
    echo "✅ JavaScript 示例代码格式正确"
    echo "✅ cURL 示例代码格式正确"
    echo "✅ 模型参数支持正常"
    echo "✅ API 端点地址正确"
    echo "✅ 模型列表 API 正常"
    
    exit 0
else
    echo -e "\n${RED}❌ 部分示例代码测试失败，请检查上述错误信息${NC}"
    exit 1
fi
