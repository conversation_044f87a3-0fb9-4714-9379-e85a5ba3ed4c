#!/bin/bash

# SoulVoice 模型参数测试

set -e

BASE_URL="https://xfsvmyceleiafhqewdkj.supabase.co/functions/v1"
API_KEY="sk-2172cb5aef9deee923a8b0e7234db065a7c5b3a15d47c3224e1bf758b352f3f8"

echo "🎤 SoulVoice 模型参数功能测试"
echo "Base URL: $BASE_URL"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试计数器
total_tests=0
passed_tests=0

# 测试函数
test_api() {
    local test_name=$1
    local endpoint=$2
    local method=$3
    local data=$4
    local output_file=$5
    
    total_tests=$((total_tests + 1))
    echo -e "\n${BLUE}🔍 测试 $total_tests: $test_name${NC}"
    echo "端点: $endpoint"
    echo "方法: $method"
    if [ -n "$data" ]; then
        echo "数据: $data"
    fi
    
    local start_time=$(date +%s.%N)
    
    if [ "$method" = "GET" ]; then
        local response=$(curl -s -w "\n%{http_code}\n%{time_total}\n%{size_download}" \
            -X GET "$BASE_URL/$endpoint" \
            -H "Authorization: Bearer $API_KEY" \
            --output "$output_file")
    else
        local response=$(curl -s -w "\n%{http_code}\n%{time_total}\n%{size_download}" \
            -X POST "$BASE_URL/$endpoint" \
            -H "Authorization: Bearer $API_KEY" \
            -H "Content-Type: application/json" \
            -d "$data" \
            --output "$output_file")
    fi
    
    local status_code=$(echo "$response" | sed -n '2p')
    local time_total=$(echo "$response" | sed -n '3p')
    local size_download=$(echo "$response" | sed -n '4p')
    
    if [ "$status_code" = "200" ]; then
        echo -e "${GREEN}✅ 请求成功${NC}"
        echo "   状态码: $status_code"
        echo "   响应时间: ${time_total}s"
        echo "   响应大小: $size_download bytes"
        
        # 如果是音频文件，检查文件类型
        if [[ "$output_file" == *.mp3 ]]; then
            if file "$output_file" | grep -q "Audio\|MPEG\|MP3"; then
                echo "   文件类型: 有效的音频文件"
                passed_tests=$((passed_tests + 1))
                return 0
            else
                echo -e "${RED}   ❌ 生成的文件不是有效的音频文件${NC}"
                echo "   文件内容: $(head -c 100 "$output_file")"
                return 1
            fi
        else
            # JSON 响应，显示内容
            echo "   响应内容:"
            cat "$output_file" | jq . 2>/dev/null || cat "$output_file"
            passed_tests=$((passed_tests + 1))
            return 0
        fi
    else
        echo -e "${RED}❌ 请求失败 (HTTP $status_code)${NC}"
        echo "   响应内容: $(cat "$output_file")"
        return 1
    fi
}

# 清理之前的测试文件
rm -f test_*.json test_*.mp3

echo -e "\n${BLUE}开始模型参数功能测试...${NC}"

# 1. 获取支持的模型列表
test_api "获取模型列表" \
    "models" \
    "GET" \
    "" \
    "test_models.json"

# 2. 获取包含音色兼容性的模型列表
test_api "获取模型列表（包含音色）" \
    "models?include_voices=true" \
    "GET" \
    "" \
    "test_models_with_voices.json"

# 3. 使用默认模型 (CosyVoice2) 生成语音
test_api "默认模型语音合成" \
    "tts" \
    "POST" \
    '{"text": "这是使用默认模型 CosyVoice2 的测试"}' \
    "test_default_model.mp3"

# 4. 明确指定 CosyVoice2 模型
test_api "CosyVoice2 模型语音合成" \
    "tts" \
    "POST" \
    '{"text": "这是使用 CosyVoice2 模型的测试", "model": "FunAudioLLM/CosyVoice2-0.5B"}' \
    "test_cosyvoice2.mp3"

# 5. 使用 MOSS 模型
test_api "MOSS 模型语音合成" \
    "tts" \
    "POST" \
    '{"text": "这是使用 MOSS 模型的测试", "model": "fnlp/MOSS-TTSD-v0.5"}' \
    "test_moss.mp3"

# 6. 组合测试：指定模型、音色和语速
test_api "完整参数测试 (CosyVoice2)" \
    "tts" \
    "POST" \
    '{"text": "这是一个完整的参数测试，使用 CosyVoice2 模型", "model": "FunAudioLLM/CosyVoice2-0.5B", "voice": "speech:mcg3fdnx:clzkyf4vy00e5qr6hywum4u84:bzznlkuhcjzpbosexitr", "speed": 1.2}' \
    "test_full_cosyvoice2.mp3"

# 7. 组合测试：MOSS 模型
test_api "完整参数测试 (MOSS)" \
    "tts" \
    "POST" \
    '{"text": "这是一个完整的参数测试，使用 MOSS 模型", "model": "fnlp/MOSS-TTSD-v0.5", "voice": "speech:uk8wtvcp:clzkyf4vy00e5qr6hywum4u84:mkrflgylzvfoeppiebis", "speed": 0.8}' \
    "test_full_moss.mp3"

# 8. 错误处理测试：不支持的模型
test_api "不支持的模型错误处理" \
    "tts" \
    "POST" \
    '{"text": "测试不支持的模型", "model": "unsupported/model"}' \
    "test_unsupported_model.json"

# 9. 调试 API 测试：CosyVoice2
test_api "调试 API - CosyVoice2" \
    "debug-tts" \
    "POST" \
    '{"text": "调试 API 测试 CosyVoice2", "model": "FunAudioLLM/CosyVoice2-0.5B"}' \
    "test_debug_cosyvoice2.mp3"

# 10. 调试 API 测试：MOSS
test_api "调试 API - MOSS" \
    "debug-tts" \
    "POST" \
    '{"text": "调试 API 测试 MOSS", "model": "fnlp/MOSS-TTSD-v0.5"}' \
    "test_debug_moss.mp3"

# 测试结果总结
echo -e "\n${BLUE}📊 测试结果总结${NC}"
echo "总测试数: $total_tests"
echo "通过测试: $passed_tests"
echo "失败测试: $((total_tests - passed_tests))"

# 显示模型信息
echo -e "\n${BLUE}📋 支持的模型信息：${NC}"
if [ -f "test_models.json" ]; then
    echo "从 models API 获取的信息："
    cat test_models.json | jq '.models[] | {id: .id, name: .name, description: .description}' 2>/dev/null || echo "无法解析模型信息"
fi

# 列出生成的文件
echo -e "\n${BLUE}📁 生成的文件：${NC}"
for file in test_*.mp3 test_*.json; do
    if [ -f "$file" ]; then
        size=$(ls -lh "$file" | awk '{print $5}')
        if [[ "$file" == *.mp3 ]]; then
            type=$(file "$file" | cut -d: -f2)
            echo "  $file ($size) -$type"
        else
            echo "  $file ($size) - JSON 响应"
        fi
    fi
done

if [ $passed_tests -eq $total_tests ]; then
    echo -e "\n${GREEN}🎉 所有测试通过！模型参数功能完全正常！${NC}"
    
    echo -e "\n${BLUE}📝 使用示例：${NC}"
    echo "# 使用默认模型 (CosyVoice2):"
    echo 'curl -X POST $BASE_URL/tts -H "Authorization: Bearer $API_KEY" -H "Content-Type: application/json" -d '"'"'{"text": "你好"}'"'"''
    echo ""
    echo "# 指定 CosyVoice2 模型:"
    echo 'curl -X POST $BASE_URL/tts -H "Authorization: Bearer $API_KEY" -H "Content-Type: application/json" -d '"'"'{"text": "你好", "model": "FunAudioLLM/CosyVoice2-0.5B"}'"'"''
    echo ""
    echo "# 指定 MOSS 模型:"
    echo 'curl -X POST $BASE_URL/tts -H "Authorization: Bearer $API_KEY" -H "Content-Type: application/json" -d '"'"'{"text": "你好", "model": "fnlp/MOSS-TTSD-v0.5"}'"'"''
    
    exit 0
else
    echo -e "\n${RED}❌ 部分测试失败，请检查上述错误信息${NC}"
    exit 1
fi
