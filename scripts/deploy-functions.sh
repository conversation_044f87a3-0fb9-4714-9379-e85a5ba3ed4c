#!/bin/bash

# SoulVoice API Functions 部署脚本

set -e

echo "🚀 开始部署 SoulVoice API Functions..."

# 检查 Supabase CLI 是否安装
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI 未安装，请先安装："
    echo "npm install -g supabase"
    exit 1
fi

# 检查是否已登录
if ! supabase projects list &> /dev/null; then
    echo "❌ 请先登录 Supabase："
    echo "supabase login"
    exit 1
fi

# 检查环境变量
if [ -z "$SUPABASE_PROJECT_ID" ]; then
    echo "❌ 请设置 SUPABASE_PROJECT_ID 环境变量"
    exit 1
fi

echo "📦 链接到 Supabase 项目..."
supabase link --project-ref $SUPABASE_PROJECT_ID

echo "🔧 设置环境变量..."
# 设置 SiliconFlow API Key
if [ -n "$SILICONFLOW_API_KEY" ]; then
    supabase secrets set SILICONFLOW_API_KEY="$SILICONFLOW_API_KEY"
    echo "✅ SILICONFLOW_API_KEY 已设置"
else
    echo "⚠️  SILICONFLOW_API_KEY 未设置，将使用默认值"
fi

echo "🚀 部署 Edge Functions..."

# 部署 TTS API
echo "📤 部署 TTS API..."
supabase functions deploy tts --no-verify-jwt

# 部署 Voices API
echo "📤 部署 Voices API..."
supabase functions deploy voices --no-verify-jwt

# 部署 Usage API
echo "📤 部署 Usage API..."
supabase functions deploy usage --no-verify-jwt

# 部署 Health Check API
echo "📤 部署 Health Check API..."
supabase functions deploy health --no-verify-jwt

echo "✅ 所有 Functions 部署完成！"

# 获取 Function URLs
echo ""
echo "🔗 API 端点："
echo "TTS API: https://$SUPABASE_PROJECT_ID.supabase.co/functions/v1/tts"
echo "Voices API: https://$SUPABASE_PROJECT_ID.supabase.co/functions/v1/voices"
echo "Usage API: https://$SUPABASE_PROJECT_ID.supabase.co/functions/v1/usage"
echo "Health Check: https://$SUPABASE_PROJECT_ID.supabase.co/functions/v1/health"

echo ""
echo "📚 使用示例："
echo "curl -X POST https://$SUPABASE_PROJECT_ID.supabase.co/functions/v1/tts \\"
echo "  -H 'Authorization: Bearer YOUR_API_KEY' \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"text\": \"你好，SoulVoice！\", \"voice\": \"zh-CN-XiaoxiaoNeural\"}'"

echo ""
echo "🎉 部署完成！请更新前端配置中的 API 基础 URL。"
