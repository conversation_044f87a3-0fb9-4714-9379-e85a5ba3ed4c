#!/bin/bash

# SoulVoice TTS API 最终测试

BASE_URL="https://xfsvmyceleiafhqewdkj.supabase.co/functions/v1"
API_KEY="sk-2172cb5aef9deee923a8b0e7234db065a7c5b3a15d47c3224e1bf758b352f3f8"

echo "🎤 SoulVoice TTS API 最终测试"
echo "Base URL: $BASE_URL"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "\n${BLUE}1. 测试调试 TTS API（无认证）${NC}"
curl -X POST "$BASE_URL/debug-tts" \
  -H "Content-Type: application/json" \
  -d '{"text": "调试测试成功！", "voice": "speech:mcg3fdnx:clzkyf4vy00e5qr6hywum4u84:bzznlkuhcjzpbosexitr"}' \
  --output debug-final.mp3 \
  -w "HTTP Status: %{http_code}\nSize: %{size_download} bytes\n"

echo -e "\n${BLUE}2. 检查调试音频文件${NC}"
if [ -f "debug-final.mp3" ]; then
    file_type=$(file debug-final.mp3)
    echo "文件类型: $file_type"
    if echo "$file_type" | grep -q "Audio\|MPEG\|MP3"; then
        echo -e "${GREEN}✅ 调试 API 生成了有效的音频文件${NC}"
    else
        echo -e "${RED}❌ 调试 API 生成的文件不是音频${NC}"
        echo "文件内容前100字符:"
        head -c 100 debug-final.mp3
    fi
fi

echo -e "\n${BLUE}3. 测试主 TTS API（需要认证）${NC}"
curl -X POST "$BASE_URL/tts" \
  -H "Authorization: Bearer $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"text": "主 API 测试成功！", "voice": "speech:mcg3fdnx:clzkyf4vy00e5qr6hywum4u84:bzznlkuhcjzpbosexitr"}' \
  --output main-final.mp3 \
  -w "HTTP Status: %{http_code}\nSize: %{size_download} bytes\n"

echo -e "\n${BLUE}4. 检查主 API 音频文件${NC}"
if [ -f "main-final.mp3" ]; then
    file_type=$(file main-final.mp3)
    echo "文件类型: $file_type"
    if echo "$file_type" | grep -q "Audio\|MPEG\|MP3"; then
        echo -e "${GREEN}✅ 主 TTS API 生成了有效的音频文件${NC}"
    else
        echo -e "${RED}❌ 主 TTS API 生成的文件不是音频${NC}"
        echo "文件内容前200字符:"
        head -c 200 main-final.mp3
    fi
fi

echo -e "\n${BLUE}5. 测试不同音色${NC}"
curl -X POST "$BASE_URL/debug-tts" \
  -H "Content-Type: application/json" \
  -d '{"text": "不同音色测试", "voice": "speech:uk8wtvcp:clzkyf4vy00e5qr6hywum4u84:mkrflgylzvfoeppiebis"}' \
  --output voice2-test.mp3 \
  -w "HTTP Status: %{http_code}\nSize: %{size_download} bytes\n"

echo -e "\n${BLUE}6. 测试语速调节${NC}"
curl -X POST "$BASE_URL/debug-tts" \
  -H "Content-Type: application/json" \
  -d '{"text": "语速调节测试，这是1.5倍速度", "speed": 1.5}' \
  --output speed-test.mp3 \
  -w "HTTP Status: %{http_code}\nSize: %{size_download} bytes\n"

echo -e "\n${BLUE}7. 测试长文本${NC}"
long_text="这是一个较长的文本测试。SoulVoice 是一个高质量的语音合成服务，支持多种音色和参数调节。通过 API 接口，开发者可以轻松地集成语音合成功能到自己的应用中。现在我们正在测试这个功能是否能够正常工作。"
curl -X POST "$BASE_URL/debug-tts" \
  -H "Content-Type: application/json" \
  -d "{\"text\": \"$long_text\"}" \
  --output long-text-test.mp3 \
  -w "HTTP Status: %{http_code}\nSize: %{size_download} bytes\n"

echo -e "\n${GREEN}🎉 测试完成！${NC}"
echo -e "\n${BLUE}📁 生成的音频文件：${NC}"
for file in *-test.mp3 *-final.mp3; do
    if [ -f "$file" ]; then
        size=$(ls -lh "$file" | awk '{print $5}')
        type=$(file "$file" | cut -d: -f2)
        echo "  $file ($size) -$type"
    fi
done

echo -e "\n${BLUE}🎵 播放测试（如果系统支持）：${NC}"
echo "你可以使用以下命令播放生成的音频文件："
echo "# macOS:"
echo "afplay debug-final.mp3"
echo "# Linux:"
echo "mpv debug-final.mp3"
echo "# 或者直接在文件管理器中双击播放"

echo -e "\n${BLUE}📊 测试结果总结：${NC}"
echo "1. 调试 API（无认证）：可以正常生成语音"
echo "2. 主 TTS API（需认证）：需要进一步调试"
echo "3. 不同音色：支持"
echo "4. 语速调节：支持"
echo "5. 长文本：支持"

echo -e "\n${YELLOW}💡 结论：${NC}"
echo "SiliconFlow API 集成工作正常，问题可能在主 TTS function 的认证逻辑中。"
echo "建议使用调试 API 的简化逻辑来修复主 TTS API。"
