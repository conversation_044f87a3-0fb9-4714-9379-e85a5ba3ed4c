#!/bin/bash

# ===========================================
# SoulVoice Vercel 部署脚本
# ===========================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必需的工具
check_requirements() {
    log_info "检查部署环境..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装，请先安装 npm"
        exit 1
    fi
    
    if ! command -v vercel &> /dev/null; then
        log_warning "Vercel CLI 未安装，正在安装..."
        npm install -g vercel
    fi
    
    log_success "环境检查完成"
}

# 检查环境变量
check_env_vars() {
    log_info "检查环境变量配置..."
    
    if [ ! -f ".env.local" ]; then
        log_warning ".env.local 文件不存在，请根据 .env.example 创建"
        log_info "复制环境变量模板..."
        cp .env.example .env.local
        log_warning "请编辑 .env.local 文件并填入正确的环境变量值"
        read -p "按 Enter 键继续..."
    fi
    
    log_success "环境变量检查完成"
}

# 构建项目
build_project() {
    log_info "安装依赖..."
    npm install
    
    log_info "构建项目..."
    npm run build
    
    if [ ! -d "dist" ]; then
        log_error "构建失败，dist 目录不存在"
        exit 1
    fi
    
    log_success "项目构建完成"
}

# 部署到 Vercel
deploy_to_vercel() {
    log_info "开始部署到 Vercel..."
    
    # 检查是否已登录 Vercel
    if ! vercel whoami &> /dev/null; then
        log_info "请登录 Vercel..."
        vercel login
    fi
    
    # 部署项目
    if [ "$1" = "--prod" ]; then
        log_info "部署到生产环境..."
        vercel --prod
    else
        log_info "部署到预览环境..."
        vercel
    fi
    
    log_success "部署完成！"
}

# 主函数
main() {
    echo "========================================"
    echo "🚀 SoulVoice Vercel 部署脚本"
    echo "========================================"
    
    check_requirements
    check_env_vars
    build_project
    deploy_to_vercel $1
    
    echo "========================================"
    log_success "🎉 部署流程完成！"
    echo "========================================"
    
    log_info "接下来的步骤："
    echo "1. 在 Vercel Dashboard 中配置环境变量"
    echo "2. 检查部署状态和日志"
    echo "3. 测试应用功能"
    echo "4. 配置自定义域名（可选）"
}

# 显示帮助信息
show_help() {
    echo "SoulVoice Vercel 部署脚本"
    echo ""
    echo "用法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --prod    部署到生产环境"
    echo "  --help    显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0          # 部署到预览环境"
    echo "  $0 --prod   # 部署到生产环境"
}

# 解析命令行参数
case "$1" in
    --help)
        show_help
        exit 0
        ;;
    --prod)
        main --prod
        ;;
    "")
        main
        ;;
    *)
        log_error "未知选项: $1"
        show_help
        exit 1
        ;;
esac
