#!/bin/bash

# SoulVoice API 部署验证脚本

set -e

BASE_URL="https://xfsvmyceleiafhqewdkj.supabase.co/functions/v1"

echo "🚀 SoulVoice API 部署验证"
echo "Base URL: $BASE_URL"
echo "Project ID: xfsvmyceleiafhqewdkj"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试计数器
total_tests=0
passed_tests=0

# 测试函数
test_endpoint() {
    local name=$1
    local method=$2
    local endpoint=$3
    local expected_status=$4
    local expected_content=$5
    local data=$6
    
    total_tests=$((total_tests + 1))
    echo -e "\n${BLUE}🔍 测试 $total_tests: $name${NC}"
    
    local headers=()
    if [ "$method" = "POST" ] && [ -n "$data" ]; then
        headers+=("-H" "Content-Type: application/json")
        headers+=("-d" "$data")
    fi
    
    local response
    local status_code
    local body

    if [ ${#headers[@]} -gt 0 ]; then
        response=$(curl -s -w "\n%{http_code}" -X "$method" "${headers[@]}" "$BASE_URL$endpoint")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" "$BASE_URL$endpoint")
    fi

    status_code=$(echo "$response" | tail -n 1)
    body=$(echo "$response" | head -n -1)
    
    # 检查状态码
    if [ "$status_code" = "$expected_status" ]; then
        # 检查响应内容
        if echo "$body" | grep -q "$expected_content"; then
            echo -e "${GREEN}✅ 通过 (HTTP $status_code)${NC}"
            passed_tests=$((passed_tests + 1))
            return 0
        else
            echo -e "${RED}❌ 失败 - 响应内容不匹配${NC}"
            echo "Expected: $expected_content"
            echo "Got: $body"
            return 1
        fi
    else
        echo -e "${RED}❌ 失败 - 状态码不匹配${NC}"
        echo "Expected: $expected_status, Got: $status_code"
        echo "Response: $body"
        return 1
    fi
}

echo -e "\n${BLUE}开始部署验证测试...${NC}"

# 1. Health Check
test_endpoint "Health Check" "GET" "/health" "200" '"status":"healthy"'

# 2. Functions List Check
echo -e "\n${BLUE}🔍 检查已部署的 Functions${NC}"
functions_output=$(supabase functions list 2>/dev/null || echo "CLI command failed")
if echo "$functions_output" | grep -q "health.*ACTIVE"; then
    echo -e "${GREEN}✅ health function 已部署${NC}"
    passed_tests=$((passed_tests + 1))
else
    echo -e "${RED}❌ health function 未正确部署${NC}"
fi
total_tests=$((total_tests + 1))

if echo "$functions_output" | grep -q "tts.*ACTIVE"; then
    echo -e "${GREEN}✅ tts function 已部署${NC}"
    passed_tests=$((passed_tests + 1))
else
    echo -e "${RED}❌ tts function 未正确部署${NC}"
fi
total_tests=$((total_tests + 1))

if echo "$functions_output" | grep -q "voices.*ACTIVE"; then
    echo -e "${GREEN}✅ voices function 已部署${NC}"
    passed_tests=$((passed_tests + 1))
else
    echo -e "${RED}❌ voices function 未正确部署${NC}"
fi
total_tests=$((total_tests + 1))

if echo "$functions_output" | grep -q "usage.*ACTIVE"; then
    echo -e "${GREEN}✅ usage function 已部署${NC}"
    passed_tests=$((passed_tests + 1))
else
    echo -e "${RED}❌ usage function 未正确部署${NC}"
fi
total_tests=$((total_tests + 1))

# 3. API 端点认证测试
test_endpoint "TTS 认证检查" "POST" "/tts" "401" '"code":"UNAUTHORIZED"' '{"text":"test"}'
test_endpoint "Voices 认证检查" "GET" "/voices" "401" '"code":"UNAUTHORIZED"'
test_endpoint "Usage 认证检查" "GET" "/usage" "401" '"code":"UNAUTHORIZED"'

# 4. 错误处理测试
test_endpoint "TTS 无效内容类型" "POST" "/tts" "400" '"code":"INVALID_CONTENT_TYPE"'
test_endpoint "TTS 无效 JSON" "POST" "/tts" "400" '"code":"INVALID_JSON"' 'invalid-json'

# 5. CORS 测试
echo -e "\n${BLUE}🔍 测试 CORS 支持${NC}"
cors_response=$(curl -s -X OPTIONS "$BASE_URL/health" -H "Origin: https://example.com" -w "%{http_code}")
if echo "$cors_response" | grep -q "200"; then
    echo -e "${GREEN}✅ CORS 预检请求正常${NC}"
    passed_tests=$((passed_tests + 1))
else
    echo -e "${RED}❌ CORS 预检请求失败${NC}"
fi
total_tests=$((total_tests + 1))

# 6. 环境变量检查
echo -e "\n${BLUE}🔍 检查环境变量${NC}"
secrets_output=$(supabase secrets list 2>/dev/null || echo "CLI command failed")
if echo "$secrets_output" | grep -q "SILICONFLOW_API_KEY"; then
    echo -e "${GREEN}✅ SILICONFLOW_API_KEY 已设置${NC}"
    passed_tests=$((passed_tests + 1))
else
    echo -e "${RED}❌ SILICONFLOW_API_KEY 未设置${NC}"
fi
total_tests=$((total_tests + 1))

# 测试结果总结
echo -e "\n${BLUE}📊 测试结果总结${NC}"
echo "总测试数: $total_tests"
echo "通过测试: $passed_tests"
echo "失败测试: $((total_tests - passed_tests))"

if [ $passed_tests -eq $total_tests ]; then
    echo -e "\n${GREEN}🎉 所有测试通过！API 部署成功！${NC}"
    
    echo -e "\n${BLUE}📋 API 端点信息：${NC}"
    echo "Health Check: $BASE_URL/health"
    echo "TTS API: $BASE_URL/tts"
    echo "Voices API: $BASE_URL/voices"
    echo "Usage API: $BASE_URL/usage"
    
    echo -e "\n${BLUE}📚 使用示例：${NC}"
    echo "# 健康检查"
    echo "curl $BASE_URL/health"
    echo ""
    echo "# 语音合成（需要 API 密钥）"
    echo "curl -X POST $BASE_URL/tts \\"
    echo "  -H 'Authorization: Bearer YOUR_API_KEY' \\"
    echo "  -H 'Content-Type: application/json' \\"
    echo "  -d '{\"text\": \"你好，SoulVoice！\", \"voice\": \"zh-CN-XiaoxiaoNeural\"}' \\"
    echo "  --output speech.mp3"
    
    exit 0
else
    echo -e "\n${RED}❌ 部分测试失败，请检查上述错误信息${NC}"
    exit 1
fi
