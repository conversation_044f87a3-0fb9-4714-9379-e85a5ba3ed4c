#!/bin/bash

# SoulVoice API 测试脚本

set -e

# 配置
BASE_URL="${API_BASE_URL:-http://localhost:54321/functions/v1}"
API_KEY="${TEST_API_KEY:-}"

echo "🧪 SoulVoice API 测试"
echo "Base URL: $BASE_URL"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试函数
test_endpoint() {
    local method=$1
    local endpoint=$2
    local description=$3
    local data=$4
    local auth_required=${5:-true}
    
    echo -e "\n${BLUE}🔍 测试: $description${NC}"
    echo "Endpoint: $method $endpoint"
    
    local headers=()
    if [ "$auth_required" = true ] && [ -n "$API_KEY" ]; then
        headers+=("-H" "Authorization: Bearer $API_KEY")
    fi
    
    if [ "$method" = "POST" ] && [ -n "$data" ]; then
        headers+=("-H" "Content-Type: application/json")
        headers+=("-d" "$data")
    fi
    
    local response
    local status_code
    
    if [ ${#headers[@]} -gt 0 ]; then
        response=$(curl -s -w "\n%{http_code}" -X "$method" "${headers[@]}" "$BASE_URL$endpoint")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" "$BASE_URL$endpoint")
    fi
    
    status_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    if [ "$status_code" -ge 200 ] && [ "$status_code" -lt 300 ]; then
        echo -e "${GREEN}✅ 成功 (HTTP $status_code)${NC}"
        if [ "$endpoint" = "/health" ]; then
            echo "$body" | jq '.' 2>/dev/null || echo "$body"
        else
            echo "$body" | jq '.voices[0:2] // .current_month // .' 2>/dev/null || echo "Response received"
        fi
    elif [ "$status_code" -eq 401 ] && [ "$auth_required" = true ] && [ -z "$API_KEY" ]; then
        echo -e "${YELLOW}⚠️  需要 API 密钥 (HTTP $status_code)${NC}"
    else
        echo -e "${RED}❌ 失败 (HTTP $status_code)${NC}"
        echo "$body" | jq '.' 2>/dev/null || echo "$body"
    fi
}

echo -e "\n${BLUE}开始 API 测试...${NC}"

# 1. 健康检查（无需认证）
test_endpoint "GET" "/health" "健康检查" "" false

# 检查是否提供了 API 密钥
if [ -z "$API_KEY" ]; then
    echo -e "\n${YELLOW}⚠️  未提供 API 密钥，跳过需要认证的测试${NC}"
    echo "请设置 TEST_API_KEY 环境变量来运行完整测试："
    echo "export TEST_API_KEY=sk-your-api-key"
    echo "./scripts/test-api.sh"
    exit 0
fi

# 2. 获取音色列表
test_endpoint "GET" "/voices" "获取音色列表"

# 3. 语音合成测试
test_endpoint "POST" "/tts" "语音合成 - 基本测试" '{
    "text": "你好，欢迎使用 SoulVoice！",
    "voice": "zh-CN-XiaoxiaoNeural"
}'

# 4. 语音合成 - 完整参数
test_endpoint "POST" "/tts" "语音合成 - 完整参数" '{
    "text": "这是一个完整参数的测试。",
    "voice": "zh-CN-YunxiNeural",
    "model": "FunAudioLLM/CosyVoice2-0.5B",
    "speed": 1.2,
    "response_format": "mp3",
    "sample_rate": 22050
}'

# 5. 获取用量统计
test_endpoint "GET" "/usage" "获取用量统计"

# 6. 错误测试 - 空文本
test_endpoint "POST" "/tts" "错误测试 - 空文本" '{
    "text": "",
    "voice": "zh-CN-XiaoxiaoNeural"
}'

# 7. 错误测试 - 文本过长
long_text=$(printf 'a%.0s' {1..5001})
test_endpoint "POST" "/tts" "错误测试 - 文本过长" "{
    \"text\": \"$long_text\",
    \"voice\": \"zh-CN-XiaoxiaoNeural\"
}"

# 8. 错误测试 - 无效音色
test_endpoint "POST" "/tts" "错误测试 - 无效参数" '{
    "text": "测试无效参数",
    "speed": 5.0
}'

echo -e "\n${GREEN}🎉 API 测试完成！${NC}"

# 性能测试（可选）
if [ "$1" = "--performance" ]; then
    echo -e "\n${BLUE}🚀 开始性能测试...${NC}"
    
    echo "测试并发请求..."
    for i in {1..5}; do
        (
            test_endpoint "POST" "/tts" "并发测试 #$i" '{
                "text": "并发测试消息 '$i'",
                "voice": "zh-CN-XiaoxiaoNeural"
            }' &
        )
    done
    wait
    
    echo -e "\n${GREEN}✅ 性能测试完成${NC}"
fi

echo -e "\n${BLUE}📊 测试总结：${NC}"
echo "- 健康检查：可用"
if [ -n "$API_KEY" ]; then
    echo "- 认证：已测试"
    echo "- 核心功能：已测试"
    echo "- 错误处理：已测试"
else
    echo "- 认证：未测试（需要 API 密钥）"
fi
