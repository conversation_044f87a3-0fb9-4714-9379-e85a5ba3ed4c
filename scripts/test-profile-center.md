# 个人中心功能测试指南

## 测试环境
- 开发服务器：http://localhost:5173
- 数据库：Supabase (SoulVoice 项目)
- 存储：Supabase Storage (avatars bucket)

## 测试步骤

### 1. 基础访问测试

#### 1.1 未登录状态
1. 访问 http://localhost:5173
2. 确认显示登录页面
3. 尝试直接访问 http://localhost:5173/profile
4. 应该被重定向到登录页面

#### 1.2 登录后访问
1. 使用有效账户登录
2. 在侧边栏查看是否有"个人中心"菜单项
3. 点击"个人中心"菜单项
4. 确认成功跳转到个人中心页面

### 2. 个人信息展示测试

#### 2.1 基本信息显示
- [ ] 用户头像正确显示（有头像时显示图片，无头像时显示首字母）
- [ ] 用户姓名正确显示
- [ ] 邮箱地址正确显示
- [ ] 公司名称正确显示（如果有）
- [ ] 注册时间正确显示
- [ ] 账户状态正确显示（普通用户/管理员）

#### 2.2 统计信息显示
- [ ] API 调用次数显示（当前为 0）
- [ ] 语音模型数量显示（当前为 0）

### 3. 个人信息编辑测试

#### 3.1 打开编辑界面
1. 点击"编辑"按钮
2. 确认弹出编辑模态框
3. 检查表单字段是否正确填充当前信息

#### 3.2 表单验证测试
1. **姓名验证**：
   - [ ] 清空姓名字段，确认显示错误提示
   - [ ] 输入单个字符，确认显示长度错误
   - [ ] 输入超过50个字符，确认显示长度错误
   
2. **公司名称验证**：
   - [ ] 输入超过100个字符，确认显示长度错误

#### 3.3 保存功能测试
1. 修改姓名和公司信息
2. 点击"保存更改"
3. 确认显示保存状态
4. 确认页面刷新后信息已更新

#### 3.4 取消功能测试
1. 修改信息但不保存
2. 点击"取消"
3. 如果有未保存更改，确认显示确认对话框
4. 确认取消后信息未被修改

### 4. 头像上传测试

#### 4.1 文件选择测试
1. 点击头像上的相机图标
2. 选择有效图片文件（JPG/PNG）
3. 确认显示预览界面

#### 4.2 文件验证测试
1. **文件类型验证**：
   - [ ] 尝试上传非图片文件，确认显示错误提示
   
2. **文件大小验证**：
   - [ ] 尝试上传超过5MB的文件，确认显示错误提示

#### 4.3 上传功能测试
1. 选择有效图片文件
2. 点击"确认上传"
3. 确认显示上传进度
4. 确认上传成功后页面刷新
5. 确认新头像正确显示

### 5. 响应式设计测试

#### 5.1 桌面端测试
- [ ] 在大屏幕上布局正确
- [ ] 侧边栏正常显示
- [ ] 个人中心页面布局合理

#### 5.2 移动端测试
1. 调整浏览器窗口到移动端尺寸
2. 确认页面布局适配移动端
3. 确认头像大小适配
4. 确认编辑模态框在移动端正常显示

### 6. 错误处理测试

#### 6.1 网络错误测试
1. 断开网络连接
2. 尝试保存个人信息
3. 确认显示适当的错误提示

#### 6.2 权限错误测试
1. 尝试访问其他用户的资料（如果可能）
2. 确认受到适当的权限限制

### 7. 集成测试

#### 7.1 与认证系统集成
- [ ] 登录状态正确同步
- [ ] 登出后无法访问个人中心
- [ ] 用户信息与认证系统一致

#### 7.2 与数据库集成
- [ ] 个人信息正确保存到数据库
- [ ] 头像URL正确保存到数据库
- [ ] 数据更新实时生效

#### 7.3 与存储系统集成
- [ ] 头像文件正确上传到 Supabase Storage
- [ ] 旧头像文件正确删除
- [ ] 头像URL可正常访问

## 测试结果记录

### 通过的测试项
- [ ] 基础访问功能
- [ ] 个人信息展示
- [ ] 信息编辑功能
- [ ] 头像上传功能
- [ ] 响应式设计
- [ ] 错误处理
- [ ] 系统集成

### 发现的问题
1. 
2. 
3. 

### 需要优化的地方
1. 
2. 
3. 

## 测试完成确认

- [ ] 所有核心功能正常工作
- [ ] 用户体验良好
- [ ] 响应式设计正确
- [ ] 错误处理完善
- [ ] 与现有系统集成良好

**测试人员**: ___________
**测试日期**: ___________
**测试版本**: v1.0.0
