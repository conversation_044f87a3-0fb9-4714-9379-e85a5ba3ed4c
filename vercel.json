{"rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}, {"source": "/((?!api/).*)", "destination": "/index.html"}], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}, {"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}], "env": {"VITE_SUPABASE_URL": "@vite_supabase_url", "VITE_SUPABASE_ANON_KEY": "@vite_supabase_anon_key", "VITE_API_BASE_URL": "@vite_api_base_url", "VITE_SILICONFLOW_API_KEY": "@vite_siliconflow_api_key", "VITE_WECHAT_PAY_MERCHANT_ID": "@vite_wechat_pay_merchant_id", "VITE_WECHAT_PAY_APP_ID": "@vite_wechat_pay_app_id"}, "build": {"env": {"NODE_ENV": "production"}}, "regions": ["hkg1", "sin1"], "cleanUrls": true, "trailingSlash": false}