# 🎵 SoulVoice

<div align="center">

![SoulVoice Logo](./public/logo-192x192.png)

**赋予每个应用说话的灵魂**

[![React](https://img.shields.io/badge/React-18.3.1-61dafb?style=for-the-badge&logo=react)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.5.3-3178c6?style=for-the-badge&logo=typescript)](https://www.typescriptlang.org/)
[![Vite](https://img.shields.io/badge/Vite-5.4.2-646cff?style=for-the-badge&logo=vite)](https://vitejs.dev/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-3.4.1-38bdf8?style=for-the-badge&logo=tailwind-css)](https://tailwindcss.com/)
[![Supabase](https://img.shields.io/badge/Supabase-2.52.0-3ecf8e?style=for-the-badge&logo=supabase)](https://supabase.com/)

[🚀 在线演示](https://soulvoice.demo.com) • [📖 文档](https://docs.soulvoice.com) • [🐛 报告问题](https://github.com/your-username/soulvoice/issues)

</div>

## 📖 项目简介

SoulVoice 是一个现代化的语音合成与克隆平台，提供业界最先进的语音技术，让开发者能够轻松为应用添加高质量的语音功能。通过简洁的 API 和直观的用户界面，SoulVoice 让语音技术的集成变得前所未有的简单。

### ✨ 核心特性

- 🎯 **极致易用** - 一行代码即可集成，3分钟完成部署
- 🚀 **高性能** - 毫秒级响应，支持大规模并发
- 🎨 **多样化音色** - 内置多种专业音色，支持自定义语音克隆
- 🔒 **企业级安全** - 完善的认证体系和数据保护
- 📊 **实时监控** - 详细的用量统计和性能分析
- 🌐 **多平台支持** - REST API、SDK 和 Webhook 全覆盖

## 🛠️ 技术栈

### 前端技术
- **React 18** - 现代化的用户界面框架
- **TypeScript** - 类型安全的 JavaScript 超集
- **Vite** - 极速的前端构建工具
- **Tailwind CSS** - 实用优先的 CSS 框架
- **Framer Motion** - 流畅的动画库
- **React Router** - 声明式路由管理

### 后端服务
- **Supabase** - 开源的 Firebase 替代方案
- **PostgreSQL** - 可靠的关系型数据库
- **Row Level Security** - 数据库级别的安全控制

### 开发工具
- **ESLint** - 代码质量检查
- **PostCSS** - CSS 后处理器
- **Autoprefixer** - CSS 前缀自动添加

## 🚀 快速开始

### 环境要求

- Node.js >= 18.0.0
- npm >= 8.0.0 或 yarn >= 1.22.0

### 安装步骤

1. **克隆项目**
   ```bash
   git clone https://github.com/your-username/soulvoice.git
   cd soulvoice
   ```

2. **安装依赖**
   ```bash
   npm install
   # 或
   yarn install
   ```

3. **环境配置**
   ```bash
   cp .env.example .env.local
   ```
   
   编辑 `.env.local` 文件，配置必要的环境变量：
   ```env
   VITE_SUPABASE_URL=your_supabase_url
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   VITE_API_BASE_URL=your_api_base_url
   ```

4. **启动开发服务器**
   ```bash
   npm run dev
   # 或
   yarn dev
   ```

5. **访问应用**
   
   打开浏览器访问 `http://localhost:5173`

## 📁 项目结构

```
soulvoice/
├── public/                 # 静态资源
├── src/
│   ├── components/         # 可复用组件
│   │   ├── animations/     # 动画组件
│   │   ├── auth/          # 认证相关组件
│   │   ├── layout/        # 布局组件
│   │   └── ui/            # UI 基础组件
│   ├── contexts/          # React 上下文
│   ├── lib/               # 第三方库配置
│   ├── pages/             # 页面组件
│   ├── services/          # API 服务层
│   ├── utils/             # 工具函数
│   └── types/             # TypeScript 类型定义
├── docs/                  # 项目文档
├── supabase/              # Supabase 配置
└── tests/                 # 测试文件
```

## 🎯 核心功能

### 🎤 语音合成 (TTS)
- 支持多种高质量音色
- 实时语音生成
- 情感和语速控制
- 批量文本处理

### 🔄 语音克隆
- 个性化音色训练
- 快速音色适配
- 音色管理和版本控制

### 👤 用户管理
- 安全的身份认证
- 多种登录方式
- 用户资料管理

### 🔑 API 管理
- API 密钥生成和管理
- 请求频率限制
- 安全访问控制

### 📊 数据分析
- 实时用量统计
- 费用计算和预算控制
- 性能监控和报告

## 🔧 API 使用示例

### 基础语音合成

```typescript
import { TTSService } from './services/ttsService';

// 生成语音
const result = await TTSService.generateSpeech({
  model: 'fnlp/MOSS-TTSD-v0.5',
  input: '你好，欢迎使用 SoulVoice！',
  voice: 'zh-CN-XiaoxiaoNeural',
  speed: 1.0,
  emotion: 'natural'
});

if (result.success) {
  // 播放音频
  const audioUrl = TTSService.createAudioUrl(result.audioData);
  const audio = new Audio(audioUrl);
  audio.play();

  // 查看用量信息
  console.log('字符数:', result.usage?.characters);
  console.log('费用:', result.usage?.cost);
} else {
  console.error('生成失败:', result.error);
}
```

### 语音克隆

```typescript
import { VoiceCloneService } from './services/voiceCloneService';

// 创建语音克隆
const cloneResult = await VoiceCloneService.createVoiceClone({
  name: '我的专属音色',
  description: '基于个人录音的音色克隆',
  audioFiles: [audioFile1, audioFile2], // 音频文件数组
  language: 'zh-CN'
});

if (cloneResult.success) {
  console.log('音色克隆成功:', cloneResult.voiceId);

  // 使用克隆的音色生成语音
  const ttsResult = await TTSService.generateSpeech({
    model: 'custom',
    input: '这是我的专属音色！',
    voice: cloneResult.voiceId
  });
}
```

### REST API 调用

```bash
# 语音合成
curl -X POST "https://api.soulvoice.com/v1/tts" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "你好，SoulVoice！",
    "voice": "zh-CN-XiaoxiaoNeural",
    "emotion": "happy",
    "speed": 1.0
  }'

# 获取支持的音色列表
curl -X GET "https://api.soulvoice.com/v1/voices" \
  -H "Authorization: Bearer YOUR_API_KEY"

# 查询用量统计
curl -X GET "https://api.soulvoice.com/v1/usage" \
  -H "Authorization: Bearer YOUR_API_KEY"
```

### 支持的音色

| 音色 ID | 名称 | 类型 | 适用场景 |
|---------|------|------|----------|
| zh-CN-XiaoxiaoNeural | 温柔知性女声 | 系统内置 | 客服、教育 |
| zh-CN-YunxiNeural | 沉稳磁性男声 | 系统内置 | 播音、解说 |
| zh-CN-XiaoyiNeural | 活泼青春女声 | 系统内置 | 娱乐、互动 |
| zh-CN-YunjianNeural | 专业播音男声 | 系统内置 | 新闻、正式 |
| custom-* | 自定义音色 | 用户克隆 | 个性化应用 |

### 错误处理

```typescript
const result = await TTSService.generateSpeech(request);

if (!result.success) {
  switch (result.error) {
    case '输入文本不能为空':
      // 处理空文本错误
      break;
    case '输入文本长度不能超过 5000 字符':
      // 处理文本过长错误
      break;
    case 'API 密钥无效':
      // 处理认证错误
      break;
    case '用量已超出限制':
      // 处理用量超限错误
      break;
    default:
      console.error('未知错误:', result.error);
  }
}
```

## ⚙️ 配置说明

### 环境变量

创建 `.env.local` 文件并配置以下变量：

```env
# Supabase 配置
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key

# API 配置
VITE_API_BASE_URL=https://api.soulvoice.com
VITE_SILICONFLOW_API_KEY=your-siliconflow-api-key

# 应用配置
VITE_APP_NAME=SoulVoice
VITE_APP_VERSION=1.0.0
VITE_ENABLE_DEBUG=false

# 第三方服务
VITE_GITHUB_CLIENT_ID=your-github-client-id
```

### 数据库设置

1. **创建 Supabase 项目**
   - 访问 [Supabase](https://supabase.com)
   - 创建新项目
   - 获取项目 URL 和 API 密钥

2. **运行数据库迁移**
   ```bash
   npx supabase db push
   ```

3. **设置 Row Level Security (RLS)**
   ```sql
   -- 启用 RLS
   ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
   ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;
   ALTER TABLE usage_records ENABLE ROW LEVEL SECURITY;

   -- 创建安全策略
   CREATE POLICY "Users can view own profile" ON profiles
     FOR SELECT USING (auth.uid() = id);
   ```

## 🚀 部署指南

### 构建生产版本

```bash
npm run build
# 或
yarn build
```

### 部署到 Vercel

1. **安装 Vercel CLI**
   ```bash
   npm i -g vercel
   ```

2. **配置环境变量**
   ```bash
   vercel env add VITE_SUPABASE_URL
   vercel env add VITE_SUPABASE_ANON_KEY
   ```

3. **部署项目**
   ```bash
   vercel --prod
   ```

### 部署到 Netlify

1. **构建项目**
   ```bash
   npm run build
   ```

2. **配置 Netlify**
   ```toml
   # netlify.toml
   [build]
     publish = "dist"
     command = "npm run build"

   [[redirects]]
     from = "/*"
     to = "/index.html"
     status = 200
   ```

3. **设置环境变量**
   在 Netlify 控制台中配置环境变量

### Docker 部署

```dockerfile
# Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

```bash
# 构建和运行
docker build -t soulvoice .
docker run -p 80:80 soulvoice
```

### 使用 Docker Compose

```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "80:80"
    environment:
      - VITE_SUPABASE_URL=${VITE_SUPABASE_URL}
      - VITE_SUPABASE_ANON_KEY=${VITE_SUPABASE_ANON_KEY}
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "443:443"
    volumes:
      - ./ssl:/etc/nginx/ssl
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - app
```

## 🧪 开发和测试

### 开发环境设置

1. **安装开发依赖**
   ```bash
   npm install --include=dev
   ```

2. **启动开发服务器**
   ```bash
   npm run dev
   ```
   应用将在 `http://localhost:5173` 启动

3. **代码检查**
   ```bash
   npm run lint
   ```

4. **类型检查**
   ```bash
   npx tsc --noEmit
   ```

### 测试

```bash
# 运行所有测试
npm run test

# 运行测试并生成覆盖率报告
npm run test:coverage

# 监听模式运行测试
npm run test:watch
```

### 代码规范

项目使用 ESLint 和 Prettier 确保代码质量：

```bash
# 检查代码规范
npm run lint

# 自动修复代码规范问题
npm run lint:fix

# 格式化代码
npm run format
```

## 🔧 故障排除

### 常见问题

#### 1. 构建失败
```bash
# 清理缓存
rm -rf node_modules package-lock.json
npm install

# 清理 Vite 缓存
rm -rf .vite
```

#### 2. 环境变量未生效
- 确保 `.env.local` 文件存在且格式正确
- 环境变量必须以 `VITE_` 开头
- 重启开发服务器

#### 3. Supabase 连接问题
```typescript
// 检查 Supabase 连接
import { supabase } from './lib/supabase';

const testConnection = async () => {
  const { data, error } = await supabase.auth.getSession();
  if (error) {
    console.error('Supabase 连接失败:', error);
  } else {
    console.log('Supabase 连接成功');
  }
};
```

#### 4. API 调用失败
- 检查 API 密钥是否正确
- 确认网络连接正常
- 查看浏览器控制台错误信息

### 性能优化

#### 1. 代码分割
```typescript
// 使用 React.lazy 进行代码分割
const VoiceLab = React.lazy(() => import('./pages/VoiceLab'));
const Dashboard = React.lazy(() => import('./pages/Dashboard'));
```

#### 2. 图片优化
```typescript
// 使用 WebP 格式和响应式图片
<picture>
  <source srcSet="image.webp" type="image/webp" />
  <img src="image.jpg" alt="描述" loading="lazy" />
</picture>
```

#### 3. 缓存策略
```typescript
// 使用 React Query 进行数据缓存
import { useQuery } from '@tanstack/react-query';

const { data, isLoading } = useQuery({
  queryKey: ['voices'],
  queryFn: () => TTSService.getSupportedVoices(),
  staleTime: 5 * 60 * 1000, // 5分钟
});
```

## 🤝 贡献指南

我们欢迎所有形式的贡献！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解详细信息。

### 开发流程

1. **Fork 项目**
   ```bash
   git clone https://github.com/your-username/soulvoice.git
   ```

2. **创建功能分支**
   ```bash
   git checkout -b feature/amazing-feature
   ```

3. **进行开发**
   - 遵循代码规范
   - 添加必要的测试
   - 更新相关文档

4. **提交更改**
   ```bash
   git add .
   git commit -m "feat: add amazing feature"
   ```

5. **推送到分支**
   ```bash
   git push origin feature/amazing-feature
   ```

6. **创建 Pull Request**
   - 提供清晰的 PR 描述
   - 关联相关的 Issue
   - 等待代码审查

### 提交规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
feat: 新功能
fix: 修复 bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

### 代码审查清单

- [ ] 代码符合项目规范
- [ ] 添加了必要的测试
- [ ] 更新了相关文档
- [ ] 没有引入安全漏洞
- [ ] 性能影响可接受

## 🗺️ 发展路线图

### 已完成 ✅
- [x] 基础语音合成功能
- [x] 用户认证和管理
- [x] API 密钥管理
- [x] 用量统计和计费
- [x] 响应式用户界面
- [x] 多音色支持

### 进行中 🚧
- [ ] 语音克隆功能优化
- [ ] 实时语音流式传输
- [ ] 移动端应用开发
- [ ] 多语言支持

### 计划中 📋
- [ ] 语音情感分析
- [ ] 批量处理 API
- [ ] Webhook 集成
- [ ] 企业级 SSO
- [ ] 音频后处理效果
- [ ] 语音转文字 (STT)

## ❓ 常见问题

### 技术相关

**Q: 支持哪些音频格式？**
A: 目前支持 MP3、WAV 格式输出，输入支持 WAV、MP3、M4A 等常见格式。

**Q: API 有请求频率限制吗？**
A: 是的，免费用户每分钟最多 60 次请求，付费用户根据套餐不同有相应限制。

**Q: 如何获取 API 密钥？**
A: 注册账户后，在控制台的 "API 密钥" 页面可以生成和管理密钥。

### 功能相关

**Q: 语音克隆需要多长时间？**
A: 通常需要 5-15 分钟，取决于音频质量和长度。

**Q: 支持实时语音合成吗？**
A: 是的，我们提供流式 API，可以实现近实时的语音合成。

**Q: 可以商用吗？**
A: 可以，请查看我们的商业许可条款。

### 计费相关

**Q: 如何计费？**
A: 按字符数计费，语音合成每字符 $0.0002，语音克隆每字符 $0.001。

**Q: 有免费额度吗？**
A: 新用户注册即可获得 10,000 字符的免费额度。

## 📊 性能指标

| 指标 | 数值 |
|------|------|
| 平均响应时间 | < 500ms |
| 音频质量 | 48kHz/16bit |
| 支持语言 | 中文、英文 |
| 并发处理 | 1000+ 请求/秒 |
| 可用性 | 99.9% |

## 🔗 相关链接

- [官方网站](https://soulvoice.com)
- [API 文档](https://docs.soulvoice.com)
- [开发者社区](https://community.soulvoice.com)
- [状态页面](https://status.soulvoice.com)
- [博客](https://blog.soulvoice.com)

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

### 核心技术
- [React](https://reactjs.org/) - 现代化的用户界面库
- [TypeScript](https://www.typescriptlang.org/) - 类型安全的 JavaScript
- [Vite](https://vitejs.dev/) - 极速的前端构建工具
- [Supabase](https://supabase.com/) - 开源的后端即服务平台

### UI 和动画
- [Tailwind CSS](https://tailwindcss.com/) - 实用优先的 CSS 框架
- [Framer Motion](https://www.framer.com/motion/) - 强大的动画库
- [Lucide React](https://lucide.dev/) - 美观的图标库
- [Headless UI](https://headlessui.com/) - 无样式的 UI 组件

### 开发工具
- [ESLint](https://eslint.org/) - 代码质量检查工具
- [PostCSS](https://postcss.org/) - CSS 后处理器
- [Autoprefixer](https://autoprefixer.github.io/) - CSS 前缀自动添加

### 特别感谢
- 所有贡献者和社区成员
- 提供反馈和建议的用户
- 开源社区的无私奉献

---

<div align="center">

**[⬆ 回到顶部](#-soulvoice)**

Made with ❤️ by the SoulVoice Team

[![Star History Chart](https://api.star-history.com/svg?repos=your-username/soulvoice&type=Date)](https://star-history.com/#your-username/soulvoice&Date)

</div>
